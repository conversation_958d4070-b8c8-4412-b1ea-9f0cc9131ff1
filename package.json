{"name": "samsonitega", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "build-themes": "devextreme build", "postinstall": "npm run build-themes"}, "dependencies": {"axios": "^1.5.0", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "devextreme": "^23.1.3", "devextreme-vue": "^23.1.3", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "sass": "^1.34.1", "vue": "^3.2.13", "vue-router": "^4.0.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "devextreme-cli": "1.6.0", "devextreme-themebuilder": "^23.1.3", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass-loader": "^10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {"no-unused-vars": "off", "no-undef": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}
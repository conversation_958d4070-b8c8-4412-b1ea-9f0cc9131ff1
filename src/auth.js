import axios from 'axios';
import databaseName from './myFunctions/databaseName';

const defaultUser = {
  userEmail: 'view only',
  userIdNum: 'view only',
  userFirst: 'View',
  userLast: 'Only',
  userPass: 'password',
  userIdName: 'view only',
  userSecurity: "standard",
  userJwtToken: "1",
  avatarUrl: '',
  isAuthenticated: false 
};


export default {
  _user: {...defaultUser, isAuthenticated: false},
  _tempAdmin: null,  // Store temporary admin data

  hasPermission(userSecurity = []) {
    // Skip permission check if no roles required
    if (userSecurity.length === 0) return true;
    
    // Check if user has one of the required roles
    return userSecurity.includes(this._user.userSecurity);
  },

  loggedIn(userSecurity= []) {
     // First check if user is authenticated at all
  if (!this._user || !this._user.isAuthenticated) return false;
  
  // If no specific roles required, any authenticated user is fine
  if (userSecurity.length === 0) {
    return true;
  }
  
  // Otherwise check if user has one of the required roles
   return this.hasPermission(userSecurity);

    // // return !!this._user;
    // console.log(this._user);
    // return this._user && this._user.userSecurity !== "standard";
  },

  defaultLogIn(){
    this._user = {...defaultUser, userSecurity: "view only", isAuthenticated: true};
    console.log("Default login, setting isAuthenticated to false:", this._user);
    return this._user;
  },

  async logIn(email, password) {
    try {
      // let path = databaseName.getPaths();
      // Send request
      let temp = email.split("@");
      //for an email login
      if(typeof temp[1] !== 'undefined'){

        const loginClass = {
          "Username": email.toString(),
          // "Password": password.toString(), //call function
          "Password": databaseName.GeneratePasswordHash(password),
          "loginMethod": "email"
        }
        await axios({
          method: 'POST',
          url: 'api/Auth/Login',
          data: loginClass,
          headers:{
            'Content-Type': 'application/json'
          }
        })
        .then(resp=>{
          // resp.data = JSON.parse(resp.data);

          if(typeof resp.data.data[0].user_id_num == 'undefined'){
            throw "Incorrect email or password";
          }
          else{
            let userEmail = resp.data.data[0].user_email;
            let userIdNum = resp.data.data[0].user_id_num;
            let userFirst = resp.data.data[0].user_first_name;
            let userLast = resp.data.data[0].user_last_name;
            let userPass = password;
            let userIdName = resp.data.data[0].user_id_name;
            let userSecurity = resp.data.data[0].user_security;
            let userJwtToken = resp.data.data[0].user_jwt_token;
            let isAuthenticated = true 
            this._user = {...defaultUser, userEmail, userIdNum, userFirst, userLast, userPass, userIdName, userSecurity, userJwtToken, isAuthenticated};
          }
        })
        .catch(error=>{
          //console.log(error); 
          //this.loadingVisible = false;
          //console.log(error.response);
          if(error.response.status == 500)
          {
            throw "Incorrect username/email or password";
          }
          else if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
            throw "Internal Server Error";
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
            throw "Internal Server Error";
          }
          else{
            //alert('Unknown error\nMake sure that you and the serve have not lost connection');
            throw "Incorrect email or password.";
          }
        });
        return{
          isOk: true,
          data: this._user
        };
      }
      //for a username login
      else{
        const loginClass = {
          "Username": email.toString(),
          // "Password": password.toString(), //call function
          "Password": databaseName.GeneratePasswordHash(password),
          "loginMethod": "username"
        }
        await axios({
          method: 'POST',
          url: 'api/Auth/Login',
          data: loginClass,
          headers:{
            'Content-Type': 'application/json'
          }
        })
        .then(resp=>{
          // resp.data = JSON.parse(resp.data);

          if(typeof resp.data.data[0].user_id_num == 'undefined'){
            throw "Incorrect email or password";
          }
          else{
            let userEmail = resp.data.data[0].user_email;
            let userIdNum = resp.data.data[0].user_id_num;
            let userFirst = resp.data.data[0].user_first_name;
            let userLast = resp.data.data[0].user_last_name;
            let userPass = password;
            let userIdName = resp.data.data[0].user_id_name;
            let userSecurity = resp.data.data[0].user_security;
            let userJwtToken = resp.data.data[0].user_jwt_token;
            let isAuthenticated = true 
            this._user = {...defaultUser, userEmail, userIdNum, userFirst, userLast, userPass, userIdName, userSecurity, userJwtToken, isAuthenticated};
            //console.log(this._user);
          }
        })
        .catch(error=>{
          //this.loadingVisible = false;
          // console.log(error);
          if(error.response.status == 500)
          {
            throw "Incorrect username/email or password";
          }
          else if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
            throw "Internal Server Error";
            //return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
            throw "Internal Server Error";
            //return;
          }
          else{
            //alert('Unknown error\nMake sure that you and the serve have not lost connection');
            throw "Incorrect username or password.";
            //return;
          }
        });
        //console.log(this._user);
        return{
          isOk: true,
          data: this._user
        }
      }
    }
    catch(error) {
      return {
        isOk: false,
        message: error
      };
    }
  },

  async logOut() {
    this._user = {...defaultUser, isAuthenticated: false};
  },

  async getUser() {
    try {
      // Send request

      return {
        isOk: true,
        data: this._user
      };
    }
    catch {
      return {
        isOk: false
      };
    }
  },

  async verifyAdmin(username, password) {
    try {
      const loginClass = {
        "Username": username.toString(),
        "Password": databaseName.GeneratePasswordHash(password),
        "loginMethod": username.includes('@') ? "email" : "username"
      };

      const response = await axios({
        method: 'POST',
        url: 'api/Auth/Login',  // We might want a different endpoint like 'api/Auth/VerifyAdmin'
        data: loginClass,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.data[0].user_security === 'administrator') {
        // Store admin data temporarily
        this._tempAdmin = {
          userSecurity: response.data.data[0].user_security,
          userJwtToken: response.data.data[0].user_jwt_token,
          // Add any other needed admin data
        };
        
        return {
          isOk: true,
          data: this._tempAdmin
        };
      }
      
      return {
        isOk: false,
        message: 'User does not have admin privileges'
      };

    } catch (error) {
      return {
        isOk: false,
        message: error.response?.status === 500 
          ? "Incorrect username/email or password"
          : "Verification failed"
      };
    }
  },

  clearAdminVerification() {
    this._tempAdmin = null;
  },

  getAdminToken() {
    return this._tempAdmin?.userJwtToken;
  },

  hasValidAdminVerification() {
    return !!this._tempAdmin;
  }
};
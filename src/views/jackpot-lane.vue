<template>
  <div>
    <!-- <h2 class="content-block">Hospital Lane</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div style="display: flex; align-items: flex-end; margin-bottom: 25px;">
          <div>
            <strong>Select Date</strong>
            <DxDateBox
              v-model:value="today"
              :min="tenDaysAgo"
              type="date"
              styling-mode="underlined"
              width="150"
            />
          </div>
          <div style="margin-left: 15px;">
            <DxButton 
              text="Search"
              type="default"
              styling-mode="contained"
              @click="getHospitalData"
            />
          </div>
        </div>
        <div>
          <h5 style="margin-top: 0px;"><strong>{{ numberInHospitalLane }}</strong></h5>
          <div class="errorMsgListContainer">
            <div v-for="(errMsg) in errorTypeArray" :key="errMsg.messageType">
              <p class="errorMsgListStyle"><strong>{{ errMsg.messageType }}:</strong> {{ errMsg.count }}</p>
            </div>
          </div>
          <div>
            <DxDataGrid
              :data-source="hospitalDataSource"
              ref="hospGridRef"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :show-column-headers="true"
              :word-wrap-enabled="true"
              width="100%" 
              :column-auto-width="true"
              @editor-preparing="modifySearchPanelStyleJackpotLanePage"
              @exporting="onExporting"
            >
              <DxFilterRow
                :visible="true"
              />
              <DxPaging :page-size="10"/>
              <DxPager
                :show-page-size-selector="true"
                :allowed-page-sizes="[10 , 20, 40]"
                :show-info="true" 
              />
              <DxExport   
                :enabled="true"
              />
              <!-- <DxScrolling mode="virtual" /> -->
              <DxSearchPanel
                :visible="true"
                :width="170"
                placeholder="Filter Results..."
              />
                <DxColumn 
                  data-field="lpn_barcode"
                  caption="LPN"
                  alignment="left"
                />
                <DxColumn 
                  data-field="error_title"
                  caption="Error"
                  alignment="left"
                />
                <DxColumn 
                  data-field="error_details"
                  caption="Error Message"
                  alignment="left"
                />
                  <!-- cell-template="errorMsgLink" -->
                  <!-- <template #errorMsgLink="{ data }">
                    <div class="gridLink" @click="seeFullErrorMsg(data)">{{ parseErrorMessage(data.text) }}</div>
                  </template> -->
                <DxColumn 
                  data-field="error_datetime"
                  caption="Error Date"
                  data-type="datetime"
                  alignment="left"
                  name="hospFirst"
                />
                <!-- <DxColumn 
                  data-field="created_timestamp"
                  caption="Seconds.Milliseconds"
                  v-model:visible="isProgrammerLevel"
                  name="hospSecond"
                  cell-template="isPro"
                />
                  <template #isPro="{data}">
                    <div>{{ transLogSecsAndMilli(data.text) }}</div>
                  </template> -->
                <!-- <DxColumn 
                  data-field="completed_timestamp"
                  caption="Error Completed Date"
                  data-type="datetime"
                  alignment="left"
                  name="hospCompFirst"
                /> -->
                <!-- <DxColumn 
                  data-field="completed_timestamp"
                  caption="Seconds.Milliseconds"
                  v-model:visible="isProgrammerLevel"
                  name="hospCompSecond"
                  cell-template="isProComp"
                />
                  <template #isProComp="{data}">
                    <div>{{ transLogSecsAndMilli(data.text) }}</div>
                  </template> -->
                  <DxColumn 
                        data-field="cleared"
                        caption="Cleared"
                        alignment="center"
                        
                        data-type="boolean" 
                /><!--
                  <template #cleared-status="{ data }">
                    <div :class="clearedStatusColor(data)">{{ getClearedStatus(data) }}</div>
                  </template> -->
            </DxDataGrid>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import { DxDateBox } from 'devextreme-vue/date-box';
import notify from 'devextreme/ui/notify';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxSearchPanel,
  DxPaging,
  DxFilterRow,
  DxPager,
  DxExport,
  //DxScrolling,
} from 'devextreme-vue/data-grid';



const loadingVisible = ref(false);
databaseName.checkWebsiteVersion();

const today = ref(new Date());
const tenDaysAgo = ref(new Date());
tenDaysAgo.value.setDate(tenDaysAgo.value.getDate() - 10);

const hospitalDataSource = ref([]);
const getHospitalData = () => {
  loadingVisible.value = true;
  let hospitalSearchParams = {
    date: today.value,
  }

  axios.get('api/Log/ShippingErrors', { params: hospitalSearchParams }).then((resp) => {
    //console.log(resp.data.data);
    if (resp.status === 200) {
      hospitalDataSource.value = resp.data.data;
      loadingVisible.value = false;
      getListOfErrorMessages();
    } else {
      hospitalDataSource.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    } else {
      if(error.response){
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the server have not lost connection');
        return;
      }
    }
  });
};
getHospitalData();

const errorTypeArray = ref([]);
const numberInHospitalLane = ref('');
// const clearedFilterValue = ref('all');
const getListOfErrorMessages = () => {
  errorTypeArray.value = [];
  let counter = 0;
  hospitalDataSource.value.forEach((msg) => {
    if (errorTypeArray.value.length == 0) {
      errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
      counter++;
      // switch (clearedFilterValue.value) {
      //   case 'clear':
      //     if (msg.cleared == true) {
      //       errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
      //       counter++;
      //     }
      //     break;
      //   case 'notClear':
      //     if (msg.cleared == false) {
      //       errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
      //       counter++;
      //     }
      //     break;
      //   case 'all':
      //     errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
      //     counter++;
      // }
    } else {
      let h = errorTypeArray.value.findIndex(m => m.messageType == msg.error_title);
      if (h == -1) {
        errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
        counter++;
        // switch (clearedFilterValue.value) {
        //   case 'clear':
        //     if (msg.cleared == true) {
        //       errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
        //       counter++;
        //     }
        //     break;
        //   case 'notClear':
        //     if (msg.cleared == false) {
        //       errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
        //       counter++;
        //     }
        //     break;
        //   case 'all':
        //     errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
        //     counter++;
        // }
      } else {
        errorTypeArray.value[h].count++;
        counter++;
        // switch(clearedFilterValue.value) {
        //   case 'clear':
        //     if (msg.cleared == true) {
        //       errorTypeArray.value[h].count++;
        //       counter++;
        //     } 
        //     break;
        //   case 'notClear':
        //     if (msg.cleared == false) {
        //       errorTypeArray.value[h].count++;
        //       counter++;
        //       }
        //     break;
        //   case 'all':
        //     errorTypeArray.value[h].count++;
        //     counter++;
        //     break;
        // }
      }
    }
  })
  numberInHospitalLane.value = `${counter} Found`;
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Jackpot_Lane_${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};

const modifySearchPanelStyleJackpotLanePage = (e) => {
  if (e.parentType === "searchPanel") {
      e.editorOptions.stylingMode = "underlined";
  }
};
</script>

<style lang="scss">
</style>

<template>
  <div>
    <!-- <h2 class="content-block">Real Time Device View</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px;">
          <div>
            <DxSelectBox
              v-model:data-source="scannerList"
              v-model:value="selectedScanner"
              display-expr="scanner_name"
              value-expr="scanner_name"
              styling-mode="underlined"
              :width="150"
              @value-changed="updateScanner"
              placeholder="Select Device..."
            />
          </div>
          <div>
            <div v-if="pauseUpdateBool">
              <strong>Click To Resume Updates:</strong>
            </div>
            <div v-else>
              <strong>Pause Updates To Examine The Data:</strong>
            </div>
            <div style="display: flex; justify-content: center;">
              <div v-if="pauseUpdateBool">
                <DxButton 
                  text="Resume Updates"
                  type="default"
                  styling-mode="contained"
                  @click="pauseDataGridUpdates"
                  :disabled="selectedScanner == ''"
                />
              </div>
              <div v-else>
                <DxButton 
                  text="Pause Updates"
                  type="default"
                  styling-mode="contained"
                  @click="pauseDataGridUpdates"
                  :disabled="selectedScanner == ''"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- TEST DIV FOR HTML TABLE - NO FLASH ???? -->
         <div style="display: flex; justify-content: space-between; align-items: baseline;">
          <strong>Grid Shows Up To Last 1000 Scanner Transactions</strong>
          <div v-show="pauseUpdateBool" style="margin-bottom: 15px;">
            <DxButton 
              text="View Data Grid To Filter Data"
              type="default"
              styling-mode="contained"
              @click="clickToShowDataGridPopup"
            />
          </div>
         </div>
        <div id="tableDivContainer">
          <table id="rtMsgTable">
            <thead>
              <tr>
                <th>Msg Type</th>
                <th>Msg Time</th>
                <th>Message</th>
              </tr>
            </thead>
            <tbody>

            </tbody>
          </table>
        </div>
        <!-- END TEST -->
        <div>
          <DxPopup
            v-model:visible="realTimeDataGridToExaminePopup"
            :drag-enabled="false"
            :hide-on-outside-click="true"
            :show-close-button="true"
            :show-title="true"
            height="80%"
            :title="realTimeGridPopupTitle"
            @hidden="rtGridPopupHiding"
          >
            <div>
              <DxDataGrid
                :data-source="RealTimeData"
                :key="wsRespKey"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
                :column-auto-width="true"
                height="500px"
                @exporting="onExporting"
              >
                <DxScrolling mode="infinite"/>
                <DxHeaderFilter 
                  :visible="true"
                />
                <DxExport   
                  :enabled="true"
                />
                  <DxColumn
                    data-field="msg_type"
                    caption="Msg Type"
                    width="170px"
                  >
                    <DxHeaderFilter 
                      :data-source="msgTypeHeaderFilterData"
                    />
                  </DxColumn>
                  <DxColumn
                    data-field="msg_time"
                    caption="Msg Time"
                    width="240px"
                    :allowHeaderFiltering="false"
                  />
                  <DxColumn
                    data-field="msg"
                    caption="Message"
                    :allowHeaderFiltering="false"
                  />
              </DxDataGrid>
            </div>
          </DxPopup>
        </div>
      </div>
    </div>
    <h4 class="content-block">Server Connection Status</h4>
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible2"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div v-if="webSocketConnected" style="height: 60px;">
          <div style="font-size: 20px;">
            <div class="client-connected-state-rt-device">
              Status: {{webSocketStatusMsg}}
            </div>
          </div>
        </div>
        <div v-else style="height: 60px;">
          <div style="font-size: 20px;">
            <div class="client-not-connected-state-rt-device">
              Status: {{webSocketStatusMsg}}
            </div>
            <div style="float: left; padding-left: 5%;">
            Auto Re-Try Connection In: {{refreshWaitTimeCounter}}
            </div>
            <div style="float: left; padding-left: 2%;">
            <DxLoadIndicator
              style="margin-bottom: -10px;"
              v-model:visible="loadingVisible2"
              :height="30"
              :width="30"
            />
            </div>
          </div>
        </div>
        <DxButton
          @click="initWebSocket"
          :width="250"
          text="Bypass Auto Reconnect"
          type="success"
          styling-mode="contained"
          style="margin-top: 10px;"
          :disabled="webSocketConnected"
        />
        <!-- <DxButton
          @click="resetAlerts"
          :width="250"
          text="Reset Local Alerts"
          type="default"
          styling-mode="contained"
          style="margin-top: 10px; margin-left: 10px;"
          :disabled="!webSocketConnected"
        /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted, onMounted } from 'vue';
import axios from 'axios';
import databaseName from '../myFunctions/databaseName';
import notify from 'devextreme/ui/notify';
import { DxSelectBox } from 'devextreme-vue/select-box';
import DxButton from 'devextreme-vue/button';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import { DxLoadIndicator } from 'devextreme-vue/load-indicator';
import DxPopup from 'devextreme-vue/popup';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxExport,
  //DxFilterRow,
  DxScrolling,
  DxHeaderFilter
} from 'devextreme-vue/data-grid';

//const loadingVisible = ref(false);
databaseName.checkWebsiteVersion();

function arrClone(source){
  return JSON.parse(JSON.stringify(source));
}

const msgTypeHeaderFilterData = ref([
  { text: 'Divert Confirm', value: 'DivertConfirm' },
  { text: 'Divert Request', value: 'DivertRequest' },
  { text: 'Raw Scan', value: 'RawScan' }
]);

onMounted(() => {
  initWebSocket();
});

const selectedScanner = ref("");
const scannerList = ref([]);

const RealTimeData = ref([]);


//get all the scanners available to be used

const LoadScanners = () =>{
  console.log('load scanners called');
  axios.get('api/Admin/AllScanners').then((resp) => {

    scannerList.value = resp.data.data;

  }).catch(error=>{
    loadingVisible2.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      console.error(error);
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
}
LoadScanners();


//WebSockets

const initWsJsonMsg = databaseName.getWebsocketMessageBase();
const webSocketEndpoint = databaseName.getRealtimeWebsocketEndpoint();

//webs socket stuff only have show when its hospital admin user or programmer user
const webSocket = ref(null);
const webSocketConnected = ref(false);
const webSocketStatusMsg = ref("Not Connected");
//const webSocketReturnMsg = ref(null);
const reconnectCounter = ref(0);       //attempts to reconnect
const reconnectMinWaitTime = ref(0.1); //in minutes
const reconnectMaxWaitTime = ref(2);   //in minutes
const reconnectConnectedWaitTime = ref(60);  //in seconds, dont want to have this go into loop to ofter even though it wont hurt anything
const reconnectIncreaseTimeAtCount = ref(10);  //change check time to reconnectMaxWaitTime
const reconnectRealWaitTime = ref(0.1);        //this will be the actuall wait time 
const refreshWaitTimeCounter = ref("");
// const countdownTimerEnabled = ref(true);
// const foreverTrue = ref(true);
//const literalCheckReconnectInterval = ref(Number); - keep commented out
const literalCountdownTimerInterval = ref(Number);
const reconnectTimeLeftMS = ref(5000);
const cancelReconnect = ref(true);
// const initWsJsonMsg = ref({});
const loadingVisible2 = ref(false);
const leftPage = ref(false);

const updateScanner = () => {
  //make sure that we reset the datasource when they switch scanners!!!
  RealTimeData.value = [];
  pausedDataArray.value = [];
  pauseUpdateBool.value = false;

  let tmpRealJson = initWsJsonMsg.value;
  tmpRealJson.msgType = "ESTABLISH";
  tmpRealJson.msgFromService = "";
  tmpRealJson.msgToService = "HOST-SERVER";
  tmpRealJson.EstablishConnObj.userIdName = "N/A";
  tmpRealJson.EstablishConnObj.userSecurity = "N/A";
  tmpRealJson.EstablishConnObj.message = "updating group";
  tmpRealJson.EstablishConnObj.messageGroup = selectedScanner.value;
  webSocket.value.send(JSON.stringify(tmpRealJson));

};

// const hospitalAdminInOperationOnServer = ref(false);
const countdownTimer = () => {
  console.log("I am the coutdown interval setter");
  // let self = this;
  cancelReconnect.value = false;
  clearInterval(literalCountdownTimerInterval.value);
  literalCountdownTimerInterval.value = setInterval(()=>{
    if(reconnectTimeLeftMS.value >= 0 && !cancelReconnect.value){
      countdownTimerInterval();
    }
    else{
      clearInterval(literalCountdownTimerInterval.value);
      console.log("Stopped Interval");
    }
  }, 1000)
};
const countdownTimerInterval = () => {
  reconnectTimeLeftMS.value = reconnectTimeLeftMS.value - 1000;
  //console.log(((reconnectTimeLeftMS.value % (1000 * 60))));
  let minutes = Math.floor((reconnectTimeLeftMS.value) / (1000*60)%60);
  let seconds = (reconnectTimeLeftMS.value / 1000) % 60;

  let strMinutes = minutes > 9 ? minutes : '0' + minutes;
  let strSeconds = seconds > 9 ? seconds : '0' + seconds;

  refreshWaitTimeCounter.value = strMinutes + ':' + strSeconds;
  console.log(refreshWaitTimeCounter.value);
  //decrement ms left till retry
  
  console.log(reconnectTimeLeftMS.value);
  if(reconnectTimeLeftMS.value <= 0){
    clearInterval(literalCountdownTimerInterval.value);
    initWebSocket();
  }
};
// async function handelAlertMessage(title, html) {
//   let alertResult = alert(html, title);
//   alertResult.then(()=>{
//     hospitalAdminInOperationOnServer.value = false;
//   });
// }
const wsRespKey = ref(0);
const pausedDataArray = ref([]);
const rowCount = ref(0);
async function processCommand(msgJson) {

  const tableEl = document.getElementById('rtMsgTable');

  msgJson = JSON.parse(msgJson);
  let tempResp = '';
  //let tempRespMessage = '';
  console.log('msgJson', msgJson);

  tempResp = JSON.parse(msgJson.CommandObj.cmdMessage);
  console.log('tempResp', tempResp);
  //let tempMsg = JSON.parse(tempResp.MsgValue);

  //if length is over 1000 remove 100 elements from array
  if (pauseUpdateBool.value) {
    if(pausedDataArray.value.length > 1000) {
      pausedDataArray.value.splice(pausedDataArray.value.length - 100, 100);
    }
    switch(msgJson.CommandObj.cmdName){
      case 'DivertConfirm':
        pausedDataArray.value.unshift({msg_type: msgJson.CommandObj.cmdName, msg_time: tempResp.datetime, msg: `Barcode: ${tempResp.lpn_barcode}, Confirmed Divert To: ${tempResp.dest_code}, With Sort Code: ${tempResp.sort_code}`});
        break;
      case 'DivertRequest':
        //RawScan
        pausedDataArray.value.unshift({msg_type: 'RawScan', msg_time: tempResp.scan_datetime, msg: `Raw Scan Message: ${tempResp.raw}, Barcode: ${tempResp.lpn_barcode}`});
        //DivertConfirm
        pausedDataArray.value.unshift({msg_type: msgJson.CommandObj.cmdName, msg_time: tempResp.request_datetime, msg: `Barcode: ${tempResp.lpn_barcode}, Requested Divert To: ${tempResp.dest_code}`});
        break;
    }
  } else {
    if(RealTimeData.value.length > 1000) {
      RealTimeData.value.splice(RealTimeData.value.length - 100, 100);
    }
    if (!pauseUpdateBool.value) {
      switch(msgJson.CommandObj.cmdName){
        case 'DivertConfirm': {
            RealTimeData.value.unshift({msg_type: msgJson.CommandObj.cmdName, msg_time: tempResp.datetime, msg: `Barcode: ${tempResp.lpn_barcode}, Confirmed Divert To: ${tempResp.dest_code}, With Sort Code: ${tempResp.sort_code}`});
            let newDivConfirmRow = tableEl.insertRow(1);
            let confirmMsgTypeCell = newDivConfirmRow.insertCell(0);
            let confirmMsgTimeCell = newDivConfirmRow.insertCell(1);
            let confirmMsgCell = newDivConfirmRow.insertCell(2);
            confirmMsgTypeCell.innerHTML = msgJson.CommandObj.cmdName;
            confirmMsgTimeCell.innerHTML = tempResp.datetime;
            confirmMsgCell.innerHTML = `Barcode: ${tempResp.lpn_barcode}, Confirmed Divert To: ${tempResp.dest_code}, With Sort Code: ${tempResp.sort_code}`;
            rowCount.value++;
            if (rowCount.value > 1000) {
              tableEl.deleteRow(rowCount.value);
              rowCount.value--;
            }
          }
          break;
        case 'DivertRequest': {
          //RawScan
            RealTimeData.value.unshift({msg_type: 'RawScan', msg_time: tempResp.scan_datetime, msg: `Raw Scan Message: ${tempResp.raw}, Barcode: ${tempResp.lpn_barcode}`});
            let newDivRequestRow = tableEl.insertRow(1);
            let requestMsgTypeCell = newDivRequestRow.insertCell(0);
            let requestMsgTimeCell = newDivRequestRow.insertCell(1);
            let requestMsgCell = newDivRequestRow.insertCell(2);
            requestMsgTypeCell.innerHTML = 'RawScan';
            requestMsgTimeCell.innerHTML = tempResp.scan_datetime;
            requestMsgCell.innerHTML = `Raw Scan Message: ${tempResp.raw}, Barcode: ${tempResp.lpn_barcode}`;
            rowCount.value++;
            if (rowCount.value > 1000) {
              tableEl.deleteRow(rowCount.value);
              rowCount.value--;
            }
          //DivertConfirm
            RealTimeData.value.unshift({msg_type: msgJson.CommandObj.cmdName, msg_time: tempResp.request_datetime, msg: `Barcode: ${tempResp.lpn_barcode}, Requested Divert To: ${tempResp.dest_code}`});
            newDivRequestRow = tableEl.insertRow(1);
            requestMsgTypeCell = newDivRequestRow.insertCell(0);
            requestMsgTimeCell = newDivRequestRow.insertCell(1);
            requestMsgCell = newDivRequestRow.insertCell(2);
            requestMsgTypeCell.innerHTML = msgJson.CommandObj.cmdName;
            requestMsgTimeCell.innerHTML = tempResp.request_datetime;
            requestMsgCell.innerHTML = `Barcode: ${tempResp.lpn_barcode}, Requested Divert To: ${tempResp.dest_code}`;
            rowCount.value++;
            if (rowCount.value > 1000) {
              tableEl.deleteRow(rowCount.value);
              rowCount.value--;
            }
          }
          break;
        }
    }
      wsRespKey.value++;
  }
}

const realTimeDataGridToExaminePopup = ref(false);
const realTimeGridPopupTitle = ref('');
const clickToShowDataGridPopup = () => {
  realTimeGridPopupTitle.value = `${selectedScanner.value} Real Time Data Grid`;
  realTimeDataGridToExaminePopup.value = !realTimeDataGridToExaminePopup.value;
};
const rtGridPopupHiding = () => {
  realTimeGridPopupTitle.value = '';
};

const initWebSocket = () => {
  //initSocket();
  //alias self to this to trick vue to update the state changes
  // let self = this;
  reconnectCounter.value++;

  webSocket.value = new WebSocket(webSocketEndpoint.value);
  
  loadingVisible2.value = true;
  //setTimeout(()=>{self.loadingVisible = false;},500);
  
  webSocket.value.onopen = function() {
    cancelReconnect.value = true;
    webSocketConnected.value = true;
    reconnectCounter.value = 0;
    reconnectRealWaitTime.value = reconnectConnectedWaitTime.value;
    webSocketStatusMsg.value = "Connected To Server";
    loadingVisible2.value = false;

    clearInterval(literalCountdownTimerInterval.value);

    notify('Connection Attempt Success.', 'success', 2500);

    let tmpRealJson = initWsJsonMsg.value;
      tmpRealJson.msgType = "ESTABLISH";
        tmpRealJson.msgFromService = "";
        tmpRealJson.msgToService = "HOST-SERVER";
        tmpRealJson.EstablishConnObj.userIdName = "N/A";
        tmpRealJson.EstablishConnObj.userSecurity = "N/A";
        tmpRealJson.EstablishConnObj.message = "connecting for the first time";
        tmpRealJson.EstablishConnObj.messageGroup = "GENERAL";
    webSocket.value.send(JSON.stringify(tmpRealJson));
    //LoadScanners();
  };
    
  webSocket.value.onmessage = function (evt) { 
    console.log('evt', evt);
    try{
      //webSocketReturnMsg.value = JSON.parse(evt.data);
      //console.log(webSocketReturnMsg.value);
      processCommand(evt.data);
    }
    catch{
      console.log("Caught message exception");
    }
    
    //this.msg += evt.data;
    
    //webSocket.send("fsdajhklasfd");
    //alert("Message is received...");
  };
    
  webSocket.value.onclose = function() { 
    console.log("onclose recon timer=>",reconnectCounter.value)
    webSocketStatusMsg.value = "Connection Closed";
    webSocketConnected.value = false;
    // websocket is closed.
    //alert("Connection is closed..."); 
    webSocket.value = null; 
    
    if(!leftPage.value){
      notify('Connection Attempt Failed.', 'error', 2500);
      if(reconnectCounter.value >= 0 && reconnectCounter.value < reconnectIncreaseTimeAtCount.value){          
        reconnectTimeLeftMS.value = reconnectMinWaitTime.value * 60000; 
        countdownTimer();
      }
      else{
        reconnectTimeLeftMS.value = reconnectMaxWaitTime.value * 60000;
        countdownTimer();
      }
    }
    loadingVisible2.value = false;
  };
};
const pauseUpdateBool = ref(false);
const pauseDataGridUpdates = () => {
  if (pauseUpdateBool.value) {
    rowCount.value = 0;
    const tableEl = document.getElementById('rtMsgTable');
    tableEl.innerHTML = `           
      <thead>
        <tr>
          <th>Msg Type</th>
          <th>Msg Time</th>
          <th>Message</th>
        </tr>
      </thead>
      <tbody>

      </tbody>`;
    for (let i = pausedDataArray.value.length -1; i >= 0; i--) {
      switch(pausedDataArray.value[i].msg_type){
        case 'DivertConfirm': {
            let newDivConfirmRow = tableEl.insertRow(1);
            let confirmMsgTypeCell = newDivConfirmRow.insertCell(0);
            let confirmMsgTimeCell = newDivConfirmRow.insertCell(1);
            let confirmMsgCell = newDivConfirmRow.insertCell(2);
            confirmMsgTypeCell.innerHTML = pausedDataArray.value[i].msg_type;
            confirmMsgTimeCell.innerHTML = pausedDataArray.value[i].msg_time;
            confirmMsgCell.innerHTML = pausedDataArray.value[i].msg;
            rowCount.value++;
            if (rowCount.value > 1000) {
              tableEl.deleteRow(rowCount.value);
              rowCount.value--;
            }
          }
          break;
        case 'RawScan': {
            let newDivConfirmRow = tableEl.insertRow(1);
            let confirmMsgTypeCell = newDivConfirmRow.insertCell(0);
            let confirmMsgTimeCell = newDivConfirmRow.insertCell(1);
            let confirmMsgCell = newDivConfirmRow.insertCell(2);
            confirmMsgTypeCell.innerHTML = pausedDataArray.value[i].msg_type;
            confirmMsgTimeCell.innerHTML = pausedDataArray.value[i].msg_time;
            confirmMsgCell.innerHTML = pausedDataArray.value[i].msg;
            rowCount.value++;
            if (rowCount.value > 1000) {
              tableEl.deleteRow(rowCount.value);
              rowCount.value--;
            }
          }
          break;
        case 'DivertRequest': {
          //DivertConfirm
            let newDivConfirmRow = tableEl.insertRow(1);
            let confirmMsgTypeCell = newDivConfirmRow.insertCell(0);
            let confirmMsgTimeCell = newDivConfirmRow.insertCell(1);
            let confirmMsgCell = newDivConfirmRow.insertCell(2);
            confirmMsgTypeCell.innerHTML = pausedDataArray.value[i].msg_type;
            confirmMsgTimeCell.innerHTML = pausedDataArray.value[i].msg_time;
            confirmMsgCell.innerHTML = pausedDataArray.value[i].msg;
            rowCount.value++;
            if (rowCount.value > 1000) {
              tableEl.deleteRow(rowCount.value);
              rowCount.value--;
            }
          }
          break;
        }
    }
    RealTimeData.value = arrClone(pausedDataArray.value);
    pauseUpdateBool.value = false;
    // let tmpRealJson = initWsJsonMsg.value;
    //   tmpRealJson.msgType = "ESTABLISH";
    //   tmpRealJson.msgFromService = "";
    //   tmpRealJson.msgToService = "HOST-SERVER";
    //   tmpRealJson.EstablishConnObj.userIdName = "N/A";
    //   tmpRealJson.EstablishConnObj.userSecurity = "N/A";
    //   tmpRealJson.EstablishConnObj.message = "updating group";
    //   tmpRealJson.EstablishConnObj.messageGroup = selectedScanner.value;
    // webSocket.value.send(JSON.stringify(tmpRealJson));
    wsRespKey.value++;
  } else {
    pausedDataArray.value = arrClone(RealTimeData.value);
    pauseUpdateBool.value = true;
    // let tmpRealJson = initWsJsonMsg.value;
    //   tmpRealJson.msgType = "ESTABLISH";
    //   tmpRealJson.msgFromService = "";
    //   tmpRealJson.msgToService = "HOST-SERVER";
    //   tmpRealJson.EstablishConnObj.userIdName = "N/A";
    //   tmpRealJson.EstablishConnObj.userSecurity = "N/A";
    //   tmpRealJson.EstablishConnObj.message = "pause data update";
    //   tmpRealJson.EstablishConnObj.messageGroup = "GENERAL";
    // webSocket.value.send(JSON.stringify(tmpRealJson));
  }
};

onUnmounted(() => {
  try {
    webSocket.value.close();
    webSocket.value = null;
  } catch (error) {
    console.log(error);
  }
  //make sure to clear interval on a page leave since this is a single page web app
  //if you do not it will run in the background
  leftPage.value = true;

  clearInterval(literalCountdownTimerInterval.value); 
});


const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ":" + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `${realTimeGridPopupTitle.value}_${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};
</script>

<style scoped lang="scss">
  .client-connected-state-rt-device {
    background-color: #90EE90;
    width: 35%;
    text-align: center;
    float: left;
  }
  .client-not-connected-state-rt-device {
    background-color: #FF0000;
    color: white;
    width: 35%;
    text-align: center;
    float: left;
  }
  #tableDivContainer {
    height: 700px;
    overflow-y: auto;
  }
  table {
    border-collapse: collapse;
    text-align: center;
    width: 100%;
  }
  table td, table th {
    border: 1px solid black;
  }
  table td:nth-child(3) {
    text-align: left;
    padding-left: 5px;
  }
</style>

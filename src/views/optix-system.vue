<template>
  <div>
    <!-- <h2 class="content-block">Optix System</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div style="margin-bottom: 10px;">
          <DxButton 
            :text="btnText"
            type="default"
            styling-mode="contained"
            @click="clickToShowOrHideEmbedDiv"
          />
        </div>
        <div v-if="showDivWithEmbedSite">
          <embed src="https://*************:8080" style="height: 90vh; width: 100%;">
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import DxButton from 'devextreme-vue/button';

databaseName.checkWebsiteVersion();
const showDivWithEmbedSite = ref(false);
const btnText = ref('Show Optix System')
const clickToShowOrHideEmbedDiv = () => {
  if (showDivWithEmbedSite.value) {
    btnText.value = 'Show Optix System';
  } else {
    btnText.value = 'Hide Optix System';
  }
  showDivWithEmbedSite.value = !showDivWithEmbedSite.value;
};
</script>

<style lang="scss">
</style>

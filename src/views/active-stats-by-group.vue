<template>
  <div>
    <!-- <h2 class="content-block">Active Stats By Group</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div :class="showMobileViewOrNot.activeGroupSearchContainer">
          <div>
            <strong>Time Range</strong>
            <DxSelectBox 
              :data-source="timeRangeSource"
              display-expr="disp"
              value-expr="value"
              v-model:value="timeRangeSelection"
              styling-mode="underlined"
              style="margin-right: 25px;"
              @value-changed="timeRangeSelectToDisable5MinsActiveGroup"
            />
          </div>
          <div>
            <strong>Interval</strong>
            <DxSelectBox 
              :data-source="minuteIntervalSource"
              display-expr="disp"
              value-expr="value"
              v-model:value="minuteIntervalSelection"
              styling-mode="underlined"
              style="margin-right: 25px;"
            />
          </div>
          <div>
            <strong>Group</strong>
            <DxSelectBox 
              :data-source="ActiveStatsHeader"
              display-expr="caption"
              value-expr="name"
              v-model:value="statGroupSelection"
              styling-mode="underlined"
              style="margin-right: 25px;"
              width="200px"
            />
          </div>
          <div>
            <DxButton 
              text="Search"
              type="default"
              styling-mode="contained"
              @click="getActiveStatsByGroup"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxTabPanel
          :key="selectionChangedKey"
          :height="800"
          :data-source="ActiveStatsHeader[statGroupIndex].displayGroups"
          v-model:selected-index="displayGroupIndex"
          :loop="true"
          :animation-enabled="false"
          :swipe-enabled="true"
          @selection-changed="onTabChanged"
        >
          <template #title="{ data: group }">
            <span>{{group.displayName}}</span>
          </template>
          <template #item="{ data: group }">
          <div>
            <DxDataGrid
              :height="300"
              :data-source="group.data"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              :column-auto-width="true"
              @exporting="onExporting"
            >
              <DxExport   
                :enabled="true"
              />
              <DxScrolling mode="virtual"/>
              <DxSorting mode="none"/>
              <DxColumn
                :width="170"
                data-field="StartDateTime"
                caption="Start"
                data-type="datetime"
                :fixed="true" 
                fixed-position="left"
              />
              <DxColumn
                :width="170"
                data-field="EndDateTime"
                caption="End"
                data-type="datetime"
                :fixed="true" 
                fixed-position="left"
              />
              <DxColumn v-for="(val) in group.statuses" :key="val.name"
                :data-field="val.name"
                :caption="val.caption"
                alignment="center"
              />
              <DxSummary>
                <DxTotalItem v-for="(val) in group.statuses" :key="val.name"
                  :column="val.name"
                  summary-type="sum"
                  display-format="{0}"
                  css-class="gridTotals"
                />
              </DxSummary>
            </DxDataGrid>
            <DxChart
              :data-source="group.data"
              :title="group.displayName"
            >
              <DxTooltip
                :enabled="true"
                :content-template="graphToolTipFunctionByGroup"
              />
              <DxCommonSeriesSettings
                argument-field="EndDateTime"
                type="line"
                hover-mode="allArgumentPoints"
              />

              <DxArgumentAxis
                argument-type="datetime"
              >
                <DxLabel
                  :staggering-spacing="10"
                  display-mode="stagger"
                />
              </DxArgumentAxis>
              <DxSeries v-for="(val) in group.statuses" :key="val.name"
                :value-field="val.name"
                :name="val.caption"
              />
              <DxLegend 
                vertical-alignment="bottom"
                horizontal-alignment="center"
              >
                <DxMargin :top="25"/>
              </DxLegend>
            </DxChart>
          </div>
          </template>
        </DxTabPanel>
      </div>
    </div>
  </div>
</template>

<script setup>
import DxTabPanel from 'devextreme-vue/tab-panel';
import { ref } from 'vue';
import axios from 'axios';
import databaseName from '../myFunctions/databaseName';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import { DxSelectBox } from 'devextreme-vue/select-box';
import DxButton from 'devextreme-vue/button';
import notify from 'devextreme/ui/notify';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxExport,
  DxSummary,
  DxTotalItem,
  DxScrolling,
  DxSorting
} from 'devextreme-vue/data-grid';
import DxChart, {
    DxSeries,
    DxCommonSeriesSettings,
    DxTooltip,
    // DxCommonAxisSettings,
    DxLabel,
    // DxFormat,
    DxMargin,
    DxLegend,
    DxArgumentAxis,
} from 'devextreme-vue/chart';

databaseName.checkWebsiteVersion();

const loadingVisible = ref(false);

const isMobile = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunction = () => {
  let activeGroupSearchContainer = '';
  if (isMobile) {
    activeGroupSearchContainer = '';
  } else {
    activeGroupSearchContainer = 'activeGroupSearchContainerNotMobile';
  }
  return {
    activeGroupSearchContainer,
  }
};
const showMobileViewOrNot = showMobileViewOrNotFunction();

const timeRangeSource = ref([
  { id: 1, disp: '1 Hour', value: 1 },
  { id: 2, disp: '2 Hours', value: 2 },
  { id: 3, disp: '3 Hours', value: 3 },
  { id: 4, disp: '4 Hours', value: 4 },
  { id: 5, disp: '5 Hours', value: 5 },
  { id: 6, disp: '6 Hours', value: 6 },
  { id: 7, disp: '7 Hours', value: 7 },
  { id: 8, disp: '8 Hours', value: 8 },
  { id: 9, disp: '9 Hours', value: 9 },
  { id: 10, disp: '10 Hours', value: 10 },
  { id: 11, disp: '11 Hours', value: 11 },
  { id: 12, disp: '12 Hours', value: 12 },
]);
const timeRangeSelection = ref(1);
const disable5MinInterval = ref(false);
const minuteIntervalSource = ref([
  { id: 1, disp: '5 Minutes', value: 5, disabled: disable5MinInterval },
  { id: 2, disp: '15 Minutes', value: 15 },
  { id: 3, disp: '30 Minutes', value: 30 },
  // { id: 4, disp: '45 Minutes', value: 45 },
  { id: 5, disp: '60 Minutes', value: 60 },
]);
const minuteIntervalSelection = ref(15);

//TYLER STUFF
const ActiveStatsHeader = ref([]);
ActiveStatsHeader.value = databaseName.getActiveStatGroupsData();

const statGroupIndex = ref(0);
const displayGroupIndex = ref(0);

const selectionChangedKey = ref(0);
const onTabChanged = () =>{
  selectionChangedKey.value++;
}

//END TYLER STUFF

const statGroupSelection = ref('');
statGroupSelection.value = ActiveStatsHeader.value[0].name;

const timeRangeSelectToDisable5MinsActiveGroup = (data) => {
  switch (data.value) {
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
      disable5MinInterval.value = true;
      minuteIntervalSelection.value = 15;
      break;
    case 1:
      disable5MinInterval.value = false;
      break;
  }
};



const groupArrayForDisplay = ref([]);

const getActiveStatsByGroup = () => {
console.log(statGroupSelection.value);
  for(let c = 0; c < ActiveStatsHeader.value.length; c++){
    if(ActiveStatsHeader.value[c].name == statGroupSelection.value){
      displayGroupIndex.value = 0;
      statGroupIndex.value = c;
    }
    for(let i = 0; i < ActiveStatsHeader.value[c].displayGroups.length; i++){
      ActiveStatsHeader.value[c].displayGroups[i].data = [];
    }
  }

  loadingVisible.value = true;
  groupArrayForDisplay.value = [];
  let activeGroupStatsParams = {
    HoursTimeRange: timeRangeSelection.value,
    IntervalMinutes: minuteIntervalSelection.value,
    StatGroup: statGroupSelection.value
  }

  let indexMap = {};

  axios.get('api/Stats/ActiveStats', { params: activeGroupStatsParams }).then((resp) => {
    console.log(resp.data);
    if (resp.status === 200) {

      for (let t = 0; t < resp.data.data.TimeSpanHeader.length; t++) {
        //TYLER
        for(let c = 0; c < ActiveStatsHeader.value[statGroupIndex.value].displayGroups.length; c++){
          let tmpObj = {};
          tmpObj['StartDateTime'] = resp.data.data.TimeSpanHeader[t].StartDate.substring(0, resp.data.data.TimeSpanHeader[t].StartDate.length - 4);
          tmpObj['EndDateTime'] = resp.data.data.TimeSpanHeader[t].EndDate.substring(0, resp.data.data.TimeSpanHeader[t].EndDate.length - 4);

          for(let i = 0; i < ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].statuses.length; i++){
            if(t == 0){
              indexMap[ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].statuses[i].name] = c;
            }
            tmpObj[ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].statuses[i].name] = 0;
          }
          
          ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].data.push(tmpObj);

        }
        //END TYLER
      }
      for (let s = 0; s < resp.data.data.ScannerData.length; s++) {
        
        if(typeof indexMap[resp.data.data.ScannerData[s].StatName] === 'undefined'){
          console.error(`UnHandled Stat Name: ${resp.data.data.ScannerData[s].StatName}`);
        }
        else{
          ActiveStatsHeader.value[statGroupIndex.value].displayGroups[indexMap[resp.data.data.ScannerData[s].StatName]].data[resp.data.data.ScannerData[s].MasterRecordsIndex][resp.data.data.ScannerData[s].StatName] += resp.data.data.ScannerData[s].Count;
        }

      }
      loadingVisible.value = false;
    } else {
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  })
  .finally(()=>{
    selectionChangedKey.value++;
  })
};
// getActiveStatsByGroup();

const graphToolTipFunctionByGroup = (data) => {
  return `${data.seriesName}: ${data.value}`;
};

const onExporting = (e) => {
  // let today = new Date();
  // let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  // let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  // let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `StatGroup${ActiveStatsHeader.value[statGroupIndex.value].name}.xlsx`);
        });
  });
  e.cancel = true;
};


</script>

<style lang="scss">
.activeGroupSearchContainerNotMobile {
  display: flex;
  align-items: center;
}
</style>


<template>
  <div>
    <!--<h2 class="content-block">Os Errors</h2>-->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />

        <DxDataGrid
          :data-source="dataSource"
          :column-hiding-enabled="true"
          :column-auto-width="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          :word-wrap-enabled="true"
        >
          <DxPaging :page-size="10"/>
          <DxPager
            :show-page-size-selector="true"
            :allowed-page-sizes="[10 , 20, 40]"
            :show-info="true" 
          />
          <DxFilterRow
            :visible="true"
          />
          <DxSearchPanel
            :visible="true"
            :width="170"
            placeholder="Filter Results..."
          />
          <DxColumn
            data-field="lpn_barcode"
            caption="Container Barcode"
          />
          <DxColumn
            data-field="wms_order_number"
            caption="Order Number"
          />
          <DxColumn
            data-field="created_datetime"
            caption="Imported Datetime"
            data-type="datetime"
          />
          <DxColumn
            data-field="order_started_datetime"
            caption="Error Datetime"
            data-type="datetime"
          />
          <DxColumn
            data-field="lpn_barcode"
            cell-template="reRelease"
            caption=""
          />
            <template #reRelease="{data}">
              <DxButton
                :width="120"
                text="Re Release"
                type="success"
                styling-mode="contained"
                @click="reReleaseOrder(data.text)"
              />
            </template>
        </DxDataGrid>

      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import auth from '../auth';
import DxButton from 'devextreme-vue/button';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import databaseName from '../myFunctions/databaseName';
import notify from 'devextreme/ui/notify';
import { useRouter, useRoute } from 'vue-router';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow
} from 'devextreme-vue/data-grid';

databaseName.checkWebsiteVersion();
const router = useRouter();
const route = useRoute();

const dataSource = ref([]);

const loadingVisible = ref(false);

auth.getUser().then(user=>{
  // console.log(user);
  if(user.data.userSecurity === 'administrator' || user.data.userSecurity === 'programmer'){
    getData();
  }
  else{
    alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");
    // router.push('/login-form?redirect=/user-manager');
    router.push({  path: "/login-form",  query: { returnUrl: route.fullPath } });
  }
})
.catch(error=>{
  loadingVisible.value = false;
  //console.log(error.response);
  if(error.response){
    alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else if(error.request){
    alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else{
    alert('Unknown error\nMake sure that you and the server have not lost connection');
    return;
  }
});

const getData = () => {
  loadingVisible.value = true;
  axios({
    method: 'GET',
    url: 'api/OrderStart/UnableToStart'
  })
  .then(resp=>{
    if(resp.status === 200){
      dataSource.value = resp.data.data;
      loadingVisible.value = false;
      //notify(resp.data.message, 'success', 4000);
    }
    else{
      dataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    dataSource.value = [];
    loadingVisible.value = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
}
getData();

const reReleaseOrder = async (lpn_barcode) => {
  await auth.getUser().then(adminUser=>{
    axios({
        method: 'PATCH',
        url: `api/OrderStart/ReRelease/${lpn_barcode}`,
        headers:{
          'Authorization': `Bearer ${adminUser.data.userJwtToken}`
        }
      })
      .then(resp=>{
        if(resp.status == 200)
        {
          notify(resp.data.message, 'success', 5000);
        }
        else
        {
          notify(resp.data.message, 'error', 5000);
        }
      })
      .catch(error=>{
        loadingVisible.value = false;
        //console.log(error.response);
        if(error.response){
          alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else if(error.request){
          alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else{
          alert('Unknown error\nMake sure that you and the serve have not lost connection');
          return;
        }
      }).finally(()=>{
        getData();
      })
  })
}

</script>

<style lang="scss">
</style>

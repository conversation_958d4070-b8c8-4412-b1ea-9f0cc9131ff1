<template>
  <div>
    <!-- <h2 class="content-block">Error Log</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div id="pick-menu-cont-error">
          <div class="pick-audit-select-error">
          Data Limit:
          <DxSelectBox
            :data-source="selectBoxDS"
            :value="selectBoxDS[0].num"
            display-expr="title"
            styling-mode="underlined"
            value-expr="num"
            @value-changed="handleDataAmountChange"
          />
          </div>
          <DxButton
            class="pick-refresh-btn-error"
            :width="120"
            text="Refresh"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="getData"
          />
        </div>
        <div id="errorRadioGroup">
        Data Options:
          <DxRadioGroup
            :items="radioBtnArr"
            :value="isReporting"
            display-expr="disp"
            value-expr="Reporting"
            layout="horizontal"
            @value-changed="handleRadioBtnChange"
          />
        </div>
        <DxDataGrid
          :data-source="dataSource"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          :word-wrap-enabled="true"
          :column-auto-width="true"
          @editor-preparing="modifySearchPanelStyleErrorLog"
          @exporting="onExporting"
        >
        <DxPaging :page-size="10"/>
        <DxPager
          :show-page-size-selector="true"
          :allowed-page-sizes="[10 , 20, 40]"
          :show-info="true" 
        />
        <DxExport   
          :enabled="true"
        />
        <DxFilterRow
          :visible="true"
        />
        <DxSearchPanel
          :visible="true"
          :width="170"
          placeholder="Filter Results..."
        />
          <DxColumn
            data-field="error_source"
            caption="Source"
          />
          <DxColumn
            data-field="error_datetime"
            data-type="datetime"
            caption="Date"
            name="errorFirst"
          />
          <DxColumn 
            data-field="error_datetime"
            caption="Seconds.Milliseconds"
            v-model:visible="isProgrammerLevel"
            name="errorSecond"
            cell-template="isPro"
          />
            <template #isPro="{data}">
              <div>{{ errorSecsAndMilli(data.text) }}</div>
            </template>
          <DxColumn
            data-field="error_stack"
            caption="Stack Trace"
          />
          <DxColumn
            data-field="error_message"
            caption="Error Message"
          />
        </DxDataGrid>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import DxSelectBox from 'devextreme-vue/select-box';
import notify from 'devextreme/ui/notify';
import DxRadioGroup from 'devextreme-vue/radio-group';
import axios from 'axios';
import auth from '../auth';
import DxButton from 'devextreme-vue/button';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import databaseName from '../myFunctions/databaseName';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow,
  DxExport,
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

const BasicDataClass = ref({
  Limit: "1000",
  Filter: "-1",
  isReporting: "0"
});
// const databaseInfo =  ref(Object);
const dataSource =  ref([]);
// const pageSizes = ref([10,20,40]);
const loadingVisible = ref(false);
const radioBtnArr = ref(['Active Data < 7 days', 'Archived Data > 7 days']);
const isReporting = ref("0");
const limit = ref(1000);
// const filter = ref("Scanner Message Center");
const selectBoxDS = ref([{title: "1,000", num: 1000},
              {title: "2,000", num: 2000},
              {title: "5,000", num: 5000},
              {title: "10,000", num: 10000},
              {title: "20,000", num: 20000},
              {title: "Unlimited", num: -1}]);
// const selectBoxFilterDS = ref([
//   {title: 'Scanner', value: 'Scanner Message Center'},
//   {title: 'PLC', value: 'PLC Message Center'},
//   {title: 'Import', value: 'Import Message Center'},
//   {title: 'ALL', value: '-99'}
// ]);
databaseName.checkWebsiteVersion();

const getData = () => {
  loadingVisible.value = true;
  axios.get('api/Log/ErrorLogs', {params: BasicDataClass.value}).then(resp=>{
    if(resp.status === 200){
      dataSource.value = resp.data.data;
      loadingVisible.value = false;
      notify(resp.data.message, 'success', 5000);
    }
    else{
      dataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  })
  .catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
}
// databaseInfo.value = databaseName.getPaths();
databaseName.checkWebsiteVersion();
radioBtnArr.value = databaseName.getNumberOfDays();
getData();

const modifySearchPanelStyleErrorLog = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};
const handleDataAmountChange = (data) => {
  BasicDataClass.value.Limit = data.value.toString();
  limit.value = data.value;
  getData();
};
const handleRadioBtnChange = (data) => {
  if(data.value == 0){
    isReporting.value = "0";
    BasicDataClass.value.isReporting = "0";
    getData();
  }
  else{
    isReporting.value = "1";
    BasicDataClass.value.isReporting = "1";
    getData();
  }
};
// const handleDataFilterChange = (data) => {
//   BasicDataClass.value.Filter = data.value.toString();
//   filter.value = data.value;
//   getData();
// };

const errorSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Error Log ${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};
</script>

<style lang="scss">
#pick-menu-cont-error{
  //width: 600px;
  margin-bottom: 40px;
}
.pick-audit-select-error{
  float: left;
  width: 100px;
  margin-right: 20px;
  margin-bottom: 20px;
}
.alert-audit-select-filter{
  float: left;
  width: 120px;
  margin-right: 20px;
  margin-bottom: 20px;
}
.pick-refresh-btn-error{
  float: left;
  margin-right: 20px;
  margin-bottom: 20px;
  margin-top: 15px;
}
#errorRadioGroup{
  margin-top: 70px;
}
</style>

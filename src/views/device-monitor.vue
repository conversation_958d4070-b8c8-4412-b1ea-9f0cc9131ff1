<template>
  <div>
    <!-- <h2 class="content-block">Device Monitor</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div class="interval-controls">
          <div class="control-float-left-refresh">
            <div class="toolTipAlignboxMonitor">
              <i id="refreshIcon"
                class="dx-icon-help"
                @mouseenter="toggleRefreshTip"
                @mouseleave="toggleRefreshTip"
              />
              <DxTooltip
                target="#refreshIcon"
                v-model:visible="refreshTipVisible"
              >
                <p>Scanner Data will refresh at the interval selected</p>
              </DxTooltip>
              Refresh Time:
            </div>
            <DxSelectBox
              :data-source="refreshTimeData"
              :value="refreshTimeData[0].value"
              display-expr="title"
              value-expr="value"
              styling-mode="underlined"
              :width="150"
              @value-changed="handleRefreshTimeChange"
            />
            <DxLoadIndicator
              class="refresh-loading-indicator"
              v-model:visible="loadingVisible2"
              :height="30"
              :width="30"
            />
          </div>
          <div class="control-float-left">
            <div class="toolTipAlignboxMonitor">
              <i id="sessionIcon" 
                class="dx-icon-help"
                @mouseenter="toggleSessionTip"
                @mouseleave="toggleSessionTip"
              />
              <DxTooltip
                target="#sessionIcon"
                v-model:visible="sessionTipVisible"
              >
                <p>Total amount of time scanner data will refresh at the set interval</p>
              </DxTooltip>
              Session Time:
            </div>
            <DxSelectBox
              :data-source="sessionTimeData"
              :value="sessionTimeData[0].value"
              display-expr="title"
              value-expr="value"
              styling-mode="underlined"
              :width="150"
              @value-changed="handleSessionTimeChange"
            />
          </div>
          <div class="control-float-left">
            <DxButton
              @click="setSessionInterval"
              :width="160"
              text="Restart Session"
              type="default"
              styling-mode="contained"
              style="margin-top: 17px;"
              hint="Restart the session countdown"
            />
          </div>
          <div style="bottom: -30px; position: relative;">
            Time Remaining: {{totTimeMSDisplay}}
          </div>
        </div>
      </div>
    </div>
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <h3 id="scannerTitleStyle">Recent Scanner Information</h3>
        <DxTabPanel
          :key="selectionChangedKey"
          :height="400"
          :data-source="ActiveScannerHeader.ScannerGroups"
          v-model:selected-index="scannerGroupIndex"
          :loop="true"
          :animation-enabled="false"
          :swipe-enabled="true"
          @selection-changed="onTabChanged"
        >
          <template #title="{ data: group }">
            <span>{{group.groupName}}</span>
          </template>
          <template #item="{ data: group }">
            <DxDataGrid
              v-model:data-source="group.data"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              :column-auto-width="true"
              @cell-click="openScannerPopup"
              hint="Click Scanner for past 500 Scans"
            >
              <DxScrolling mode="virtual"/>
              <DxSorting mode="none"/>
              <DxColumn
                data-field="scannerName"
                caption="Scanner"
                cell-template="make-link"
              />
                <template #make-link="{data}">
                  <div class="scannerGridLink">{{data.text}}</div>
                </template>
              <DxColumn
                data-field="scanTime"
                data-type="datetime"
                caption="Date"
              />
              <DxColumn
                data-field="message"
                caption="Message"
              />
            </DxDataGrid>
          </template>
        </DxTabPanel>
          <!-- <h3 id="alarmTitleStyle">Recent Alarm Information</h3>
          <DxDataGrid
            :data-source="alarmDataSource"
            :column-hiding-enabled="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="false"
            :column-auto-width="true"
          >
          <DxPaging :page-size="10"/>
            <DxColumn
              data-field="alarmdate"
              data-type="datetime"
              caption="Date"
              alignment="left"
              name="alarmFirst"
            />
            <DxColumn 
              data-field="alarmdate"
              caption="Seconds.Milliseconds"
              v-model:visible="isProgrammerLevel"
              name="alarmSecond"
              cell-template="isProAlarm"
            />
              <template #isProAlarm="{data}">
                <div>{{ deviceMonitorAlarmSecsAndMilli(data.text) }}</div>
              </template>
            <DxColumn
              data-field="kvkalarmcode"
              caption="Alarm Code"
              alignment="left"
            /> -->
              <!-- cell-template="make-alarm-color" -->
              <!-- <template #make-alarm-color="{data}">
                <div :class="alarmClass(data.data.alarmdevicenote)">{{data.text}}</div>
              </template> -->
            <!-- <DxColumn
              data-field="alarmdevicenote"
              caption="Note"
              alignment="left"
            />
            <DxColumn
              data-field="kvkplc"
              caption="PLC"
              alignment="left"
            />
            <DxColumn
              data-field="kvkalarmmessage"
              caption="Message"
              alignment="left"
            />
          </DxDataGrid> -->
          <DxPopup
            v-model:visible="scannerPopupVisible"
            :drag-enabled="false"
            :hide-on-outside-click="true"
            :show-close-button="true"
            :show-title="true"
            height="80%"
            title="Scanner Audit Log. Last 500 Messages"
          >
            <DxButton 
              text="Refresh"
              type="success"
              styling-mode="contained"
              style="margin-bottom: 15px;"
              icon="refresh"
              @click="getScannerLogData(scannerFilterLookupName);"
            />
            <DxDataGrid
              :data-source="popupMostRecentDataSource"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              :column-auto-width="true"
              height="90%"
              @exporting="onExporting"
            >
            <DxFilterRow :visible="true" />
            <DxExport   
              :enabled="true"
            />
              <DxColumn
                data-field="audit_category"
                caption="Scanner"
              />
              <DxColumn
                data-field="audit_datetime"
                data-type="datetime"
                caption="Date"
                name="last500First"
              />
              <DxColumn 
                data-field="audit_datetime"
                caption="Seconds.Milliseconds"
                v-model:visible="isProgrammerLevel"
                name="last500Second"
                cell-template="isProLast500"
              />
                <template #isProLast500="{data}">
                  <div>{{ deviceMonitorAlarmSecsAndMilli(data.text) }}</div>
                </template>
              <DxColumn
                data-field="audit_filter"
                caption="Filter"
              />
              <DxColumn
                data-field="audit_message"
                caption="Message"
              />
            </DxDataGrid>
          </DxPopup>
      </div>
    </div>
  </div>
</template>

<script setup>

import DxTabPanel from 'devextreme-vue/tab-panel';
import { ref, onUnmounted, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import databaseName from '../myFunctions/databaseName';
import auth from '../auth';
import axios from 'axios';
//import scannerParser from '../myFunctions/scannerParser';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import DxSelectBox from 'devextreme-vue/select-box';
import { DxLoadIndicator } from 'devextreme-vue/load-indicator';
import { DxTooltip } from 'devextreme-vue/tooltip';
import notify from 'devextreme/ui/notify';
import DxPopup from 'devextreme-vue/popup';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  // DxPaging,
  DxFilterRow,
  DxExport,
  DxScrolling,
  DxSorting
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    // loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

onMounted(() => {
  setSessionInterval();
})

//TYLER STUFF

const ActiveScannerHeader = ref({});
ActiveScannerHeader.value = databaseName.getActiveScannerData();
console.log(ActiveScannerHeader.value);

const scannerGroupIndex = ref(0);

const selectionChangedKey = ref(0);
const onTabChanged = () =>{
  console.log("i was clicked");
  selectionChangedKey.value++;
  console.log(ActiveScannerHeader.value);
  console.log(`tab: ${scannerGroupIndex.value}`);
}
//END TYLER STUFF

const router = useRouter();
const loadingVisible = ref(false);
const loadingVisible2 = ref(false);
const interval = ref(Number);
const refreshTimeData = ref([
  {title: '15 Seconds', value: 15000},
  {title: '30 Seconds', value: 30000},
  {title: '45 Seconds', value: 45000},
  {title: '60 Seconds', value: 60000}
]);
const sessionTimeData = ref([
  {title: '30 Minutes', value: 30},
  {title: '1 Hour', value: 60},
  {title: '1.5 Hours', value: 90},
  {title: '2 Hours', value: 120}
]);
const refreshTime = ref(15000);
const sessionTime = ref(30);
const totTimeMS = ref(0);
const totTimeMSDisplay = ref ("");
const time = ref(0);
const sessionTipVisible = ref(false);
const refreshTipVisible = ref(false);

const AmtOfScanners = ref(Number);
databaseName.checkWebsiteVersion();
AmtOfScanners.value = databaseName.getAmtOfScanners();

const alarmDataSource = ref([]);
const getScannerData = () => {
  loadingVisible2.value = true;

  //TYLER STUFF
  let scannerIndexArray = {};
  for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups.length; i++){
    ActiveScannerHeader.value.ScannerGroups[i].data = [];
    for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup.length; c++){
      ActiveScannerHeader.value.ScannerGroups[i].data.push(
        {
          scannerName: ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName,
          scanTime: "",
          message: ""
        });
        scannerIndexArray[ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName] = [i, c];
    }
  }
  //END TYLER STUFF

  axios.get('api/Stats/DeviceMonitor').then((resp) => {
    console.log(resp.data);
    if (resp.status === 200) {
      console.log("hi=>", scannerIndexArray);
      for (let i = 0; i < resp.data.data.RecentScans.length; i++) {
        //TYLER STUFF
        console.log(resp.data.data.RecentScans[i].audit_category);
        let ScannerGroupsIndex = scannerIndexArray[resp.data.data.RecentScans[i].audit_category][0];
        let scannersInGroupIndex = scannerIndexArray[resp.data.data.RecentScans[i].audit_category][1];

        ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].data[scannersInGroupIndex].scanTime = resp.data.data.RecentScans[i].audit_datetime;
        ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].data[scannersInGroupIndex].message = resp.data.data.RecentScans[i].audit_message;

        //END TYLER STUFF
      }
      alarmDataSource.value = resp.data.data.AlarmMessages;
      console.log(ActiveScannerHeader.value);

      selectionChangedKey.value++;
      setTimeout(()=>{loadingVisible2.value = false;},700)
    } else {
      alarmDataSource.value = [];
      loadingVisible2.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible2.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      console.error(error);
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
// getScannerData();

const scannerFilterLookupName = ref('');
const openScannerPopup = (e) => {
  scannerFilterLookupName.value = '';
  if(e.columnIndex == 0 && e.rowType === 'data'){
    loadingVisible.value = true;
    scannerFilterLookupName.value = e.data.scannerName.toString();
    getScannerLogData(scannerFilterLookupName.value);
  }
};
const popupMostRecentDataSource = ref([]);
const scannerPopupVisible = ref(false);
const getScannerLogData = (scanner) => {
  loadingVisible.value = true;
  axios.get('api/Stats/Recent500', {params: {'Scanner': scanner} }).then(resp=>{
    if(resp.status === 200){
      popupMostRecentDataSource.value = resp.data.data;
      scannerPopupVisible.value = true;
      loadingVisible.value = false;
      //notify(resp.data.message, 'success', 4000);
    } else {
      popupMostRecentDataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      console.error(error);
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
const formatMS = (time) => {
  let seconds = Math.floor((time / 1000)%60);
  let minutes = Math.floor((time)/(1000*60)%60);
  let hours = Math.floor(((time) / (1000*60*60)%60)%60);
  
  hours = (hours < 10) ? "0" + hours : hours;
  minutes = (minutes < 10) ? "0" + minutes : minutes;
  seconds = (seconds < 10) ? "0" + seconds : seconds;

  return hours + ":" + minutes + ":" + seconds;
};
const setSessionInterval = () => {
  getScannerData();
  let timeCalc = sessionTime.value * 60000;
  totTimeMS.value = timeCalc;
  totTimeMSDisplay.value = formatMS(totTimeMS.value);
  timeCalc = timeCalc / refreshTime.value;

  clearInterval(interval.value);
  interval.value = setInterval(() => {
    if(time.value < timeCalc-1){
      getScannerData();
      time.value++;
      totTimeMS.value = totTimeMS.value - refreshTime.value;
      totTimeMSDisplay.value = formatMS(totTimeMS.value);
    }
    else{
      totTimeMS.value = totTimeMS.value = refreshTime.value;
      totTimeMSDisplay.value = formatMS(totTimeMS.value);
      clearInterval(interval.value);
      console.log('device monitor sent it home');
      router.push('/home');
    }
  },refreshTime.value)
};
// setSessionInterval();

const toggleSessionTip = () => {
  sessionTipVisible.value = !sessionTipVisible.value;
};
const toggleRefreshTip = () => {
  refreshTipVisible.value = !refreshTipVisible.value;
};

const handleRefreshTimeChange = (data) => {
  refreshTime.value = data.value;
  setSessionInterval();
};
const handleSessionTimeChange = (data) => {
  sessionTime.value = data.value;
  setSessionInterval();
};
// const alarmClass = (data) => {
//   if (data == 'START') {
//     return 'alarm-on-scan-mon';
//   } else {
//     return 'alarm-off-scan-mon';
//   }
// };
const deviceMonitorAlarmSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ":" + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Scanner Audit Log Last 500 Messages ${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};

onUnmounted(() => {
  //make sure to clear interval on a page leave since this is a single page web app
  //if you do not it will run in the background
  clearInterval(interval.value);
});
</script>

<style lang="scss">
.interval-controls {
  width: 100%;
  padding-bottom: 50px;
  //just adds bottom padding
}
.control-float-left-refresh {
  float: left;
  margin-right: 70px;
}
.control-float-left{
  float: left;
  margin-right: 20px;
}
.toolTipAlignboxMonitor {
  display: flex;
  align-items: center ;
}
.refresh-loading-indicator{
  position: relative;
  left: 170px;
  margin-top: -40px;
  top: -20px;
}
#scannerTitleStyle {
  text-align: center;
  margin-top: 0px;
}
.scannerGridLink {
  cursor: pointer;
  text-decoration: underline;
}
#alarmTitleStyle {
  text-align: center;
  margin-top: 35px;
}
// .alarm-on-scan-mon{
//   background-color: #FF0000;
//   color: white;
//   text-align: center;
//   width: 50%;
// }
// .alarm-off-scan-mon{
//   background-color: #90EE90;
//   color: black;
//   text-align: center;
//   width: 50%;
// }
</style>


<template>
  <div>
    <!-- <h2 class="content-block">User Manager</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          v-model:visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <DxDataGrid
          :data-source="dataSource"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          @row-updated="validateDataGridChanges"
        >
        <DxEditing
          :allow-updating="true"
        >
          <!-- mode="form"
          <DxForm :col-count="2">
      
            <DxSimpleItem data-field="userFirst" caption="First Name" />
            <DxSimpleItem data-field="userLast" caption="Last Name" />
            <DxSimpleItem data-field="userEmail" caption="Email" />
            <DxSimpleItem data-field="userSecurity" caption="Security" editor-type="dxSelectBox"
            :editor-options="secEditor" />

          </DxForm> -->
        </DxEditing>
          <DxColumn
            data-field="user_id_name"
            caption="Username"
            :allow-editing="false"
          />
          <DxColumn
            data-field="user_email"
            caption="Email"
          />
          <DxColumn
            data-field="user_first_name"
            caption="First Name"
          />
          <DxColumn
            data-field="user_last_name"
            caption="Last Name"
          />
          <DxColumn
            data-field="user_security"
            caption="Security"
          >
            <DxLookup 
              :data-source="secEditor.items"
            />
          </DxColumn>
        <DxPaging :page-size="5"/>
        </DxDataGrid>
        </div>
      </div>  
        <h4 class="content-block">User Options</h4>
      <div class="content-block">  
        <div class="dx-card responsive-paddings">
          <DxTabPanel
            id="adminUserTabPan"
            :key="tabReloadKey"
            :height="550"
            :data-source="userOptions"
            :swipe-enabled="false"
            :animation-enabled="false"
            :show-nav-buttons="true"
            :item-template="itemSelected"
            @title-click="handleTitleClick"
          >
          <template #title="{data: userOptions}">
            {{userOptions.title}}
          </template>
          <template #pass>
            <DxForm1
            id="adminTabPanForm"
            :form-data="reset"
            :on-field-data-changed="validateForm"
            >
            <DxGroupItem :col-span="1">
              <DxSimpleItem
                data-field="Username"
                editor-type="dxSelectBox"
                :editor-options="userEditor"
                :validation-rules="validation"
              />
              <DxButtonItem
                horizontal-alignment="left"
                :button-options="resetButton"
              />
            </DxGroupItem>
          </DxForm1>
          </template>
          <template #add>
            <DxForm1
            id="adminTabPanForm"
            :form-data="add"
            :on-field-data-changed="validateForm"
            >
            <DxGroupItem :col-span="3">
              <DxSimpleItem
                data-field="FirstName"
                :validation-rules="validation"
              />
              <DxSimpleItem
                data-field="LastName"
                :validation-rules="validation"
              />
              <DxSimpleItem
                data-field="Email"
                :validation-rules="validation"
              />
              <DxSimpleItem
                data-field="Username"
                :validation-rules="validation"
              />
              <DxSimpleItem
                data-field="Security"
                editor-type="dxSelectBox"
                :editor-options="secEditor"
                :validation-rules="validation"
              />
              <DxButtonItem
                horizontal-alignment="left"
                :button-options="addButton"
              />
            </DxGroupItem>
          </DxForm1>
          </template>
          <template #remove>
            <DxForm1
            id="adminTabPanForm"
            :form-data="remove"
            :on-field-data-changed="validateForm"
            >
            <DxGroupItem :col-span="1">
              <DxSimpleItem  
                data-field="Username"
                editor-type="dxSelectBox"
                :editor-options="userEditor"
                :validation-rules="validation"
              />
              <DxButtonItem
                horizontal-alignment="left"
                :button-options="removeButton"
              />
            </DxGroupItem>  
          </DxForm1>
          </template>
        </DxTabPanel>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxEditing,
  // DxForm
  DxLookup
} from 'devextreme-vue/data-grid';
import DxLoadPanel from 'devextreme-vue/load-panel';
import DxTabPanel from 'devextreme-vue/tab-panel';
import { DxSimpleItem, DxGroupItem, DxButtonItem } from 'devextreme-vue/form';
import DxForm1 from 'devextreme-vue/form';
import notify from 'devextreme/ui/notify';
import axios from 'axios';
import auth from '../auth';
import databaseName from '../myFunctions/databaseName';

// const databaseInfo = ref(Object);
const users = ref([]);
const dataSource = ref([]);
const loadingVisible = ref(false);
const userOptions = ref([
  {id: 0, title: "Reset Password", option: "pass"},
  {id: 1, title: "Add User", option: "add"},
  {id: 2, title: "Remove User", option: "remove"}
]);
const itemSelected = ref("pass");
const reset = ref({});
const resetButton = ref({
  text: 'Reset',
  stylingMode: 'contained',
  type: 'default',
  onClick: ()=>{
    resetUser();
  }
});
const add = ref({});
const addButton = ref({
  text: 'Add',
  stylingMode: "contained",
  type: "default",
  onClick: ()=>{
    addUser();
  }
});
const remove = ref({});
const removeButton = ref({
  text: 'Remove',
  stylingMode: 'contained',
  type: 'default',
  onClick: ()=>{
    removeUser();
  }
});
const userEditor = ref({
  items: users.value,
  searchEnabled: true
});
const secEditor = ref({
  items: ["administrator", "editor", "standard"]
});
const validation = ref([
  {type: 'required', message: 'This field is required'}
]);
//   };
// },
  // created(){
    // this.databaseInfo = databaseName.getPaths();
databaseName.checkWebsiteVersion();
const router = useRouter();
const route = useRoute();

auth.getUser().then(user=>{
  // console.log(user);
  if(user.data.userSecurity === 'administrator' || user.data.userSecurity === 'programmer'){
    getData();
  }
  else{
    alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");
    // router.push('/login-form?redirect=/user-manager');
    router.push({  path: "/login-form",  query: { returnUrl: route.fullPath } });
  }
})
.catch(error=>{
  loadingVisible.value = false;
  //console.log(error.response);
  if(error.response){
    alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else if(error.request){
    alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else{
    alert('Unknown error\nMake sure that you and the server have not lost connection');
    return;
  }
});
// onBeforeUnmount(() => {
//   users.value = [];
// });
const tabReloadKey = ref(0);
const firstLoad = ref(true);
//gets new data
const getData = async () => {
  dataSource.value = [];
  let user = []
  users.value = [];
  loadingVisible.value = true;
  // let generateList = false;
  // if (dataSource.value.length < 1) {
  //   generateList = true;
  // }
  await auth.getUser().then(adminUser=>{
  axios({
        method: 'GET',
        url: 'api/Admin/AllUsers',
        headers:{
          'Authorization': `Bearer ${adminUser.data.userJwtToken}`,
          'Content-Type': 'application/json'
        }
      })
  .then(resp=>{
    let temp = resp.data;
    //console.log(temp);
    if(resp.status !== 200){
      dataSource.value = [];
      notify(temp.message, 'error', 10000);
    }
    else{
      dataSource.value = temp.data;
      loadingVisible.value = false;
      // notify(temp.message, 'success', 4000);
    }
    // if(generateList){
      // users.value = [];
    temp.data.forEach(obj=>{
      user.push(obj.user_id_name);
    });
    userEditor.value.items = user; 
    // }
    loadingVisible.value = false;
    if(firstLoad.value) {
      firstLoad.value = false;
      tabReloadKey.value++;
    } 
  })
  .catch(error=>{
        loadingVisible.value = false;
        //console.log(error.response);
        if(error.response){
          alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else if(error.request){
          alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else{
          alert('Unknown error\nMake sure that you and the serve have not lost connection');
          return;
        }
      });
    })
  .catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      console.error(error);
      alert('Unknown error\nMake sure that you and the serve have not lost connection');
      return;
    }
  });
};
    //validates an submits changes made to users grid
const validateDataGridChanges = async(e) => {
  let tempObj = e.data;
  let nameCheck = /^[a-zA-Z]+$/;
  let emailCheck = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
  if(!nameCheck.test(tempObj.user_first_name) && !nameCheck.test(tempObj.user_last_name)){
    alert('Invalid name in either first name or last name fields');
    //return;
  }
  else if(!emailCheck.test(tempObj.user_email)){
    alert("Invalid email address: "+tempObj.user_email);
    //return;
  }
  else{
    await auth.getUser().then(adminUser=>{
      //const token = Buffer.from(`${adminUser.data.userIdNum}:${adminUser.data.userPass}`, 'utf8').toString('base64');
      const bodyObj = {
        "userEmail": tempObj.user_email,
        "userFirst": tempObj.user_first_name,
        "userLast": tempObj.user_last_name,
        "userSecurity": tempObj.user_security,
        "userIdNum": tempObj.user_id_num
      }

      axios({
        method: 'PATCH',
        url: 'api/Admin/User',
        data: bodyObj,
        headers:{
          'Authorization': `Bearer ${adminUser.data.userJwtToken}`,
          'Content-Type': 'application/json'
        }
      })
      .then(resp=>{
        if(resp.status == 200){
          loadingVisible.value = false;
          notify(resp.data.message, 'success', 4000);
          getData();
        }
        else{
          loadingVisible.value = false;
          notify(resp.data.message, 'error', 10000);
          getData();
        }
      })
      .catch(error=>{
        loadingVisible.value = false;
        //console.log(error.response);
        if(error.response){
          alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else if(error.request){
          alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else{
          alert('Unknown error\nMake sure that you and the serve have not lost connection');
          return;
        }
      });
    })
    .catch(error=>{
      loadingVisible.value = false;
      //console.log(error.response);
      if(error.response){
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the serve have not lost connection');
        return;
      }
    });
  }
};
//handles clicks on the toolbar titles
const handleTitleClick = (data) => {
  itemSelected.value = data.itemData.option;
};
//validates user options
const validateForm = (e) => {
  e.component.validate();
};
//used to reset the users password
const resetUser = async() => {
  let tempObj = reset.value;
  if(typeof tempObj.Username === 'undefined'){
    alert('Did Not change password Invalid username');
    return;
  }
  else{
    await auth.getUser().then(adminUser=>{
      
      const bodyObj = {"userIdName": tempObj.Username};

      axios({
        method: 'PATCH',
        url: 'api/Admin/UserPassword',
        data: bodyObj,
        headers:{
          'Authorization': `Bearer ${adminUser.data.userJwtToken}`,
          'Content-Type': 'application/json'
        }
      })
      .then(resp=>{
        if(resp.status == 200){
          loadingVisible.value = false;
          notify(resp.data.message, 'success', 5000);
          getData();
        }
        else{
          loadingVisible.value = false;
          notify(resp.data.message, 'error', 10000);
          getData();
        }
      })
      .catch(error=>{
        loadingVisible.value = false;
        //console.log(error.response);
        if(error.response){
          alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else if(error.request){
          alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else{
          alert('Unknown error\nMake sure that you and the serve have not lost connection');
          return;
        }
      });
    })
    .catch(error=>{
      loadingVisible.value = false;
      //console.log(error.response);
      if(error.response){
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the serve have not lost connection');
        return;
      }
    });
  }
};
//used to add a new user
const addUser = async() => {
  let tempObj = add.value;
  if(typeof tempObj.FirstName === 'undefined' || typeof tempObj.LastName === 'undefined' || typeof tempObj.Email === 'undefined' || typeof tempObj.Security === 'undefined' || typeof tempObj.Username === 'undefined'){
    notify('Did Not Add User Empty items in the input field', 'warning', 6000);
    //alert('Did Not Add user Empty items in the input field');
    return;
  }
  else{
    let nameCheck = /^[a-zA-Z]+$/;
    let emailCheck = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
    if(!nameCheck.test(tempObj.FirstName) && !nameCheck.test(tempObj.LastName)){
      notify('Invalid name in either first name or last name fields', 'warning', 6000);
      //alert('Invalid name in either first name or last name fields');
      return;
    }
    else if(!emailCheck.test(tempObj.Email)){
      notify('Invalid email address', 'warning', 6000);
      //alert("Invalid email address");
      return;
    }
    else{

      // let tempUsername = createUsername(tempObj.FirstName, tempObj.LastName);

      // console.log(dataSource.value.findIndex(p => p.userIdName == tempObj.Username));
      if(dataSource.value.findIndex(p => p.userIdName == tempObj.Username) > -1){
        notify(`Detected User: ${tempObj.Username} Already Exists.`, 'warning', 6000);
        //alert("Invalid email address");
        return;
      }


      await auth.getUser().then(adminUser=>{
        // const params = new URLSearchParams;
        // params.append('command', 'kvkAdminCreateUser');
        // params.append('username', adminUser.data.userIdName);
        // params.append('password', adminUser.data.userPass);
        // params.append('userIdName', tempObj.Username);
        // params.append('userEmail', tempObj.Email);
        // params.append('userFirst', tempUsername.First);
        // params.append('userLast', tempUsername.Last);
        // params.append('userSecurity', tempObj.Security);

        const bodyObj = {
          "userIdName": tempObj.Username.toString(),
          "userEmail": tempObj.Email.toString(),
          "userFirst": tempObj.FirstName.toString(),
          "userLast": tempObj.LastName.toString(),
          "userSecurity": tempObj.Security.toString()
        };

        axios({
          method: 'PUT',
          url: 'api/Admin/User',
          data: bodyObj,
          headers:{
          'Authorization': `Bearer ${adminUser.data.userJwtToken}`,
          'Content-Type': 'application/json'
          }
        })
        .then(resp=>{
          if(resp.status == 200){
            loadingVisible.value = false;
            notify(resp.data.message, 'success', 5000);
            getData();
          }
          else{
            loadingVisible.value = false;
            notify(resp.data.message, 'error', 10000);
            getData();
          }
        })
        .catch(error=>{
          loadingVisible.value = false;
          //console.log(error.response);
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the serve have not lost connection');
            return;
          }
        });
      });
    }
  }
};
//used to add a new user
const removeUser = async() => {
  let tempObj = remove.value;
  if(typeof tempObj.Username === 'undefined'){
    alert('Did Not delete Invalid username');
    return;
  }
  else{
    await auth.getUser().then(adminUser=>{
      // const params = new URLSearchParams;
      // params.append('command', 'kvkAdminRemoveUser');
      // params.append('username', adminUser.data.userIdName);
      // params.append('password', adminUser.data.userPass);
      // params.append('userIdName', tempObj.Username);

      const bodyObj = {
        "userIdName": tempObj.Username.toString()
      };

      axios({
        method: 'DELETE',
        url: 'api/Admin/User',
        data: bodyObj,
        headers:{
          'Authorization': `Bearer ${adminUser.data.userJwtToken}`,
          'Content-Type': 'application/json'
        }
      })
      .then(resp=>{
        if(resp.status == 200){
            loadingVisible.value = false;
            notify(resp.data.message, 'success', 4000);
            getData();
          }
          else{
            loadingVisible.value = false;
            notify(resp.data.message, 'error', 10000);
            getData();
          }
      })
      .catch(error=>{
        loadingVisible.value = false;
        //console.log(error.response);
        if(error.response){
          alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else if(error.request){
          alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
          return;
        }
        else{
          alert('Unknown error\nMake sure that you and the serve have not lost connection');
          return;
        }
      });
    })
    .catch(error=>{
      loadingVisible.value = false;
      //console.log(error.response);
      if(error.response){
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the serve have not lost connection');
        return;
      }
  });
  }
};
//this function creates a username using the selected first and last name
// const createUsername = (first, last) => {
//   let firstLetter = first[0];
//   firstLetter = firstLetter.toLowerCase();

//   let secondLetter = last[0];
//   secondLetter = secondLetter.toUpperCase();
//   let third = last.substring(1);
//   third = third.toLowerCase();

//   let id = firstLetter + secondLetter + third;
//   let goodUsername = null;
//   let nameFlag = true;
//   let i = 1;

//   //logic for getting username
//   while(nameFlag){
//       goodUsername = checkUsername(id + i);
//       if(typeof goodUsername === "string"){
//           nameFlag = false;
//           break;
//       }
//       else if(goodUsername == false){
//           i++;
//       }
//   }

//   //creating properly formated first name
//   let newFirst = first[0].toUpperCase();
//   let tempFirst = first.substring(1);
//   newFirst = newFirst + tempFirst.toLowerCase();

//   //creating properly formated last name
//   let newLast = last[0].toUpperCase();
//   let tempLast = last.substring(1);
//   newLast = newLast + tempLast.toLowerCase();
//   users.value.push(goodUsername);
//   return {"First": newFirst, "Last": newLast, "ID": goodUsername};
// };
//makes sure there are no duplicates
// const checkUsername = (id) => {

//   let size = users.value.length;
//   for (let i = 0; i < size; i++){
//       if(users.value[i] === id){
//           return false;
//       }
//   }
//   return id;
// };
//returns whatever is sent to it with a capitalized first letter
// const capitalizeFirstLetter = (string) => {
//   return string.charAt(0).toUpperCase() + string.slice(1);
// };
//   }
// }
</script>

<style lang="scss">
#adminUserTabPan{
  width: 100%;
  position: relative;
  margin: auto;
}
#adminTabPanForm{
  width: 50%;
  position: relative;
  margin: 20px auto;
  padding: 20px 10px 10px 10px;
  border: 2px solid #3aafa9; 
  border-radius: 25px;
  height: auto;
}
</style>

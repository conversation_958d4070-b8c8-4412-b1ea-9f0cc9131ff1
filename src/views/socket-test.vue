<script setup>
import { ref, watch, computed, onMounted } from 'vue'
import { useOrderStart } from '@/composables/useOrderStart'

// Initialize the order processing WebSocket with options
const { 
  isConnected, 
  connectionStatus, 
  messages, 
  processOrderMessages,
  sendClientReadyCommand, 
  sendProcessOrderCommand,
  sendEstablishMessage,
  reconnect,
  disconnect,
	connect
} = useOrderStart({
  debug: true, // Enable logging
  reconnectInterval: 5000 // Customize reconnection timing
})

// Order processing
const orderNumber = ref('')

onMounted(() => {
  console.log('OrderProcessingPage mounted - connecting to WebSocket')
  connect()
})

// Compute reversed messages for descending order display (newest first)
const reversedMessages = computed(() => {
  return [...messages].reverse()
})

// Send an order for processing
const processOrder = () => {
  if (orderNumber.value) {
    sendProcessOrderCommand(orderNumber.value)
    orderNumber.value = ''
  }
}

// Monitor connection status changes
watch(connectionStatus, (newStatus, oldStatus) => {
  console.log(`WebSocket status changed from ${oldStatus} to ${newStatus}`)
  
  if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED') {
    // Automatically send ESTABLISH and CLIENT-READY when connection is established
    sendEstablishMessage()
    sendClientReadyCommand()
  }
})
</script>

<template>
  <div class="order-processing-container">
    <div class="status-panel">
      <h3>WebSocket Status</h3>
      <div class="status-indicator" :class="connectionStatus.toLowerCase()">
        {{ connectionStatus }}
      </div>
      
      <div class="actions">
        <button @click="reconnect" :disabled="isConnected">Connect</button>
        <button @click="disconnect" :disabled="!isConnected">Disconnect</button>
        <button @click="sendClientReadyCommand" :disabled="!isConnected">
          Send CLIENT-READY
        </button>
      </div>
    </div>
    
    <div class="order-processing">
      <h3>Process Order</h3>
      <div class="order-form">
        <input 
          v-model="orderNumber" 
          placeholder="Enter order number"
          :disabled="!isConnected" 
        />
        <button @click="processOrder" :disabled="!isConnected || !orderNumber">
          Process Order
        </button>
      </div>
    </div>
    
    <div class="message-log">
      <h3>Message Log ({{ messages.length }} total)</h3>
      <div class="message-list">
        <div v-if="messages.length === 0" class="no-messages">
          No messages received yet
        </div>
        <div v-else v-for="(msg, index) in reversedMessages" :key="index" class="message">
          <div class="message-type">
            {{ msg.msgType }}
            <span v-if="msg.CommandObj" class="command-name">
              - [ {{ msg.CommandObj.cmdName }} ] 
            </span>
            <span class="message-timestamp" v-if="msg._receivedAt">
               {{ new Date(msg._receivedAt).toLocaleTimeString() }}
            </span>
          </div>
          <div class="message-details">
            <pre>{{ JSON.stringify(msg, null, 2) }}</pre>
          </div>
        </div>
      </div>
    </div>

    <div v-if="processOrderMessages.length > 0" class="order-history">
      <h3>Order History</h3>
      <table>
        <thead>
          <tr>
            <th>Order Number</th>
            <th>From Service</th>
            <th>To Service</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(msg, index) in [...processOrderMessages].reverse()" :key="index">
            <td>{{ msg.CommandObj.cmdMessage }}</td>
            <td>{{ msg.msgFromService }}</td>
            <td>{{ msg.msgToService }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<style scoped>
.order-processing-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
}

.status-indicator {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: bold;
  margin-bottom: 1rem;
}

.status-indicator.connected {
  background-color: #4caf50;
  color: white;
}

.status-indicator.connecting,
.status-indicator.reconnecting {
  background-color: #ff9800;
  color: white;
}

.status-indicator.disconnected,
.status-indicator.error {
  background-color: #f44336;
  color: white;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.order-form {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.order-form input {

  padding: 0.5rem;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  padding: 0.5rem 1rem;
  background-color: #2196f3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover:not(:disabled) {
  background-color: #0b7dda;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.message-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 0.5rem;
}

.message {
  margin-bottom: 0.5rem;
  padding: 0.5rem;

  border-radius: 4px;
}

.message-type {
  font-weight: bold;
  margin-bottom: 0.25rem;
}

.command-name {
  font-weight: normal;
  font-style: italic;
}

.no-messages {
  color: #666;
  text-align: center;
  padding: 1rem;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 0.5rem;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f5f5f5;
  font-weight: bold;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  font-size: 0.85rem;
}
</style>
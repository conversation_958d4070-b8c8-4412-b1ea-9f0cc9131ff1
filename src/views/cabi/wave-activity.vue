<template>
  <div class="content-block dx-card responsive-paddings">

    <!-- Search and Sort Controls -->
		<div class="row">
			<DxTextBox v-model="searchTerm" placeholder="Search by wave number or user name..." :show-clear-button="true" width="300px"
				:buttons="[ { name: 'search', location: 'after', options: { icon: 'search', disabled: false } } ]">
			</DxTextBox>

			<div style="flex:auto; text-align: center; display: flex; justify-content: center; align-items: center;">Last Update: {{formatLocalDateTime(lastMessageTimestamp)}}</div>

			<DxButton styling-mode="contained" 
				:text="sortField === 'wave_number' ? (sortDirection === 'asc' ? 'Wave ▲' : 'Wave ▼') : 'Wave'" 
				:type="sortField === 'wave_number' ? 'default' : 'normal'" @click="toggleSort('wave_number')">
			</DxButton>
			<DxButton styling-mode="contained"
				:text="sortField === 'total_tasks' ? (sortDirection === 'asc' ? 'Tasks ▲' : 'Tasks ▼') : 'Tasks'"
				:type="sortField === 'total_tasks' ? 'default' : 'normal'" @click="toggleSort('total_tasks')">
			</DxButton>
			<!-- <DxButton
				styling-mode="contained"
				:text="sortField === 'user_count' ? (sortDirection === 'asc' ? 'Users ▲' : 'Users ▼') : 'Users'"
				@click="toggleSort('user_count')"
				:type="sortField === 'user_count' ? 'default' : 'normal'">
			</DxButton> -->
		</div>

		<div style="flex:auto; text-align: center; display: flex; justify-content: center; align-items: center;">Last Update: {{formatLocalDateTime(lastMessageTimestamp)}}</div>
    <!-- Accordion -->
    <DxAccordion :data-source="filteredWaveData" :multiple="false" :collapsible="true" item-title-template="waveTitle" item-template="waveContent" class="accordion" >

      <!-- Title Template -->
      <template #waveTitle="{ data }">
        <div class="accordion-header">
          <div class="wave-info">
            <span class="accent">{{ data.wave_number }}</span>
            <div class="row">
							<div class="badge" :class="`badge-${data.wave_task_info.wave_type}`">
								{{data.wave_task_info.wave_type.toUpperCase()}}
							</div>              
              <span class="task-count">{{ getTotalTasks(data) }} tasks</span>
              <span class="user-count">{{ data.wave_users.length }} users</span>
            </div>
          </div>
          <div class="wave-status">
						<div>
							<i :class="data.wave_task_info.wave_released_to_putwall ? 'dx-icon-check' : 'dx-icon-warning'"></i>
							{{data.wave_task_info.wave_released_to_putwall ? 'Released' : 'Not Released'}}
						</div>
          </div>
        </div>
      </template>

      <!-- Content Template -->
      <template #waveContent="{ data }">
        <div class="wave-content">
          <DxTabPanel :data-source="getTabData(data)" :loop="false" :animate-enabled="true" item-title-template="tabTitle" item-template="tabContent">

            <!-- Tab Title Template -->
            <template #tabTitle="{ data: tabData }">
              <span>{{ tabData.title.toUpperCase() }}</span>
							<span v-if="tabData.count !== undefined" class="tab-badge"> ({{ tabData.count.toString() }})</span>
            </template>

            <!-- Tab Content Template -->
            <template #tabContent="{ data: tabData }">

							<!-- Tasks/ Counts Content -->
              <div v-if="tabData.id === 'tasks'" class="tasks-content">
								<!-- Task  -->
								<DxTabPanel :swipe-enabled="false" >
									<!-- Replen Tasks -->
									<DxItem v-if="tabData.wave.wave_task_info.replen_task_counts?.length" title="Replen Tasks">
										<DxDataGrid
											:data-source="tabData.wave.wave_task_info.replen_task_counts"
											:show-borders="false"
											:column-auto-width="true"
											:group-panel="{ visible: false }"
											:grouping="{ autoExpandAll: true }" 
											style="max-height: 300px;" >

											<DxColumn dataField="area" caption="Area"  />
											<DxColumn dataField="count" caption="Count"/>

											<DxScrolling :scrolling-mode="'virtual'" />
										</DxDataGrid>
									</DxItem>

									<!-- Putwall Tasks -->
									<DxItem v-if="tabData.wave.wave_task_info.putwall_task_counts?.length" title="Putwall Tasks">
										<DxDataGrid :data-source="tabData.wave.wave_task_info.putwall_task_counts"
											:show-borders="false" :column-auto-width="true" :group-panel="{ visible: false }"
											:grouping="{ autoExpandAll: true }" style="max-height: 300px;">

											<DxColumn dataField="area" caption="Area"  />
											<DxColumn dataField="count" caption="Count"/>

											<DxScrolling :scrolling-mode="'virtual'" />
										</DxDataGrid>
									</DxItem>

									<!-- Putwall Area Counts -->
									<DxItem v-if="tabData.wave.wave_task_info.putwall_area_counts?.length" title="Putwall Area Counts">
										<DxDataGrid :data-source="tabData.wave.wave_task_info.putwall_area_counts"
											:show-borders="false" :column-auto-width="true" 
											:group-panel="{ visible: false }" :grouping="{ autoExpandAll: true }"  style="max-height: 300px;">

											<DxColumn dataField="putwall_area" caption="Putwall Area"  />
											<DxColumn dataField="requested_count" caption="Requested Count"/>
											<DxColumn dataField="actual_count" caption="Actual Count" />

											<DxScrolling :scrolling-mode="'virtual'" />
										</DxDataGrid>
									</DxItem>

									<!-- Singles Picks -->
									<DxItem v-if="tabData.wave.wave_task_info.singles_picks?.length" title="Singles Picks">
										<DxDataGrid :data-source="tabData.wave.wave_task_info.singles_picks"
											:show-borders="false" :column-auto-width="true"
											:group-panel="{ visible: false }" :grouping="{ autoExpandAll: true }" style="max-height: 300px;">
											
											<DxColumn dataField="item_number" caption="Item"  />
											<DxColumn dataField="area" caption="Area" cell-template="areaCellTemplate"/>
											<DxColumn dataField="count" caption="Task Count"  cell-template="countCellTemplate"/>

											<template #areaCellTemplate ="{ data: columnInfo }">
												<div v-for="picks in columnInfo.data.task_counts" :key="picks.area" >
														{{ picks.area }} 
												</div>
											</template>
											<template #countCellTemplate ="{ data: columnInfo }">
												<div v-for="picks in columnInfo.data.task_counts" :key="picks.area" >
														{{ picks.count }} 
												</div>
											</template>

											<DxScrolling :scrolling-mode="'virtual'" />
										</DxDataGrid>
									</DxItem>

									<!-- Shipment Picks -->
									<DxItem v-if="tabData.wave.wave_task_info.shipment_picks?.length" title="Shipment Picks">
										<DxDataGrid :data-source="tabData.wave.wave_task_info.shipment_picks" :show-borders="false"
											:column-auto-width="true" :group-panel="{ visible: false }"
											:grouping="{ autoExpandAll: true }" style="max-height: 300px;">
											
											<DxColumn dataField="shipment_id" caption="Shipment ID"  />
											<DxColumn dataField="task_counts" caption="Area" cell-template="areaCellTemplate"/>
											<DxColumn dataField="count" caption="Count"  cell-template="countCellTemplate"/>

											<template #areaCellTemplate ="{ data: columnInfo }">
												<div v-for="picks in columnInfo.data.task_counts" :key="picks.area" >
													{{ picks.area }} 
												</div>
											</template>
											<template #countCellTemplate ="{ data: columnInfo }">
												<div v-for="picks in columnInfo.data.task_counts" :key="picks.area" >
													{{ picks.count }} 
												</div>
											</template>

											<DxScrolling :scrolling-mode="'virtual'" />
										</DxDataGrid>
									</DxItem>

								</DxTabPanel>
							</div>

              <!-- Users Content tab-->
              <div v-else-if="tabData.id === 'users'" class="users-content">
                <div v-if="tabData.wave.wave_users.length === 0" class="no-users">
									<span>No active users in this wave</span>
                </div>
                
								<div v-else class="users-grid">
									<DxDataGrid
										:data-source="tabData.wave.wave_users"
										:show-borders="false"
										:column-auto-width="true" class="grid-border"
										:group-panel="{ visible: false }"
										:grouping="{ autoExpandAll: true }" 
										style="max-height: 300px;"
										@row-click="onRowClick" >
										
										<DxColumn dataField="user_id_name" caption="Name" width="100"/>
										<DxColumn dataField="disp_login_datetime" caption="Login At" :customizeText="formatDateColumn" width="120"/>
										<DxColumn dataField="disp_last_activity_datetime" caption="Last Activity At" :customizeText="formatDateColumn" width="120"/>
										<DxColumn dataField="area_list" caption="Areas"/>
										<DxColumn caption="Current Pick Task" cell-template="currentPickTaskTemplate" />
										<DxColumn caption="Current Replen Task" cell-template="currentReplenTaskTemplate" />


										<DxMasterDetail :enabled="true" template="detailTemplate" />
										<DxScrolling :scrolling-mode="'virtual'" />
							
										<template #currentPickTaskTemplate ="{ data: columnInfo }">
											<div style="display: flex; flex-direction: column; gap: 5px;" v-if="columnInfo.data.current_pick_task">
												<div>Item: {{ columnInfo.data.current_pick_task.task_item_number }}</div> 
												<div>Location: {{ columnInfo.data.current_pick_task.slotted_location }}</div>
												<!-- Progress: {{ columnInfo.data.current_pick_task.completed_qty }} / {{ columnInfo.data.current_pick_task.requested_qty }} -->
												<div class="progress-bar">
													<div class="progress" :style="`width: ${((columnInfo.data.current_pick_task.completed_qty+1 / columnInfo.data.current_pick_task.requested_qty) * 100)}%;`"></div>
													<div class="progress-bar-content">Comp: {{ columnInfo.data.current_pick_task.completed_qty }} / {{ columnInfo.data.current_pick_task.requested_qty }}</div>
												</div>
											</div>
										</template>

										<template  #currentReplenTaskTemplate ="{ data: columnInfo }">
											<div style="display: flex; flex-direction: column; gap: 5px;" v-if="columnInfo.data.current_replen_task">
												<div>Item: {{ columnInfo.data.current_replen_task.task_item_number }} </div>
												<div>Location: {{ columnInfo.data.current_replen_task.slotted_location }}</div>
												<!-- Progress: {{ columnInfo.data.current_pick_task.completed_qty }} / {{ columnInfo.data.current_pick_task.requested_qty }} -->
												<div class="progress-bar">
													<div class="progress" :style="`width: ${((columnInfo.data.current_replen_task.completed_qty+1 / columnInfo.data.current_replen_task.requested_qty) * 100)}%;`"></div>
													<div class="progress-bar-content">Comp: {{ columnInfo.data.current_replen_task.completed_qty }} / {{ columnInfo.data.current_replen_task.requested_qty }}</div>
												</div>
											</div>
										</template>

										<!-- Detail Template -->
										<template #detailTemplate="{ data: data }">
											<div class="detail-view">
												<table class="table">
													<caption>Activity Log</caption>
													<thead>
														<tr>
															<th>#</th><th style="text-align: center;">Completed</th><th>Type</th><th>Date</th><th>Message</th>
														</tr>
													</thead>
													<tbody>
														<tr v-for="(item, index) in data.data.user_activity_log" :key="index">
															<td>{{ index + 1 }}</td>
															<td  style="text-align: center;"><i :class="item.is_success ? 'dx-icon-check accent' : 'dx-icon-close red'" ></i></td>
															<td>{{ item.transaction_code }}</td>
															<td>{{ item.transaction_datetime }}</td>
															<td>{{ item.transaction_message }}</td>
														</tr>
													</tbody>
												</table>
											</div>
										</template>
									</DxDataGrid>
                </div>
              </div>
            </template>
          </DxTabPanel>
        </div>
      </template>
    </DxAccordion>
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
	import { ref, computed, onMounted, watch } from 'vue'

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import { DxAccordion, DxTotalItem, DxButton, DxForm, DxSimpleItem, DxProgressBar, DxTextBox, DxTextBoxButton} from 'devextreme-vue'
	import { DxDataGrid, DxColumn, DxSorting, DxPaging, DxScrolling, DxMasterDetail} from 'devextreme-vue/data-grid';
	import { DxTabPanel, DxItem } from 'devextreme-vue/tab-panel';

	// Child components

	// Composables
	import { useWaveManagement } from '@/composables/useWaveManagement';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { sendEstablishMessage,sendGetWaveMonitorCommand, connect,connectionStatus, waveMonitorData,lastError, lastMessageTimestamp } = useWaveManagement();

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	onMounted(() => {
		connect();

	});


	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)
	const searchTerm = ref('');
	const sortField = ref('wave_number'); // Default sort by wave number
	const sortDirection = ref('desc'); // Default descending
	const waveData = ref([{"wave_number":"W2025-06-24_000001","wave_task_info":{"wave_number":"W2025-06-24_000001","wave_type":"putwall","wave_released_to_putwall":false,"singles_picks":[],"shipment_picks":[],"putwall_area_counts":[{"putwall_area":"Putwall 02","requested_count":153,"actual_count":0},{"putwall_area":"Putwall 03","requested_count":141,"actual_count":0},{"putwall_area":"Putwall 04","requested_count":142,"actual_count":0},{"putwall_area":"Putwall 05","requested_count":173,"actual_count":0},{"putwall_area":"Putwall 06","requested_count":163,"actual_count":0},{"putwall_area":"Putwall 07","requested_count":133,"actual_count":0},{"putwall_area":"Putwall 08","requested_count":164,"actual_count":0},{"putwall_area":"Putwall 09","requested_count":144,"actual_count":0},{"putwall_area":"Putwall 10","requested_count":172,"actual_count":0},{"putwall_area":"Putwall 11","requested_count":161,"actual_count":0},{"putwall_area":"Putwall 12","requested_count":139,"actual_count":0},{"putwall_area":"Putwall 01","requested_count":160,"actual_count":0}],"replen_task_counts":[{"area":"06","count":8},{"area":"03","count":8},{"area":"05","count":1},{"area":"02","count":6},{"area":"09","count":5},{"area":"01","count":9},{"area":"12","count":2},{"area":"11","count":3}],"putwall_task_counts":[{"area":"01","count":58},{"area":"02","count":63},{"area":"03","count":65},{"area":"05","count":16},{"area":"06","count":76},{"area":"09","count":36},{"area":"10","count":7},{"area":"11","count":55},{"area":"12","count":18}]},"wave_users":[{"user_id_name":"TylerBuoy","disp_login_datetime":"2025-06-28 15:29:19.894","disp_last_activity_datetime":"2025-07-01 10:05:30.545","wave_number":"W2025-06-24_000001","current_pick_task":{"slotted_location":"0224.11.A01","task_item_number":"456664WHTC0","requested_qty":2,"completed_qty":0,"assigned":true,"shipment_id":null,"shipment_type":null,"completed":false,"is_pick":true},"current_replen_task":null,"area_list":["PV","12","11","10","09","08","07","06","05","04","03","02","01"],"user_activity_log":[{"wave_number":"W2025-06-24_000001","is_success":true,"transaction_code":"pick_task","transaction_datetime":"2025-07-01 10:05:30.545","transaction_message":"New Pick Task Issued For Location: 0224.11.A01, Item: 456664WHTC0, QTY: 2"}]}]},{"wave_number":"W2025-06-26_000001","wave_task_info":{"wave_number":"W2025-06-26_000001","wave_type":"blended","wave_released_to_putwall":false,"singles_picks":[{"item_number":"456701NTLR0","task_counts":[{"area":"12","count":10},{"area":"11","count":10}],"wave_number":null,"wave_type":null,"wave_released_to_putwall":false,"singles_picks":null,"shipment_picks":null,"replen_task_counts":null,"putwall_task_counts":null}],"shipment_picks":[{"shipment_id":"112517125","task_counts":[{"area":"01","count":12},{"area":"02","count":9},{"area":"03","count":6},{"area":"09","count":8},{"area":"10","count":2},{"area":"11","count":11},{"area":"12","count":13}],"wave_number":null,"wave_type":null,"wave_released_to_putwall":false,"singles_picks":null,"shipment_picks":null,"replen_task_counts":null,"putwall_task_counts":null}],"replen_task_counts":[{"area":"Ty","count":5},{"area":"11","count":1}],"putwall_task_counts":null},"wave_users":[]}])

	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	const filteredWaveData = computed(() => {
		// First filter by search term
		let filtered = waveData.value;
		if (searchTerm.value) {
			const searchLower = searchTerm.value.toLowerCase();
			filtered = waveData.value.filter(wave => {
				// Search by wave number
				const waveNumberMatch = wave.wave_number.toLowerCase().includes(searchLower);

				// Search by user names
				const userMatch = wave.wave_users && wave.wave_users.some(user =>
					user.user_id_name.toLowerCase().includes(searchLower)
				);

				return waveNumberMatch || userMatch;
			});
		}

		// Then sort the filtered results
		return [...filtered].sort((a, b) => {
			let valueA, valueB;

			if (sortField.value === 'wave_number') {
				valueA = a.wave_number.toLowerCase();
				valueB = b.wave_number.toLowerCase();
			} else if (sortField.value === 'total_tasks') {
				valueA = getTotalTasks(a);
				valueB = getTotalTasks(b);
			} else if (sortField.value === 'user_count') {
				valueA = a.wave_users.length;
				valueB = b.wave_users.length;
			}

			if (sortDirection.value === 'asc') {
				return valueA > valueB ? 1 : -1;
			} else {
				return valueA < valueB ? 1 : -1;
			}
		});
	});



	/*=====================================================================
		FUNCTIONS
	=====================================================================*/


	const getTotalTasks = (wave) => {
		let total = 0
		if (wave.wave_task_info.replen_task_counts) {
			total += wave.wave_task_info.replen_task_counts.reduce((sum, item) => sum + item.count, 0)
		}
		if (wave.wave_task_info.putwall_task_counts) {
			total += wave.wave_task_info.putwall_task_counts.reduce((sum, item) => sum + item.count, 0)
		}
		if (wave.wave_task_info.shipment_picks) {
			total += wave.wave_task_info.shipment_picks.reduce((sum, shipment) =>  sum + shipment.task_counts.reduce((shipSum, task) => shipSum + task.count, 0), 0)
		}
		if (wave.wave_task_info.singles_picks) {
			total += wave.wave_task_info.singles_picks.reduce((sum, single) =>  sum + single.task_counts.reduce((shipSum, task) => shipSum + task.count, 0), 0)
		}
			// wave.wave_task_info.singles_picks.reduce( (sum, single) => sum + (single.task_counts?.length || 0), 0 );
		return total
	}

	const toggleSort = (field) => {
		if (sortField.value === field) {
			// Toggle direction if same field
			sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
		} else {
			// Set new field and default direction based on field type
			sortField.value = field;
			// Default to descending for total_tasks, ascending for others
			sortDirection.value = field === 'total_tasks' ? 'desc' : 'asc';
		}
	};

	const getTabData = (wave) => {
		return [
			{ id: 'tasks', title: 'Task Overview', wave: wave },
			{ id: 'users', title: 'Active Users', count: wave.wave_users.length, wave: wave }
		]
	}

	const onRowClick = (e) => {
		if (e.rowType === 'data') {
			e.component.collapseAll(-1);
			if (e.isExpanded) { e.component.collapseRow(e.key); } else { e.component.expandRow(e.key); }
		}
	}

	const formatDateColumn = (cellInfo) => {
		if (!cellInfo.value) return '';
    const date = new Date(cellInfo.value);
    const options = {
      month: 'numeric', 
      day: 'numeric', 
      hour: 'numeric',
      minute: '2-digit',
			second: '2-digit',
      hour12: true
    };
    return date.toLocaleString('en-US', options).replace(',', '');
  }

	const formatLocalDateTime = (iso) => {
		const dateObj = new Date(iso)
		return dateObj.toLocaleString(undefined, {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: 'numeric',
			minute: '2-digit',
			second: '2-digit',
			hour12: true,
			// timeZoneName: 'short',
		})
	}

	/*=====================================================================
		WATCHERS
	=====================================================================*/


	watch(connectionStatus, (newStatus, oldStatus) => {
		if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED' ) {
			notify({ ...notifyOptions.value, message: 'Connected to server.', type: 'success', displayTime: 500 });
			sendEstablishMessage('WAVE-MONITOR');
			// sendGetWaveMonitorCommand();
		}
		else if (newStatus === 'DISCONNECTED' && oldStatus !== 'DISCONNECTED') {
			notify({ ...notifyOptions.value, message: 'Disconnected from server.', type: 'error', displayTime: 1500 });
		}
	});

	watch(waveMonitorData, (newData, oldData) => {
		console.log('Wave Monitor Data Changed:', newData);
		// Update local state or perform any necessary actions
		waveData.value = newData.reverse();
	});
	
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 15px;
	
	}



	.accent {
		color: $base-accent;
	}

	.row {
		display: flex;
		gap: 10px;
		flex-wrap: wrap;
		justify-content:stretch;
		justify-items: stretch;
	}

	.card {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 20px;
		// flex: auto;
		min-height: 0;
		max-height: 100%;
		display: flex;
		flex-direction: column;
	}

	.card-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}

	.progress-bar {
		width: 100%;           
		height: 16px;    
		background-color: rgba($base-accent, 0.2);
		border-radius: 8px;
		overflow: hidden;
		position: relative;
		color: white;
	}
	.progress-bar-content {
		position: absolute; 
		font-size: 12px; 
		left: 50%; 
		top: 0; 
		transform: translateX(-50%);
	}

	.progress {
		height: 100%;
		background-color: $base-accent;
		width: 0%;
		transition: width 0.3s ease;
	}

	.red {
		color: #df5149!important;
	}
	.orange {
		color: #e97836!important;
	}
	.matthews-yellow {
		color: #C4D92E!important;
	}

	table {
		border-collapse: collapse;
		width: 100%;
		position: sticky;
		top: 0;
		z-index: 1;

		caption {
			font-size: 14px;
			font-weight: 600;
			margin-bottom: 10px;
		}
	}

	th, td {
		border: none;
		padding: 1px 12px;
		text-align: left;
	}

	th {
		// background-color: $base-bg-dark;
	}


	// DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 8px;
		background-color: rgba($base-bg-dark, .65);
		// padding: 0 10px;
		
	}
	
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}

	// DX Tab Panel Overrides
	::v-deep(.dx-tab) {
		// border: 1px solid $base-border-color;
		border-bottom: 0;
		padding: 8px 15px;
		border-radius: 8px 8px 0px 0px;
		border:1px solid #515159;
		border-bottom: 0;
		// margin-top: 5px;
		margin-right: 5px;
	}
	::v-deep(.dx-tabs) {
		max-width: 200px;
		border-radius: 8px 8px 0px 0px;
		// padding-top: 10px;
		padding-left: 10px;
		padding-right: 10px;
		background-color: unset;
		line-height: 1.2;
	}
	::v-deep(.dx-tab-text) {
		font-weight: 400;
			line-height: 1.2;
	}

	::v-deep(.dx-tab.dx-tab-selected) {
		background-color: rgba($base-bg-dark, .65);
		// border: 1px solid $base-border-color;

		border:1px solid #515159;
		border-bottom: 0;
		border-radius: 8px 8px 0px 0px;
		// margin-top: 0px;
	}
	::v-deep(.dx-tab.dx-state-focused) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-hover) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-active) {
		background-color: rgba($base-bg-dark, .65)!important;
	}

	.dx-tabpanel {
		display: block;
		overflow: auto;
		// border: 1px solid #515159;
		border-radius: 8px;
		overflow: visible;
		color: rgba($base-text-color, .65)!important;
	}
	::v-deep(.dx-tabpanel-tabs) {
		overflow-x: auto;
		height: auto;
		display: block;
	}
	::v-deep(.dx-tabs-wrapper ){
		position: relative;
		display: flex;
		// border: 1px solid $base-border-color;
		// border-right: 2px solid $base-border-color;
		border-bottom: 0;
		border-radius: 8px 8px 0px 0px;
		// overflow: scroll;
	}
	::v-deep(.dx-tabpanel-container>.dx-tabpanel-container ) {
		height: 100%!important;
	}
	::v-deep(.dx-multiview-wrapper) {
		padding: 0px;
		background-color: #2f2e38;
		border: 1px solid #515159;
		border-radius: 8px 8px 0px 0px;
	}

	// DX Data Grid Overrides
	.grid-border {
		border-radius: 8px;
		border: 1px solid $base-border-color;
		overflow: hidden;
		// height: 100%;
	}
	::v-deep .dx-datagrid {
		color: rgba($base-text-color, 0.75)!important;
		overflow: hidden!important;
		border-radius: 8px;
		// border: 1px solid $base-border-color;
	}
	.detail-view {
		padding: 15px;
	}
	::v-deep .dx-header-row {
		background-color: rgba($base-bg-dark, .65);
		border-radius: 8px;
	}

	::v-deep .dx-empty-message {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;
	}
	::v-deep .dx-datagrid-rowsview .dx-row {
		border: none;
	}
	::v-deep .dx-data-row {
		cursor: pointer;
	}
	::v-deep .dx-datagrid-rowsview .dx-master-detail-row > .dx-master-detail-cell {
		padding: 0!important;
	}
	::v-deep .dx-datagrid-header-panel {
		padding: 10px!important;
	}
	::v-deep .dx-datagrid-search-panel {
		margin: 0!important;
	}

	// -----------------------------------------------


	
.accordion {
  
	// background-color: rgba($base-bg-dark, 0.3);

	color: rgba($base-text-color, .65)!important;
	overflow: auto;
	height: calc(100vh - 265px);
	box-shadow: none;
	
}

.accordion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 15px;
	background-color: rgba($base-bg-dark, 0.3);
	border: 1px solid $base-border-color;
	border-radius: 8px!important;

	
}

.badge{
  display: flex;
  align-items: center;
  gap: 15px;
}
.badge-putwall {
	// background-color: #C4D92E!important;
	color:#C4D92E!important;
	// padding: 5px;
	// border-radius: 5px;
}
.wave-content {
  padding: 15px;
	border: 1px solid $base-border-color;
	
	border-top: 0!important
}

.wave-status {
padding: 0 20px;
}

/* dx-accordion overrides */
:deep(.dx-accordion) {
  margin: 0!important;
}

::v-deep(.dx-accordion-wrapper) {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
:deep(.dx-accordion-item ) {
  box-shadow: none!important;
	background-color: rgba($base-bg-dark, 0.3);
	// margin: 10px 0;
	border:unset!important;


}
:deep(.dx-accordion-item-opened >.accordion-header) {

margin: 0!important;
		border-radius: 8px 8px 0 0!important;
	
}

:deep(.dx-accordion-item-opened ) {
	border:unset!important;
	margin: 0!important;
}

.tab-badge {
  margin-left: 8px;
}



.tasks-content {
  padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 15px;
}

.task-section {
  margin-bottom: 24px;
}

.task-section h4 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px 0;
  color: #333;
}

.task-grid {
  margin-bottom: 16px;
}

.users-content {
  padding: 16px;
}

.no-users {
  text-align: center;
  padding: 40px;

}

.users-grid {
  display: grid;
  gap: 16px;
}

.user-card {

  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
	
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.user-header h5 {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
}

.current-task {
	background-color: $base-bg;
  border: 1px solid $base-border-color;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
	
}

.current-task h6 {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;

}

.user-info {
  margin-bottom: 16px;
}

.activity-log {
  
  padding-top: 16px;
}

.activity-grid {
  margin-top: 8px;
}

.no-activity {
  text-align: center;
  padding: 20px;

  font-style: italic;
}



</style>
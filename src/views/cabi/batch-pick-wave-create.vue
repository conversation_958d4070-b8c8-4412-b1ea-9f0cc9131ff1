<template>
  <div class="content-block dx-card responsive-paddings">
    
		<div style="display: flex; gap: 1rem;">
<pre>
Open Orders Count By Filter Type:
FIFO Count: 5,321
Priority Counts
Priority 01: 1,324
Priority 03: 324
Priority 06: 3,324
Class of Service Count
FedEx Ground: 2,324
FedEx Next Day: 1,254
Top 5 Highest SKUs
SKU 1234 Black Shirt: 1,432
SKU 4321 Silver Coat: 1,233
SKU 4312 Jeans: 976
SKU 4373 Shoe: 888
SKU 8736 Red Short 876
Singles Order Count: 2,187
Multi Item Order Count: 1,232
</pre>
<pre>
Batch Waves Not Released
Batch #1232 - Date Time
Total Shipments
Total Orders
Total Items
Total SKUs
Put Wall Capacity
Replen or Out of Stock
Batch #3244 - Date Time
Total Shipments
Total Orders
Total Items
Total SKUs
Put Wall Capacity
Replen or Out of Stock
</pre>
</div>

<pre>
Current Batch Creation Information:
Total Shipments: 500
Show some details
10 Priority of 500
Total Orders: 900
Total Items: 2700
Total SKUs: 
Put Wall Capacity Needed: 6 (BIN 40 of 480)
Replenishment or Out of Stock Information

Create Batch Questions:
Select Wave Type:
Put Wall Wave
Bypass Put Wall Wave
Single Item Orders
Multi Item Orders
Both Single and Multi Item Orders
Choose Priority or Null
Choose Class of Service or Null
Choose SKUs or Null
</pre>

  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
  //EXAMPLE: 
	// Vue core
  import { ref, computed, onMounted, watch, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';

	// Composables


	// Child components

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	//EXAMPLE: 
	onMounted(async () => {
		
	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/



	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	//EXAMPLE: 
	const firstName = ref(String);
	const lastName = ref(String);
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)



	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	//EXAMPLE: 
	const fullName = computed(() => {
		return firstName.value + '' + lastName.value
	})

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	//EXAMPLE: 
	watch(firstName, (newData, oldData) => { 

})

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 40px;
	}
</style>
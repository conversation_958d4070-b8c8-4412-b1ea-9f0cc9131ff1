<template>
  <div class="content-block dx-card responsive-paddings">

		<!-- Wave Stats -->
		<OrderStats :orderStatsData="waveData" />

		<!-- Wave Data Grid -->
		<div style="flex: auto; height: 100%; overflow: auto;">
			<DxDataGrid :data-source="waveData.waves" 
				:row-alternation-enabled="true" 
				:show-borders="false" 
				:word-wrap-enabled="true" 
				:column-hiding-enabled="false" 
				:column-auto-width="false"
				@row-click="onRowClick"
				:height="'calc(100vh - 410px)'" 
				class="grid-border">
			
				<!-- <DxColumn v-for="column in (columns || [])" :key="column.field_name" :data-field="column.field_name" :caption="column.caption"/> -->
				<DxColumn data-field="wave_number" caption="Number" />
				<DxColumn data-field="wave_type" caption="Type" cell-template="type"/>
				<DxColumn data-field="wave_status" caption="Status" cell-template="status"/>
				<DxColumn data-field="wave_created_datetime" caption="Created" :width="120" sort-order="desc" sort-index="0" dataType="datetime" :customizeText="formatDateColumn" />
				<DxColumn data-field="wave_released_datetime" caption="Released" :width="120" dataType="datetime" :customizeText="formatDateColumn"/>
				<DxColumn data-field="shipment_count" caption="Shipments" />
				<DxColumn data-field="order_count" caption="Orders" />
				<DxColumn data-field="unique_items_count" caption="Unique Items" />
				<DxColumn data-field="total_qty_count" caption="Qty" />

				<DxColumn cell-template="action" width="150" />
				<DxToolbar>
					<DxItem name="searchPanel" location="before" />
					<DxItem location="after" widget="dxButton" :options="{ text: 'New Wave', type: 'default', stylingMode: 'contained', onClick: onNewWave }" />
				</DxToolbar>

				<DxSelection mode="single"/>
				<DxHeaderFilter :visible="true"/>
				<DxFilterPanel :visible="false"/>
				<DxSearchPanel :visible="true" :highlight-case-sensitive="true" :width="`${isMobile ? '100%' : '300'}`"/>
				<DxMasterDetail :enabled="true" template="detailTemplate" />
				<DxScrolling mode="virtual" />

				<!-- Type Template -->
				<template #type="{data}">
					<div :class="`type-${data.value}`">
						{{data.value.toUpperCase()}}
					</div>
				</template>

				<!-- Status Template -->
				<template #status="{data}">
					<div :class="`status-${data.value}`">
						{{data.value.replace(/([a-z])([A-Z])/g, '$1 $2')}}
					</div>
				</template>

				<!-- Action Button Template -->
				<template #action="{data}">
					<div style="display: flex; justify-content: end; gap: 5px;">

						<template v-if="data.data.wave_type === 'putwall' && data.data.wave_released_to_putwall !== true">
							<div v-if="data.data.wave_status !== 'PickingReleased'">
								<DxButton text="Release Picks" class="action-button orange" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || releaseWave(data, 'picks from wave')" />
							</div>
							<div v-else>
								<DxButton text="Release Wave" class="action-button" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || releaseWaveToPutwall(data)" />
							</div>
						</template>

						<template v-if="data.data.wave_type != 'putwall' && data.data.wave_status !== 'Released'" >
							<DxButton text="Release Wave" class="action-button" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || releaseWave(data, 'wave')" />
						</template>
						
						<!-- Active Status Actions -->
						<!-- <template v-if="data.data.wave_status === 'Active'">
							<DxButton text="Complete" class="action-button" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || completeWave(data)"  />
							<DxButton text="Deactivate" class="action-button yellow" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || deactivateWave(data)" />
						</template> -->

						<!-- Release Picks (only if not released and not PickingReleased) -->
						<!-- <template v-if="data.data.wave_released_to_putwall !== true && data.data.wave_status !== 'PickingReleased' && data.data.wave_status !== 'Released'">
							<DxButton text="Release Picks" class="action-button orange" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || releaseWavePicks(data)" />
						</template> -->

						<!-- Release Wave (only if not released yet) -->
						<template v-if="data.data.wave_released_to_putwall === false && data.data.wave_status !== 'Released'" >
							<!-- <DxButton text="Release Wave" class="action-button" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || releaseWave(data)" /> -->
							<!-- <DxButton text="Remove" class="action-button red" type="default" styling-mode="contained" @click="e => e.event.stopPropagation() || removeWave(data)" /> -->
						</template>

						<!-- N/A Button if nothing else applies -->
						<template v-if="data.data.wave_released_to_putwall === true && data.data.wave_status !== 'Active'">
							<!-- <DxButton text="N/A" type="default" styling-mode="contained" :disabled="true" /> -->
						</template>

					</div>
				</template>

				<!-- Wave Detail Template -->
				<template #detailTemplate="{ data: details }">
					<div class="detail-view">
						<table class="table">
							<thead>
								<tr>
									<th>Single</th><th>Putwall</th><th>Bypass Putwall</th><th>Large Shipments</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>{{ details.data.details.single }}</td>
									<td>{{ details.data.details.putwall }}</td>
									<td>{{ details.data.details.bypass_putwall }}</td>
									<td>{{ details.data.details.large_shipment }}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</template>
			</DxDataGrid>
		</div>

		<DxPopup
			:key="popupID"
			:visible="popupWaveWizardVisible"
			:drag-enabled="false"
			:hide-on-outside-click="false"
			:show-close-button="true"
			:show-title="true"
			title="Create New Wave"
			:onHidden='onPopupHidden'
			:height="isMobile? '100%': '90%'"
			:width="isMobile? '100%': '90%'"
			class="popup-custom">
			
			<template #content>
				<WaveWizard :waveStatsData="waveData" @close-popup="popupWaveWizardVisible = false"/>
			</template>

		</DxPopup>
	</div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
  import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import { DxButton } from 'devextreme-vue/button';
	import { DxDataGrid, DxColumn, DxMasterDetail, DxSelection, DxFilterPanel, DxHeaderFilter, DxSearchPanel, DxToolbar, DxItem ,DxScrolling} from 'devextreme-vue/data-grid';
	import { confirm } from 'devextreme/ui/dialog';
	import { DxPopup } from 'devextreme-vue/popup';

	// Composables
	import { useScreenSize } from '@/composables/useScreenSize';
	import { useWaveManagement } from '@/composables/useWaveManagement';

	// Child components
	import OrderStats from '@/components/order-stats.vue';
	import WaveWizard from '@/components/wave-wizard.vue';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { isMobile } = useScreenSize();
	const { 
		connectionStatus,
		waveMainData, 
		releaseWaveResponse,
		initialize,
		connect,
		sendEstablishMessage,
		sendGetWaveDashboardCommand,
		sendReleaseWaveCommand ,
		sendReleaseWaveToPutwallCommand
}	 = useWaveManagement();

	/*=====================================================================
    In Composition API this is the equivalent of beforeCreate() or beforeMount()
  =====================================================================*/
	
	// Initialize WebSocket connection and send establish message
	// initialize();
	

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	//EXAMPLE: 
	onMounted(async () => {
		// Initialize WebSocket connection and send establish message
		// initialize();
		connect();

	});

	onUnmounted(() => {
		// Clean up WebSocket connection
		// disconnect();
	});

	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/



	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)
	const waveData = ref({});
	const popupWaveWizardVisible = ref(false);
	const popupID = ref(1)
	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/

	
	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	const deactivateWave = (data) => {
		confirm(`Are you sure you want to deactivate wave ${data.data.wave_number}?`, 'Confirm Deactivate').then((dialogResult) => {
			if (dialogResult) {
				showNotification({ success: true, error: '' }, 'Batch Deactivated');
			}
		});
	};

	const completeWave = (data) => {
		confirm(`Are you sure you want to complete wave ${data.data.wave_number}?`, 'Confirm Complete').then((dialogResult) => {
			if (dialogResult) {
				showNotification({ success: true, error: '' }, 'Wave Completed');
			}
		});
	};

	const	releaseWaveToPutwall = (data) => {
		confirm(`Are you sure you want to release wave ${data.data.wave_number}?`, 'Confirm Release').then((dialogResult) => {
			if (dialogResult) {
				sendReleaseWaveToPutwallCommand(data.data.wave_number);
			}
		});
	};

	const releaseWave = (data, type) => {
		console.log(data)
		confirm(`Are you sure you want to release ${type} ${data.data.wave_number}?`, 'Confirm Release').then((dialogResult) => {
			if (dialogResult) {
				sendReleaseWaveCommand(data.data.wave_number);
				
			}
		});
	};

	const removeWave = (data) => {
		confirm(`Are you sure you want to remove wave ${data.data.wave_number}?`, 'Confirm Remove').then((dialogResult) => {
			if (dialogResult) {
				showNotification({ success: true, error: '' }, 'Wave Removed');
			}
		});
	};

	const onRowClick = (e) => {
		console.log(e)
		if (e.rowType === 'data') {
			e.component.collapseAll(-1);
			if (e.isExpanded) { e.component.collapseRow(e.key); } else { e.component.expandRow(e.key); }
		}
	}
	
	const onNewWave = () => {
		popupWaveWizardVisible.value = true;
		// sendEstablishMessage('CREATE-WAVE');
		showNotification({ success: true, error: '' }, 'Open New Wave Popup');
	};

	const onPopupHidden = () => {
		popupWaveWizardVisible.value = false;
		popupID.value++;
		sendGetWaveDashboardCommand();
	};

	const formatDateColumn = (cellInfo) => {
		if (!cellInfo.value) return '';
    const date = new Date(cellInfo.value);
    const options = {
      month: 'numeric', 
      day: 'numeric', 
      hour: 'numeric',
      minute: '2-digit',
			second: '2-digit',
      hour12: true
    };
    return date.toLocaleString('en-US', options).replace(',', '');
  }

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	watch(waveMainData, (newData, oldData) => {
		console.log('Wave Main Data Changed:', newData);
		// Update local state or perform any necessary actions
		if (!popupWaveWizardVisible.value) waveData.value = newData;
	});

	watch(connectionStatus, (newStatus, oldStatus) => {
		if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED' ) {
			notify({ ...notifyOptions.value, message: 'Connected to server.', type: 'success', displayTime: 500 });
			sendEstablishMessage("OVERALL-WAVE-DASHBOARD");
			sendGetWaveDashboardCommand();
		}
		else if (newStatus === 'DISCONNECTED' && oldStatus !== 'DISCONNECTED') {
			notify({ ...notifyOptions.value, message: 'Disconnected from server.', type: 'error', displayTime: 1500 });
		}
	});

	watch(releaseWaveResponse, (newVal) => {
		if(newVal) {
			showNotification({ success: true, error: '' }, `${newVal.wave_number} - Wave Released`);
			
		}
	});

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 15px;
	}

	.accent {
		color: $base-accent;
	}

	
	.detail-view {
		padding: 15px;
	}

	table {
		width: 100%;
		border-collapse: collapse;
		border: none;

		th, td {
			border: none;
			padding: 3px;
			text-align: left;
		}
	}

	.action-button {
		font-size: 11px!important;


		&.red { background-color: #FF5252!important;}
		&.yellow { background-color: #f4b524!important;}
		&.orange { background-color: #e97836!important;}
	}

	.status-NotReleased {
		color: #df5149!important;
	}
	.status-Released {
		color: $base-accent!important;
	}
	.status-Complete {
		color: $base-accent!important;
	}
	.status-PickingReleased {
		color: #e97836!important;
	}

	.type-putwall {
		color: #C4D92E!important;
	}


	// DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;
		
	}
	
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}
	// DX Data Grid Overrides
	::v-deep .dx-datagrid {
		color: rgba($base-text-color, 0.75)!important;
	}

	.grid-border {
		border-radius: 8px;
		border: 1px solid $base-border-color;
		overflow: hidden;
		// height: 100%;
	}

	::v-deep .dx-header-row {
		background-color: rgba($base-bg-dark, .65);
		border-radius: 8px;
	}

	::v-deep .dx-empty-message {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}

	::v-deep .dx-master-detail-row td {
		// background-color:rgba($base-bg-dark, 0.5);
	}

	::v-deep .dx-datagrid-rowsview .dx-row {
		border: none;
	}
	::v-deep .dx-data-row {
		cursor: pointer;
	}
	
	
	::v-deep .dx-datagrid-rowsview .dx-master-detail-row > .dx-master-detail-cell {
		padding: 0!important;
	}

	::v-deep .dx-datagrid-header-panel {
		padding: 10px!important;
	}

	::v-deep .dx-datagrid-search-panel {
		margin: 0!important;
	}
	
</style>
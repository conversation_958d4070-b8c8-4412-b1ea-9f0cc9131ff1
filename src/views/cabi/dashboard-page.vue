<template>
  <div class="content-block dx-card responsive-paddings">

<pre>
-Quick overview of important metrics /info
-Grids, charts, graphs and other visualizations
</pre>
		<DxTabPanel ref="dxTabs" :items="dashTabs" > <!-- item-title-template="titleTemplate" -->
			<DxItem title="Gerneral">

<pre>
	Shipment Totals in KOZ
		Total Shipments in KOZ
		Total Shipments Downloaded to KOZ Today, 08/10
		Total Shipments Downloaded Minus Today, 08/01 - 08/09
		Total Shipments Completed Today, 08/10
		Total Shipments Completed Minus Today, 08/01 - 08/09
		Total Shipments Not Completed
	Order Totals in KOZ
		Total Number Orders
		Total Orders Downloaded to KOZ Today, 08/10
		Total Orders Downloaded Minus Today, 08/01 - 08/09
		Total Orders Shipped
		Total Orders Not Shipped
		Total Orders Multi Items
		Total Orders Singles
		Total Order Items Quantity
		Total Order SKUs Quantity
	KOZ Batch Pick Waves:
		Total Waves Created
		Total Waves Completed
		Total Orders Waved
		Total Orders Waved Completed
		Total Orders Batch Pick In-Progress
	Put Wall: Orders Completed 20 of 50
		Qty Items: Picked 100 of 500
	Bypass Wall: Orders Completed 10 of 60
		Qty Items: Picked 75 of 1000
		
</pre>
<div style="display: flex; gap: 1rem;">
<pre>
Open Orders Count By Filter Type:
FIFO Count: 5,321
Priority Counts
Priority 01: 1,324
Priority 03: 324
Priority 06: 3,324
Class of Service Count
FedEx Ground: 2,324
FedEx Next Day: 1,254
Top 5 Highest SKUs
SKU 1234 Black Shirt: 1,432
SKU 4321 Silver Coat: 1,233
SKU 4312 Jeans: 976
SKU 4373 Shoe: 888
SKU 8736 Red Short 876
</pre>
<pre>
Completed Orders Count By Filter Type:
FIFO Count: 5,321
Priority Counts
Priority 01: 1,324
Priority 03: 324
Priority 06: 3,324
Class of Service Count
FedEx Ground: 2,324
FedEx Next Day: 1,254
Top 5 Highest SKUs
SKU 1234 Black Shirt: 1,432
SKU 4321 Silver Coat: 1,233
SKU 4312 Jeans: 976
SKU 4373 Shoe: 888
SKU 8736 Red Short 876
</pre>
</div>

			</DxItem>
			<DxItem title="KPIs">

<pre>
	Wave Release:
		Average Time:
			Average Time to Pick
			Average Time for Put Away
			Average Time for Packing
			Average Order Total
			Average Order Item Picks
			KOZ Handhelds Used (Users)
			Put Walls Used
			Pack Station Used
		Fastest Wave Time: Wave ID (Click to see Wave Details)
			Fastest Time to Pick
			Fastest Time for Put Away
			Fastest Time for Packing
			Fastest Order Total
			Fastest Order Item Picks
			KOZ Handhelds Used (Users)
			Put Walls Used
			Pack Station Used
		Slowest Wave Time: Wave ID (Click to see Wave Details)
		Slowest Time to Pick
		Slowest Time for Put Away
		Slowest Time for Packing
		Slowest Order Item Picks
		KOZ Handhelds Used (Users)
		Put Walls Used
		Pack Station Used
</pre>
<div style="display: flex; gap: 1rem;">
<pre>
		Put Wall: First Put Away
		Today: 08/10/2025
		Wave Count:
		Total Shipments:
		Total Orders:
		Total Items:
		Put Walls Used
		Daily History: 08/01 - 08/10
		Wave Count:
		Total Shipments:
		Total Orders:
		Total Items:
		Put Walls Used
</pre>
<pre>
		Today: 08/10/2025
		Highest Pack Station Completed:
		Wave Count:
		Total Shipments:
		Total Orders:
		Total Items:
		Users
		Station History By Date:
		Wave Count:
		Total Shipments:
		Total Orders:
		Total Items:
</pre>
</div>
			</DxItem>

	

			<!-- Tab title -->
			<!-- <template #titleTemplate="{ data }">
				<span>{{ data.displayName }}</span>
			</template> -->

			<!-- Tab content -->
			<!-- <template #item="{ data: tab }">
				<div class="dashboard-container" style="margin-top: 15px;">
					<div class="dashboard-grid" :style="gridStyle">
						<template v-for="widget in filteredWidgetsByTab[tab.id] || []" :key="widget.id" >
							<component
								:is="getComponentType(widget.type)"
								:widget-data="widget"
								:data="getDataObject(widget.dataSource)"
								:class="widget.type !== 'statBoxGroup' ? ['dashboard-item', `dashboard-item-${widget.id}`] : ''"
								:style="getItemStyle(widget)"
							/>
						</template>
					</div>
				</div>
			</template> -->
			
		</DxTabPanel>	
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
  //EXAMPLE: 
	// Vue core
  import { ref, computed, onMounted, watch, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import { DxTabPanel, DxItem } from 'devextreme-vue/tab-panel';

	// Composables
	import {useDynamicDash} from '@/composables/useDynamicDash' 
	import auth from '@/auth'

	// Child components
	import ChartWidget from '@/components/dashboard/chart-widget.vue';
	import GridWidget from 	'@/components/dashboard/grid-widget.vue';
	import StatBoxWidget from	'@/components/dashboard/stat-box-widget.vue';
	import StatBoxGroup from '@/components/dashboard/stat-box-group.vue';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { getDemoDashboardConfig, getDemoDashboardData , getDemoDashboardConfigNew} = useDynamicDash();

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	onMounted(async () => {
		const user = await auth.getUser()
		userSecurity.value =user.data.userSecurity;

		await fetchData();
	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/
	//EXAMPLE: 
	const zoomContainer = ref()
	const tabs = ref(['Activity', 'asd'])

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	const notifyOptions = { 
		position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
		animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
	}
	const dashConfig = ref([])
	const dashData = ref([])
	const columns = ref(12);
	const gridGap = ref(16); 
	const maxRowHeight = ref(300);
	const userSecurity = ref()

	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	
	// Determine if the data is in category format or flat array format
	const isNestedFormat = computed(() => {
		return dashConfig.value.length > 0 && 
					Object.prototype.hasOwnProperty.call(dashConfig.value[0],	'data') && 
					Array.isArray(dashConfig.value[0].data);
	});

	// Convert flat array to category format if needed
	const normalizedConfig = computed(() => {
		if (isNestedFormat.value) {
			// Already in nested format, return as is
			return dashConfig.value;
		} else {
			// Convert flat array to default category
			return [{ id: 'default', name: 'default', displayName: 'Dashboard', priority: 1, userSecurity: [], data: dashConfig.value }];
		}
	});

	// Filter dashboard categories based on user security role
	const filteredDashboardCategories = computed(() => {
		return normalizedConfig.value.filter(category => {
			return hasAccess(category.userSecurity);
		});
	});

	// Get tabs with access control and sorting
	const dashTabs = computed(() => {
		const tabs = filteredDashboardCategories.value;
			tabs.sort((a, b) => (a.priority || 999) - (b.priority || 999));
			return tabs
	});

	// Get filtered widgets for all tabs
	const filteredWidgetsByTab = computed(() => {
    const result = {};
    dashTabs.value.forEach(tab => {
        // Filter widgets within each tab based on user security, then sort by priority
        result[tab.id] = (tab.data || [])
            .filter(widget => {
                return hasAccess(widget.userSecurity);
            })
            .sort((a, b) => {
                // Sort by priority (lower numbers = higher priority)
                // Default to 999 if no priority is specified
                const priorityA = a.priority || 999;
                const priorityB = b.priority || 999;
                return priorityA - priorityB;
            });
    });
    return result;
	});

	// Grid style
	const gridStyle = computed(() => {
		return {
			gridTemplateColumns: `repeat(${columns.value}, 1fr)`,
			gridAutoRows: `minmax(100px, ${maxRowHeight.value}px)`,
			gap: `${gridGap.value}px` // Use the reactive gap
		};
	});

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	const filteredWidgetsForTab = (tabId) => {
		return filteredWidgetsByTab.value[tabId] || [];
	};

	const getItemStyle = (widget) => {
		let colSpan;
		const widgetMinWidth = widget.minWidth || 1;
		const maxColumns = 12; //default max columns

		if (columns.value < maxColumns) {
			if (widgetMinWidth >= 4) { 
				colSpan = columns.value;
			} else {
				// For very small widgets, let them keep their original minWidth,
				// but ensure they don't exceed the current available columns.
				colSpan = Math.min(widgetMinWidth, columns.value);
			}
		} else {
			// For the widest layout (e.g., 12 columns),
			// use the widget's specified minWidth, capped by columns.value.
			colSpan = Math.min(widgetMinWidth, columns.value);
		}

		// Ensure colSpan is at least 1 and not more than current columns
		colSpan = Math.max(1, Math.min(colSpan, columns.value));

		const rowSpan = widget.rowSpan || 1;
		
		return { gridColumn: `span ${colSpan}`, gridRow: `span ${rowSpan}` };
	};

	const getComponentType = (type) => {
		const componentMap = {
			'chart': ChartWidget,
			'grid': GridWidget,
			'statBox': StatBoxWidget,
			'statBoxGroup': StatBoxGroup
		};		
		return componentMap[type] || 'div';
	};

	const getDataObject = (key) => {
		if (dashData.value && dashData.value.data) {
			return dashData.value.data[key];
		}
		return [];
	};

	const fetchData = async () => {
		try {
			// fetch dashboard categories (this will be represented as tabs)
			dashConfig.value = await getDemoDashboardConfigNew()
			
			// fetch dashboard widgets (this will be represented as cards)
			dashData.value = await getDemoDashboardData()

		} catch (error) {
			console.error(error);
		}	
	}

	const hasAccess = (roles) => {
		
		if (!roles || roles.length === 0) return true;

		// If user has no roles but access is required, deny access
		if (!userSecurity.value) return false;

		// const user = await auth.getUser()
	
		return roles.includes(userSecurity.value);
	}

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	//EXAMPLE: 
	// watch(firstName, (newData, oldData) => { 

	// })

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 15px;
	}


	// DX Tab Panel Overrides
	::v-deep(.dx-tab) {
		// border: 1px solid $base-border-color;
		border-bottom: 0;
		padding: 8px 25px;
		border-radius: 8px 8px 0px 0px;
	}

	::v-deep(.dx-tabs) {
		width: 200px;
		border-radius: 8px 8px 0px 0px;

	}
	::v-deep(.dx-tab-text) {
		font-weight: 400;
	}

	::v-deep(.dx-tab.dx-tab-selected) {
		background-color: rgba($base-bg-dark, .65);
		// border: 1px solid $base-border-color;
		// border-bottom: 0!important;
		border: 1px solid #515159;
		border-radius: 8px 8px 8px 8px;

	}
	::v-deep(.dx-tab.dx-state-focused) {
		background-color: rgba($base-bg-dark, .65)!important;
		// border: 1px solid #515159;
		border-radius: 8px 8px 8px 8px;
	}

	::v-deep(.dx-tab.dx-state-hover) {
		background-color: rgba($base-bg-dark, .65)!important;
		// border: 1px solid #515159;
		border-radius: 8px 8px 8px 8px;
	}

	::v-deep(.dx-tab.dx-state-active) {
		background-color: rgba($base-bg-dark, .65)!important;
		// border: 1px solid #515159;
		border-radius: 8px 8px 8px 8px;
	}

	.dx-tabpanel {

		display: block;
		overflow: auto;
		// border: 1px solid #515159;
		border-radius: 8px;
	}
	::v-deep(.dx-tabpanel-tabs) {

		overflow-x: auto;
		height: auto;
		display: block;
	}
	::v-deep(.dx-tabs-wrapper ){
		position: relative;
		display: flex;
		// border: 1px solid $base-border-color;
		// border-right: 2px solid $base-border-color;
		border-bottom: 0;
		border-radius: 8px 8px 0px 0px;
		// overflow: scroll;
	
	}
	::v-deep(.dx-tabpanel-container ) {
		height: 100%!important;
	}
	::v-deep(.dx-multiview-wrapper) {
		padding: 0px;
		// background-color: #2f2e38;
    // border-top: 1px solid #515159;
	}

	.dashboard-page {
		width: 100%;
		height: 100%;
		padding: 20px;
		background-color: #f5f7fa;
	}

	.dashboard-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24px;
	}

	.dashboard-header h1 {
		margin: 0;
		color: #333;
		font-size: 24px;
	}

	.dashboard-actions button {
		padding: 8px 16px;
		background-color: #4a6cf7;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.dashboard-actions button:disabled {
		background-color: #a0aec0;
		cursor: not-allowed;
	}

	.dashboard-error {
		padding: 16px;
		background-color: #fee2e2;
		border: 1px solid #f87171;
		border-radius: 4px;
		color: #b91c1c;
		margin-bottom: 20px;
	}

	.dashboard-loading {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200px;
		font-size: 18px;
		color: #6b7280;
	}

	.dashboard-grid {
		display: grid;
		gap: 16px; 
		width: 100%;
		grid-auto-flow: dense; 
	}
	
	.text-color {
		color: rgba($base-text-color, 0.75);
	}

	@media (max-width: 1199px) {
		.dashboard-grid {
			gap: 12px;
		}
	}

	@media (max-width: 767px) {
		.dashboard-grid {
			gap: 8px;
		}
	}

	.dashboard-item {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 20px;
		overflow: hidden;
	}


	// DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;

		
}

	::v-deep(.dx-texteditor-label) {	
		top: -5px!important;
	}

	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}

	// DX Button Overrides
	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 32px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 32px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon ,.dx-button-content {
		min-width: unset!important;
		height: unset!important;
	}

	::v-deep(.dx-button-has-icon ){
		width: unset!important;
		height: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		width: unset;
		height: unset;
	}

</style>
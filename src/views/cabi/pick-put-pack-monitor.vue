<template>
  <div class="content-block dx-card responsive-paddings">
    
		
<pre>
	JIM ELLIS   | Device #1        | Actively Picking | Last Pick | Zone | Date/Time |Batch #
	Tyler Buoy | Device #2        | Actively Picking | Last Pick | Zone | Date/Time |Batch #
	Chris M.     | Device #3        | Actively Picking |Last Pick | Zone | Date/Time |Batch #
	Eugene V.  | Device #4.       | Actively Picking | Last Pick | Zone | Date/Time |Batch #

	Matthews Put Wall Monitoring:
	Put Wall #01 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #02 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #03 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #04 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #05 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #06 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #07 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #08 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #09 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #10 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #11 | Last Scan Date/Time | User   | Item | Bin Locations
	Put Wall #12 | Last Scan Date/Time | User   | Item | Bin Locations
	Color Legend: Based on KOZ Utilizing the Put Wall Areas
	Green Active Put Away Last 5 Minutes
	Yellow Active Put Away within 5-10 Minutes
	Red Not Put Away over 10 minutes

	Pack Monitoring:
	Station #01 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #02 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #03 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #04 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #05 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #06 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #07 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #08 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #09 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #10 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
	Station #11 | Last Action Date/Time | User | Wave | Shipment | Orders | Item Counts
</pre>
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
  //EXAMPLE: 
	// Vue core
  import { ref, computed, onMounted, watch, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';

	// Composables


	// Child components

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	//EXAMPLE: 
	onMounted(async () => {
		
	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/



	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	//EXAMPLE: 
	const firstName = ref(String);
	const lastName = ref(String);
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)



	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	//EXAMPLE: 
	const fullName = computed(() => {
		return firstName.value + '' + lastName.value
	})

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	//EXAMPLE: 
	watch(firstName, (newData, oldData) => { 

})

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 40px;
	}
</style>
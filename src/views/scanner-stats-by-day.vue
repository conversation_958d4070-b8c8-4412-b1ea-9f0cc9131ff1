<template>
  <div>
    <!-- <h2 class="content-block">Scanner Stats By Day</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div :class="showMobileViewOrNot.scanDaySearchContainer">
          <div>
            <strong>Start Date</strong>
            <DxDateBox
              v-model:value="yesterday"
              :min="oneMonthAgo"
              type="date"
              styling-mode="underlined"
              width="75%"
            />
          </div>
          <div>
            <strong>End Date</strong>
            <DxDateBox
              v-model:value="getToday"
              :min="oneMonthAgo"
              type="date"
              styling-mode="underlined"
              width="75%"
            />
          </div>
          <div>
            <DxButton 
              text="Search"
              type="default"
              styling-mode="contained"
              @click="getDailyScannerStats"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxTabPanel
          :key="selectionChangedKey"
          :height="800"
          :data-source="ActiveScannerHeader.ScannerGroups"
          v-model:selected-index="scannerGroupIndex"
          :loop="true"
          :animation-enabled="false"
          :swipe-enabled="true"
          @selection-changed="onTabChanged"
        >
          <template #title="{ data: group }">
            <span>{{group.groupName}}</span>
          </template>
          <template #item="{ data: group }">
            <DxDataGrid
              :height="300"
              :data-source="group.data"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              :column-auto-width="true"
              @cell-click="onScannerDataGridClick"
              @exporting="onExporting"
            >
            <DxExport   
              :enabled="true"
            />
            <DxScrolling mode="virtual"/>
            <DxSorting mode="none"/>
              <DxColumn
                :width="160"
                data-field="scannerName"
                caption="Scanner"
                :fixed="true" 
                fixed-position="left"
                cell-template="scanner-header"
              />
              <DxColumn
                data-field="Date"
                :group-index="0"
              />
              <template #scanner-header="{data}">
                <div class="scannerTitleLink">{{data.text}}</div>
              </template>
              <DxColumn v-for="(val) in ActiveScannerHeader.DefaultScannerStatus" :key="val.name"
                :data-field="val.name"
                :caption="val.caption"
                alignment="center"
              />
              <DxColumn v-for="(val) in group.extraStatuses" :key="val.name"
                :data-field="val.name"
                :caption="val.caption"
                alignment="center"
              />

              <template #data-template="{data}">
                <div class="scannerDataLink">{{data.text}}</div>
              </template>

              <DxSummary>
                <DxTotalItem v-for="(val) in ActiveScannerHeader.DefaultScannerStatus" :key="val.name"
                  :column="val.name"
                  summary-type="sum"
                  display-format="{0}"
                  css-class="gridTotals"
                />
                <DxTotalItem v-for="(val) in group.extraStatuses" :key="val.name"
                  :column="val.name"
                  summary-type="sum"
                  display-format="{0}"
                  css-class="gridTotals"
                />
              </DxSummary>
            </DxDataGrid>
            <h5>***Click Scanner In Grid To Update Chart***</h5>
            <DxChart
              id="scanOneChart"
              :data-source="group.scannersInGroup[group.chartDisplayIndex].data"
              :key="chartKey"
              :title="group.scannersInGroup[group.chartDisplayIndex].scannerName"
            >
              <DxCommonSeriesSettings
                argument-field="Date"
                type="bar"
              >
                <!-- <DxLabel :visible="true">
                  <DxFormat
                    :precision="0"
                    type="fixedPoint"
                  />
                </DxLabel> -->
              </DxCommonSeriesSettings>
              <DxArgumentAxis
                argument-type="date"
                tick-interval="day"
              >
                <DxLabel
                  format="yyyy-MM-dd"
                  :staggering-spacing="10"
                  display-mode="stagger"
                />
              </DxArgumentAxis>
              <DxTooltip
                :enabled="true"
                :content-template="graphToolTipFunctionDailyScans"
              />
              <DxSeries v-for="(val) in ActiveScannerHeader.DefaultScannerStatus" :key="val.value"
                  :value-field="val.name"
                  :name="val.caption"
                />
                <DxSeries v-for="(val) in group.extraStatuses" :key="val.value"
                  :value-field="val.name"
                  :name="val.caption"
                />
              <DxValueAxis 
                :visible="true"
                :auto-breaks-enabled="true"
                :max-auto-break-count="2"
              />
              <DxLegend
                vertical-alignment="bottom"
                horizontal-alignment="center"
              >
                <DxMargin :top="25"/>
              </DxLegend>
            </DxChart>
          </template>
        </DxTabPanel>
      </div>
    </div>

  </div>
</template>

<script setup>


import DxTabPanel from 'devextreme-vue/tab-panel';
import { ref } from 'vue';
import axios from 'axios';
import databaseName from '../myFunctions/databaseName';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import { DxDateBox } from 'devextreme-vue/date-box';
import DxButton from 'devextreme-vue/button';
import notify from 'devextreme/ui/notify';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxChart,
  DxSeries,
  DxCommonSeriesSettings,
  DxLabel,
  // DxFormat,
  DxLegend,
  DxMargin,
  DxValueAxis,
  DxArgumentAxis,
  DxTooltip,
  // DxBreak,
  // DxMinVisualRangeLength,
  DxExport,
} from 'devextreme-vue/chart';
import {
  DxDataGrid,
  DxColumn,
  DxSummary,
  DxTotalItem,
  DxScrolling,
  DxSorting
} from 'devextreme-vue/data-grid';


databaseName.checkWebsiteVersion();

const loadingVisible = ref(false);

//TYLER ADDED VARS
const ActiveScannerHeader = ref({});
ActiveScannerHeader.value = databaseName.getActiveScannerData();
console.log(ActiveScannerHeader.value);

const scannerGroupIndex = ref(0);
const chartKey = ref(0);

const onScannerDataGridClick = (e) =>{
  console.log(e);
  if(e.rowType == "data"){
    if(e.columnIndex == 1){
      let value = e.data.scannerName;
      for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups.length; c++){
        for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup.length; i++){
          if(ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup[i].scannerName == value){
            ActiveScannerHeader.value.ScannerGroups[c].chartDisplayIndex = i;
            console.log(ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup[i]);
            break;
          }
        }
      }
    }
    chartKey.value++;
  }
}

const selectionChangedKey = ref(0);
const onTabChanged = () =>{
  console.log("i was clicked");
  selectionChangedKey.value++;
  console.log(ActiveScannerHeader.value);
  console.log(`tab: ${scannerGroupIndex.value}`);
}


const getDates = (startDate, stopDate) => {
    let dateArray = [];
    let currentDate = startDate;
    while (currentDate <= stopDate) {
        console.log("current=> ", typeof currentDate);
        dateArray.push(formatDatePG(currentDate));
        let date = new Date(currentDate);
        currentDate = date.setDate(date.getDate() + 1);
    }
    return dateArray;
    
}
//END OF TYLER VARS

const isMobile = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunction = () => {
  let scanDaySearchContainer = '';
  if (isMobile) {
    scanDaySearchContainer = '';
  } else {
    scanDaySearchContainer = 'scanDaySearchContainerNotMobile';
  }
  return {
    scanDaySearchContainer,
  }
};
const showMobileViewOrNot = showMobileViewOrNotFunction();

const convertDateMySQL = (date, which) => {
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let year = date.getFullYear();

  let monthString = month.toString();
  if(monthString.length == 1){
    monthString = '0' + monthString;
  }

  let dayString = day.toString();
  if(dayString.length == 1){
    dayString = '0' + dayString;
  }

  let hour = date.getHours();

  let hourString = hour.toString();
  if(hourString.length == 1){
    hourString = '0' + hourString;
  }
  let minutes = date.getMinutes();
  let minutesString = minutes.toString();
  if(minutesString.length == 1){
    minutesString = '0' + minutesString;
  }
  let seconds = date.getSeconds();
  let secondsString = seconds.toString();
  if(secondsString.length == 1){
    secondsString = '0' + secondsString;
  }
  // let realDate = year+"-"+monthString+"-"+dayString+" "+hourString+":"+minutesString+":"+secondsString;
  let realDate = year+"-"+monthString+"-"+dayString;
  // return realDate;
  if (which == 'start') {
    yesterday.value = realDate;
  } else {
    getToday.value = realDate;
  }
};

const formatDatePG = (date) => {
  date = new Date(date);
  console.log(typeof date);
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let year = date.getFullYear();

  let monthString = month.toString();
  if(monthString.length == 1){
    monthString = '0' + monthString;
  }

  let dayString = day.toString();
  if(dayString.length == 1){
    dayString = '0' + dayString;
  }

  let hour = date.getHours();

  let hourString = hour.toString();
  if(hourString.length == 1){
    hourString = '0' + hourString;
  }
  let minutes = date.getMinutes();
  let minutesString = minutes.toString();
  if(minutesString.length == 1){
    minutesString = '0' + minutesString;
  }
  let seconds = date.getSeconds();
  let secondsString = seconds.toString();
  if(secondsString.length == 1){
    secondsString = '0' + secondsString;
  }
  // let realDate = year+"-"+monthString+"-"+dayString+" "+hourString+":"+minutesString+":"+secondsString;
  return year+"-"+monthString+"-"+dayString;
};

const yesterday = ref(new Date());
//const StartValue = ref(new Date());
//StartValue.value.setDate(StartValue.value.getDate() - 1);
yesterday.value.setDate(yesterday.value.getDate() - 1);
const getToday = ref(new Date());
//const EndValue = ref(new Date());
convertDateMySQL(yesterday.value, 'start');
convertDateMySQL(getToday.value, 'end');
const oneMonthAgo = ref(new Date());
oneMonthAgo.value.setMonth(oneMonthAgo.value.getMonth() - 1);


const getDailyScannerStats = async () => {

  loadingVisible.value = true;
  let dailyParams = {
    StartDate: yesterday.value,
    EndDate: getToday.value
  }

  let tmpStart = new Date(yesterday.value);
  let tmpEnd = new Date(getToday.value);
  tmpStart.setDate(tmpStart.getDate() + 1);
  tmpEnd.setDate(tmpEnd.getDate() + 1);

  let dateArr = getDates(tmpStart, tmpEnd);
  console.log(dateArr);
  //TYLER STUFF
  let scannerIndexArray = {};
  for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups.length; i++){
    ActiveScannerHeader.value.ScannerGroups[i].data = [];
    for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup.length; c++){
      ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].data = [];
      scannerIndexArray[ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName] = [i, c];
      for(let x = 0; x < dateArr.length; x++){

        let totalsObj = {};
        totalsObj['scannerName'] = ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName;
        totalsObj['Date'] = dateArr[x];

        totalsObj['scannerName'] = ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName;
        //construct the scanner totals based on the default scanner stats
        for(let x = 0; x < ActiveScannerHeader.value.DefaultScannerStatus.length; x++){
          totalsObj[ActiveScannerHeader.value.DefaultScannerStatus[x].name] = 0;
        }
        //construct the scanner groups extra statuses
        for(let x = 0; x < ActiveScannerHeader.value.ScannerGroups[i].extraStatuses.length; x++){
          totalsObj[ActiveScannerHeader.value.ScannerGroups[i].extraStatuses[x].name] = 0;
        }
        ActiveScannerHeader.value.ScannerGroups[i].data.push(totalsObj);

        //construct the data per scanner per timeslot
        let scannerObj = {};
        scannerObj['Date'] = dateArr[x];
        //construct the scanner totals based on the default scanner stats
        for(let x = 0; x < ActiveScannerHeader.value.DefaultScannerStatus.length; x++){
          scannerObj[ActiveScannerHeader.value.DefaultScannerStatus[x].name] = 0;
        }
        //construct the scanner groups extra statuses
        for(let x = 0; x < ActiveScannerHeader.value.ScannerGroups[i].extraStatuses.length; x++){
          scannerObj[ActiveScannerHeader.value.ScannerGroups[i].extraStatuses[x].name] = 0;
        }
        ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].data.push(scannerObj);

      }
    }
  }
  //END TYLER STUFF

  

  axios.get('api/Stats/ScannerDayStats', { params: dailyParams }).then((resp) => {
    // console.log(resp.data);
    if (resp.status === 200) {
      console.log(resp.data.data);
      for (let i = 0; i < resp.data.data.length; i++) {

        if(typeof scannerIndexArray[resp.data.data[i].stat_group] === 'undefined'){
          console.error(`Scanner: ${resp.data.data[i].stat_group} Does Not Exist In ActiveScannersData OBJECT`);
        }
        else{
          let ScannerGroupsIndex = scannerIndexArray[resp.data.data[i].stat_group][0];
          let scannersInGroupIndex = scannerIndexArray[resp.data.data[i].stat_group][1];
          //find date time index!!!
          let tmpDate = resp.data.data[i].stat_date.split('T')[0];
          let tmpTotalsIndex = ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].data.findIndex(p => p.scannerName == resp.data.data[i].stat_group && p.Date == tmpDate);
          let tmpScannerIndex = ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].scannersInGroup[scannersInGroupIndex].data.findIndex(p => p.Date == tmpDate);

          if(tmpTotalsIndex != -1 && tmpScannerIndex != -1){
            ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].data[tmpTotalsIndex][resp.data.data[i].stat_name] = resp.data.data[i].stat_sum;
            ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].scannersInGroup[scannersInGroupIndex].data[tmpScannerIndex][resp.data.data[i].stat_name] = resp.data.data[i].stat_sum;
          }
          else{
            console.error(`Scanner: ${resp.data.data.stat_group} Issue Finding Indexes For Data Assignment`);
          }

        }

      }

      loadingVisible.value = false;
    } else {
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  })
  .finally(()=>{
    selectionChangedKey.value++;
  })
};

// getDailyScannerStats();

const onExporting = (e) => {
  // let today = new Date();
  // let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  // let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  // let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Scanner Stats by Day ${yesterday.value} to ${getToday.value}.xlsx`);
        });
  });
  e.cancel = true;
};


const graphToolTipFunctionDailyScans = (data) => {
  return `${data.seriesName}: ${data.value}`;
};

</script>

<style lang="scss">
.scanDaySearchContainerNotMobile {
  display: flex;
  align-items: center;
}
</style>

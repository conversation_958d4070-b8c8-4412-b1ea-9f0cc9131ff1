<template>
  <div>
    <!-- <h2 class="content-block">Hospital Pack Station</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div style="margin-top: 15px; display: flex; flex-direction: row; gap: 10px;">
          <DxTextBox placeholder="Enter LPN" width="300px" height="2.5rem" styling-mode="filled" style="margin-bottom: 10px" v-model:value="SearchValue" @enter-key="searchBtnClick" :disabled="disableSearch" /> 
          <DxButton text="Search" type="default" styling-mode="contained" style="height: 2.5rem;" @click="searchBtnClick" :disabled="disableSearch" />
        </div>

        <div v-if="disableSearch == true" style="padding-top: 20px" height="123px">
          <DxDataGrid :key="datagridRefreshKey"  v-model:data-source="SearchData" :column-hiding-enabled="false" :row-alternation-enabled="true" :show-borders="true" :word-wrap-enabled="true"> 
            <DxScrolling mode="virtual" />
            <DxColumn :width="250" data-field="printer_name" caption="Printer" />
            <DxColumn data-field="printable_type" caption="Label Type" />
          </DxDataGrid>

          <div v-if="!printMessageSent">
            <DxButton text="Print Labels" type="default" styling-mode="contained" style="margin-top: 15px" @click="printLabels" :disabled="!disableSearch" /> 
          </div>
          <div v-else>
            <DxButton text="Complete Container" type="success" styling-mode="contained" style="margin-top: 15px; margin-right: 15px" @click="completeContainer" /> 
            <DxButton text="Reprint Labels" type="default" styling-mode="contained" style="margin-top: 15px; margin-right: 15px" @click="printLabels" />
            <DxButton text="Cancel" type="danger" styling-mode="contained" style="margin-top: 15px" @click="completeContainer" /> </div>
        </div>

        <div v-else style="padding-top: 20px">
          <DxDataGrid height="123px" :show-borders="true" :word-wrap-enabled="true">
          </DxDataGrid>
        </div>
      </div>
    </div>

    <h4 class="content-block">Server Connection Status</h4>
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div v-if="webSocketConnected" style="height: 60px">
          <div style="font-size: 20px">
            <div class="client-connected-state-rt-device">Status: {{ webSocketStatusMsg }}</div>
          </div>
        </div>
        <div v-else style="height: 60px">
          <div style="font-size: 20px">
            <div class="client-not-connected-state-rt-device">Status: {{ webSocketStatusMsg }}</div>
            <div style="float: left; padding-left: 5%">Auto Re-Try Connection In: {{ refreshWaitTimeCounter }}</div>
            <div style="float: left; padding-left: 2%">
              <DxLoadIndicator style="margin-bottom: -10px" v-model:visible="loadingVisible2" :height="30" :width="30" />
            </div>
          </div>
        </div>
        <DxButton @click="initWebSocket" :width="250" text="Bypass Auto Reconnect" type="success" styling-mode="contained" style="margin-top: 10px" :disabled="webSocketConnected" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, reactive, toRef, onUnmounted, onMounted, onBeforeUnmount } from 'vue'
  import { useRouter , useRoute} from 'vue-router'
  import auth from '../auth'
  import databaseName from '../myFunctions/databaseName'
  import axios from 'axios'
  import notify from 'devextreme/ui/notify'
  import { DxLoadIndicator } from 'devextreme-vue/load-indicator'
  import DxButton from 'devextreme-vue/button'
  import DxTextBox from 'devextreme-vue/text-box'
  import { DxDataGrid, DxColumn, DxScrolling} from 'devextreme-vue/data-grid'

  /*------------------------------------------------------------
    COMPONENT-LEVEL MAIN SETUP
  ------------------------------------------------------------*/
  /* 
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  */
  const router = useRouter()
  const route = useRoute()
  const { handleError } = useErrorHandling()
  const { checkUserAccess } = useAuth()
  const { initWebSocket, writeCommandMessage, webSocketConnected, webSocketStatusMsg, loadingVisible2, refreshWaitTimeCounter } = useWebSocket(databaseName, notify)
  // const { hospitalDataSource, numberInHospitalLane, hospitalLoadingVisible, getHospitalData } = useHospitalData(handleError)
  const { SearchValue, SearchData, disableSearch, printMessageSent, searchBtnClick, printLabels, completeContainer } = useSearch(writeCommandMessage, handleError)

  /* 
    COMPONENT-LEVEL VARIABLES  
  */
  const datagridRefreshKey = ref(0)

  /* 
    COMPONENT-LEVEL LIFECYCLE HOOKS  
  */
  onMounted(async () => {
    try {
      // check website version  
      databaseName.checkWebsiteVersion();
      // initialize user session
      const userData = await initUserSession();
      // initialize websocket 
      await initWebSocket({ userName: userData.userName, userSecurity: userData.userSecurity });
      // initialize application data
      await initApplicationData();

    } catch (error) {
      console.log("error", error)
      if (error.message === "Access Denied") {
        alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");
        router.push({  path: "/login-form",  query: { returnUrl: route.fullPath } });
      } else {
        console.error("Initialization Error:", error); 
        handleError(error);
      }
    }
  })

  onUnmounted(() => { })

  /* 
    COMPONENT-LEVEL COMPUTED PROPERTIES  
  */
    //  EXAMPLE:
    //  const isConnected = computed(() => webSocketConnected.value !== null)
    
  /* 
    COMPONENT-LEVEL WATCHERS  
  */
    //  EXAMPLE:
    //  watch(webSocketConnected, (newVal, oldVal) => {
    //   console.log(newVal, oldVal)
    //  })

  /* 
    COMPONENT-LEVEL FUNCTIONS
  */
  async function initUserSession() {
    const userData = await checkUserAccess();
    if (!userData) {
      throw new Error("Access Denied");
    }
    return userData;
  }

  async function initApplicationData(){
    // hospital lane data
    // await getHospitalData();
  }

  /*------------------------------------------------------------
    COMPOSABLES - Separate files eventually.
  ------------------------------------------------------------*/
  /*------------------------------------------------------------
    AUTH COMPOSABLE
  ------------------------------------------------------------*/
  function useAuth() {
    const checkUserAccess = async () => {
      const user = await auth.getUser()
      if (["administrator", "programmer", "editor"].includes(user.data.userSecurity)) {
        return {
          userName: user.data.userIdName,
          userSecurity: user.data.userSecurity
        }
      }
      return false
    }
    return { checkUserAccess }
  }

  /*------------------------------------------------------------
    ERROR HANDLING COMPOSABLE
  ------------------------------------------------------------*/
  function useErrorHandling() {
    const handleError = (error) => {
      if (error.response?.status === 500) {
        notify(error.response.data.message, 'error', 10000)
        return
      }

      const message = error.response
        ? `Response failed with status code: ${error.response.status}`
        : error.request
          ? 'Request failed'
          : 'Unknown error'

      alert(`${message}\nMake sure that you and the server have not lost connection`)
    }
    return { handleError }
  }

  /*------------------------------------------------------------
    WEBSOCKET COMPOSABLE
  ------------------------------------------------------------*/

  function useWebSocket(databaseName, notify) {
    /* 
      CONSTANTS
    */
    const WEBSOCKET_RECONNECT_INITIAL_DELAY_MS = 100; // 0.1 minutes
    const WEBSOCKET_RECONNECT_MAX_DELAY_MS = 120000; // 2 minutes
    const WEBSOCKET_RECONNECT_MAX_ATTEMPTS = 10;
    const WEBSOCKET_MESSAGE_TYPE_COMMAND = "COMMAND";
    const WEBSOCKET_MESSAGE_TYPE_ESTABLISH = "ESTABLISH";

    /* 
      STATE MANAGEMENT
    */
    const webSocket = ref(null);
    const intentionalDisconnect = ref(false);
    const webSocketState = reactive({ connected: false, statusMsg: "Not Connected", loading: false });
    const reconnectState = reactive({ attempts: 0, waitTimeCounter: "", timer: null, timeLeftMS: WEBSOCKET_RECONNECT_INITIAL_DELAY_MS});

    /* 
      HELPER FUNCTIONS
    */
    /* CREATE MESSAGE */
    const createEstablishMessage = (userName, userSecurity) => {
      const initWsJsonMsg = databaseName.getWebsocketMessageBase();
      const obj = {
        msgType: WEBSOCKET_MESSAGE_TYPE_ESTABLISH,
        msgToService: "HOST-SERVER",
        EstablishConnObj: {
          userIdName: userName,
          userSecurity: userSecurity,
          message: "connecting for the first time",
          messageGroup: "PACK-STATION",
        }
      }
      return { ...initWsJsonMsg.value, ...obj };
    };

    /* CREATE COMMAND MESSAGE */
    const createCommandMessage = (cmdName, cmdMessage, isObject = false) => {
      const initWsJsonMsg = databaseName.getWebsocketMessageBase();
      const obj = {
        msgType: WEBSOCKET_MESSAGE_TYPE_COMMAND,
        msgToService: "KOZ-CONVEYOR",
        CommandObj: {
          cmdName,
          cmdMessage: isObject ? JSON.stringify(cmdMessage) : cmdMessage,
        }
      }
      return { ...initWsJsonMsg.value, ...obj };
    };

    /* CLEAR TIMER */
    const clearReconnectTimer = () => {
      if (reconnectState.timer) {
        clearInterval(reconnectState.timer);
        reconnectState.timer = null;
      }
    };

    /* UPDATE TIMER */
    const updateReconnectTime = () => {
      reconnectState.timeLeftMS = Math.min(
        WEBSOCKET_RECONNECT_INITIAL_DELAY_MS * Math.pow(2, reconnectState.attempts - 1),
        WEBSOCKET_RECONNECT_MAX_DELAY_MS
      );
    };

    /* UPDATE COUNTDOWN */
    const updateCountdown = () => {
      reconnectState.timeLeftMS -= 1000;
      const minutes = Math.floor((reconnectState.timeLeftMS / (1000 * 60)) % 60);
      const seconds = Math.floor((reconnectState.timeLeftMS / 1000) % 60);
      reconnectState.waitTimeCounter = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      if (reconnectState.timeLeftMS <= 0) {
        clearReconnectTimer();
        // Attempt to reconnect
        if (!webSocket.value) return;
        initWebSocket(); 
      }
    };

    /* START TIMER */
    const startReconnectTimer = () => {
      // Clear any existing timer
      clearReconnectTimer(); 

      reconnectState.timer = setInterval(() => {
        if (reconnectState.timeLeftMS > 0) { 
          updateCountdown();
        } else {
          // Clear when timeLeftMS reaches 0
          clearReconnectTimer(); 
        }
      }, 1000);
    };

    /* 
      WEBSOCKET EVENT HANDLERS
    */
    /*  OPEN WEBSOCKET CONNECTION */
    const handleWebSocketOpen = (credentials) => {
      // Clear timer on successful connection
      clearReconnectTimer(); 
      webSocketState.connected = true;
      reconnectState.attempts = 0;
      webSocketState.statusMsg = "Connected To Server";
      webSocketState.loading = false;

      notify("Connection Attempt Success.", "success", 2500);
      const establishMessage = createEstablishMessage(credentials.userName, credentials.userSecurity);
      webSocket.value?.send(JSON.stringify(establishMessage));
    };

    
    /* PROCESS MESSAGE */
    /*
      Return object from the mapping altering messages is below
      {
        msgNameCalled: 'the MSG that was called that generated the reply',
        success: boolean flag for if the operation was successful,
        message: 'string message that is the reason for what happend OR for success TRUE will be generic accept message',
        canOverwrite: boolean flag if set to true it means the FAILED message can be resent with the FLAG ON to overwrite the value that was preventing the action,
        originalMessage: obj that is the original message the client sent with the request
      }
    */
    const processMessage = (msgJson) => {
      switch (msgJson.msgType) {
        case WEBSOCKET_MESSAGE_TYPE_COMMAND:
          switch (msgJson.CommandObj.cmdName) {
            case "FIXED-PRINTER-PRINT-ALL_ERROR":
              notify(msgJson.CommandObj.cmdMessage, "error", 7500);
              break;
            default:
              console.warn(`Unhandled command: ${msgJson.CommandObj.cmdName}`, msgJson);
          }
          break;
        default:
          console.warn(`Unhandled message type: ${msgJson.msgType}`, msgJson);
      }
    };

    /* HANDLE MESSAGE */
    const handleWebSocketMessage = (evt) => {
      try {
        const msgJson = JSON.parse(evt.data);
        processMessage(msgJson);
      } catch (error) {
        console.error("Message handling error:", error, "Raw data:", evt.data);
      }
    };

    /* HANDLE WEB SOCKET CLOSE */
    const handleWebSocketClose = () => {
      webSocketState.statusMsg = "Connection Closed";
      webSocketState.connected = false;

      // Immediately clear any existing timer
      clearReconnectTimer();

      if (!intentionalDisconnect.value) {
        if (reconnectState.attempts < WEBSOCKET_RECONNECT_MAX_ATTEMPTS) {
          notify("Connection Attempt Failed.", "error", 2500);
          updateReconnectTime();
          startReconnectTimer(); // Start a *new* timer
        } else {
          notify("Max Reconnect Attempts reached, giving up.", "error", 5000);
        }
      }

      webSocketState.loading = false;
      webSocket.value = null;
    };

    /* 
      WEBSOCKET CORE FUNCTIONS
    */
    const initWebSocket = (credentials) => {
      if (!credentials) {
        console.error("Cannot initialize WebSocket without credentials.");
        return;
      }

      reconnectState.attempts++;
      const endpoint = databaseName.getMonitorWebsocketEndpoint();
      webSocket.value = new WebSocket(endpoint.value);
      webSocketState.loading = true;

      webSocket.value.onopen = () => handleWebSocketOpen(credentials);
      webSocket.value.onmessage = handleWebSocketMessage;
      webSocket.value.onclose = handleWebSocketClose;
      webSocket.value.onerror = (error) => {
        console.error("WebSocket error:", error);
      };
    };

    const writeCommandMessage = (cmdName, cmdMessage, isObject = false) => {
      if (!webSocket.value || webSocket.value.readyState !== WebSocket.OPEN) {
        console.error("WebSocket is not connected.  Cannot send message.");
        return;
      }

      const commandMessage = createCommandMessage(cmdName, cmdMessage, isObject);
      try {
        webSocket.value.send(JSON.stringify(commandMessage));
      } catch (error) {
        console.error("Error sending command message:", error);
      }
    };


    /* 
      LYFECYCLE HOOKS
    */
    onBeforeUnmount(() => {
      // Clear the timer on unmount
      clearReconnectTimer(); 
      intentionalDisconnect.value = true;
      try {
        webSocket.value?.close();
      } catch (error) {
        console.error("Error closing WebSocket on unmount:", error);
      }
      webSocket.value = null;
    });

  

    /* 
      RETURNED VALUES
    */
    return {
      initWebSocket,
      writeCommandMessage,
      webSocketConnected: toRef(webSocketState, 'connected'),
      webSocketStatusMsg: toRef(webSocketState, 'statusMsg'),
      loadingVisible2: toRef(webSocketState, 'loading'),
      refreshWaitTimeCounter: toRef(reconnectState, 'waitTimeCounter'),
    };
  }

  /*------------------------------------------------------------
    HOSPITAL DATA COMPOSABLE
  ------------------------------------------------------------*/
  // function useHospitalData(handleError) {
  //   const hospitalDataSource = ref([])
  //   const numberInHospitalLane = ref("")
  //   const hospitalLoadingVisible = ref(false)
  //   const today = ref(new Date().toLocaleDateString())

  //   const getHospitalData = async () => {
  //     hospitalLoadingVisible.value = true
  //     try {
  //       const resp = await axios.get("api/Log/ShippingErrors", { 
  //         params: { date: today.value } 
  //       })
  //       if (resp.status === 200) {
  //         hospitalDataSource.value = resp.data.data
  //         numberInHospitalLane.value = `${hospitalDataSource.value.length} Found`
  //       } else {
  //         hospitalDataSource.value = []
  //         numberInHospitalLane.value = "0 Found"
  //       }
  //     } catch (error) {
  //       handleError(error)
  //     } finally {
  //       hospitalLoadingVisible.value = false
  //     }
  //   }

  //   return {
  //     hospitalDataSource,
  //     numberInHospitalLane,
  //     hospitalLoadingVisible,
  //     getHospitalData
  //   }
  // }

  /*------------------------------------------------------------
    SEARCH COMPOSABLE
  ------------------------------------------------------------*/
  function useSearch(writeCommandMessage, handleError) {
    const SearchValue = ref("")
    const SearchData = ref([])
    const disableSearch = ref(false)
    const printMessageSent = ref(false)

    const searchBtnClick = async () => {
      try {
        const resp = await axios.get(`api/Admin/ExtraPrintables?LPNBarcode=${SearchValue.value}`)
        SearchData.value = resp.data.data
        disableSearch.value = true
      } catch (error) {
        handleError(error)
      }
    }

    const onRowClick = (e) => {
      SearchValue.value = e.data.lpn_barcode
      searchBtnClick()
    }

    const printLabels = () => {
      printMessageSent.value = true
      writeCommandMessage("FIXED-PRINTER-PRINT-ALL", SearchValue.value)
    }

    const completeContainer = () => {
      disableSearch.value = false
      printMessageSent.value = false
      SearchValue.value = ""
    }

    return {
      SearchValue,
      SearchData,
      disableSearch,
      printMessageSent,
      searchBtnClick,
      onRowClick,
      printLabels,
      completeContainer
    }
  }
</script>

<style lang="scss" scoped>
  .row_hover .dx-row:hover {
    cursor: pointer;
  }
</style>
<template>
  <div>
    <!-- <h2 class="content-block">Group Stats By Day</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div :class="showMobileViewOrNot.groupDaySearchContainer">
          <div>
            <strong>Start Date</strong>
            <DxDateBox
              v-model:value="yesterday"
              :min="oneMonthAgo"
              type="date"
              styling-mode="underlined"
              width="75%"
            />
          </div>
          <div>
            <strong>End Date</strong>
            <DxDateBox
              v-model:value="getToday"
              :min="oneMonthAgo"
              type="date"
              styling-mode="underlined"
              width="75%"
            />
          </div>
          <div>
            <strong>Group</strong>
            <DxSelectBox 
              :data-source="ActiveStatsHeader"
              display-expr="caption"
              value-expr="name"
              v-model:value="statGroupSelection"
              styling-mode="underlined"
              style="margin-right: 25px;"
              width="200px"
            />
          </div>
          <div>
            <DxButton 
              text="Search"
              type="default"
              styling-mode="contained"
              @click="getGroupStatsByDay"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxTabPanel
          :key="selectionChangedKey"
          :height="800"
          :data-source="ActiveStatsHeader[statGroupIndex].displayGroups"
          v-model:selected-index="displayGroupIndex"
          :loop="true"
          :animation-enabled="false"
          :swipe-enabled="true"
          @selection-changed="onTabChanged"
        >
          <template #title="{ data: group }">
            <span>{{group.displayName}}</span>
          </template>
          <template #item="{ data: group }">
            <div>
              <DxDataGrid
              :height="300"
              :data-source="group.data"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              :column-auto-width="true"
              @exporting="onExporting"
            >
              <DxExport   
                :enabled="true"
              />
              <DxScrolling mode="virtual"/>
              <DxSorting mode="none"/>
              <DxColumn
                :width="170"
                data-field="Date"
                caption="Date"
                data-type="date"
                :fixed="true" 
                fixed-position="left"
              />
              <DxColumn v-for="(val) in group.statuses" :key="val.name"
                :data-field="val.name"
                :caption="val.caption"
                alignment="center"
              />
              <DxSummary>
                <DxTotalItem v-for="(val) in group.statuses" :key="val.name"
                  :column="val.name"
                  summary-type="sum"
                  display-format="{0}"
                  css-class="gridTotals"
                />
              </DxSummary>
            </DxDataGrid>
            <DxChart
              :data-source="group.data"
              :title="group.displayName"
            >
            <DxTooltip
              :enabled="true"
              :content-template="graphToolTipFunctionByGroup"
            />
              <DxCommonSeriesSettings
                argument-field="Date"
                type="bar"
              >
              </DxCommonSeriesSettings> 
              <DxArgumentAxis
                argument-type="datetime"
                tick-interval="day"
              >
                <DxLabel
                  format="MM-dd-yyyy"
                  :staggering-spacing="10"
                  display-mode="stagger"
                />
              </DxArgumentAxis>
              <DxSeries v-for="(val) in group.statuses" :key="val.name"
                :value-field="val.name"
                :name="val.caption"
              />
              <DxValueAxis 
                :visible="true"
                :auto-breaks-enabled="true"
                :max-auto-break-count="2"
              />
              <DxLegend 
                vertical-alignment="bottom"
                horizontal-alignment="center"
              >
                <DxMargin :top="25"/>
              </DxLegend>
            </DxChart>
            </div>
          </template>
        </DxTabPanel>
      </div>
    </div>
  </div>
</template>

<script setup>
import DxTabPanel from 'devextreme-vue/tab-panel';
import { ref } from 'vue';
import axios from 'axios';
import databaseName from '../myFunctions/databaseName';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import { DxDateBox } from 'devextreme-vue/date-box';
import { DxSelectBox } from 'devextreme-vue/select-box';
import DxButton from 'devextreme-vue/button';
import notify from 'devextreme/ui/notify';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxExport,
  DxSummary,
  DxTotalItem,
  DxScrolling,
  DxSorting
} from 'devextreme-vue/data-grid';
import DxChart, {
  DxSeries,
  DxCommonSeriesSettings,
  DxTooltip,
  // DxCommonAxisSettings,
  DxLabel,
  DxValueAxis,
  // DxBreak,
  // DxFormat,
  DxMargin,
  DxLegend,
  DxArgumentAxis,
} from 'devextreme-vue/chart';

databaseName.checkWebsiteVersion();

const loadingVisible = ref(false);

const isMobile = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunction = () => {
  let groupDaySearchContainer = '';
  if (isMobile) {
    groupDaySearchContainer = '';
  } else {
    groupDaySearchContainer = 'groupDaySearchContainerNotMobile';
  }
  return {
    groupDaySearchContainer,
  }
};
const showMobileViewOrNot = showMobileViewOrNotFunction();

//TYLER STUFF
const ActiveStatsHeader = ref([]);
ActiveStatsHeader.value = databaseName.getActiveStatGroupsData();

const statGroupIndex = ref(0);
const displayGroupIndex = ref(0);

const selectionChangedKey = ref(0);
const onTabChanged = () =>{
  selectionChangedKey.value++;
}

const getDates = (startDate, stopDate) => {
    let dateArray = [];
    let currentDate = startDate;
    while (currentDate <= stopDate) {
        console.log("current=> ", typeof currentDate);
        dateArray.push(formatDatePG(currentDate));
        let date = new Date(currentDate);
        currentDate = date.setDate(date.getDate() + 1);
    }
    return dateArray;
    
}

const formatDatePG = (date) => {
  date = new Date(date);
  console.log(typeof date);
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let year = date.getFullYear();

  let monthString = month.toString();
  if(monthString.length == 1){
    monthString = '0' + monthString;
  }

  let dayString = day.toString();
  if(dayString.length == 1){
    dayString = '0' + dayString;
  }

  let hour = date.getHours();

  let hourString = hour.toString();
  if(hourString.length == 1){
    hourString = '0' + hourString;
  }
  let minutes = date.getMinutes();
  let minutesString = minutes.toString();
  if(minutesString.length == 1){
    minutesString = '0' + minutesString;
  }
  let seconds = date.getSeconds();
  let secondsString = seconds.toString();
  if(secondsString.length == 1){
    secondsString = '0' + secondsString;
  }
  // let realDate = year+"-"+monthString+"-"+dayString+" "+hourString+":"+minutesString+":"+secondsString;
  return year+"-"+monthString+"-"+dayString;
};

//END TYLER STUFF

const convertDateMySQL = (date, which) => {
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let year = date.getFullYear();

  let monthString = month.toString();
  if(monthString.length == 1){
    monthString = '0' + monthString;
  }

  let dayString = day.toString();
  if(dayString.length == 1){
    dayString = '0' + dayString;
  }

  let hour = date.getHours();

  let hourString = hour.toString();
  if(hourString.length == 1){
    hourString = '0' + hourString;
  }
  let minutes = date.getMinutes();
  let minutesString = minutes.toString();
  if(minutesString.length == 1){
    minutesString = '0' + minutesString;
  }
  let seconds = date.getSeconds();
  let secondsString = seconds.toString();
  if(secondsString.length == 1){
    secondsString = '0' + secondsString;
  }
  // let realDate = year+"-"+monthString+"-"+dayString+" "+hourString+":"+minutesString+":"+secondsString;
  let realDate = year+"-"+monthString+"-"+dayString;
  // return realDate;
  if (which == 'start') {
    yesterday.value = realDate;
  } else {
    getToday.value = realDate;
  }
};

const yesterday = ref(new Date());
yesterday.value.setDate(yesterday.value.getDate() - 1);
const getToday = ref(new Date());
convertDateMySQL(yesterday.value, 'start');
convertDateMySQL(getToday.value, 'end');
const oneMonthAgo = ref(new Date());
oneMonthAgo.value.setMonth(oneMonthAgo.value.getMonth() - 1);

const statGroupSelection = ref('');
statGroupSelection.value = ActiveStatsHeader.value[0].name;

const getGroupStatsByDay = () => {

  loadingVisible.value = true;

  for(let c = 0; c < ActiveStatsHeader.value.length; c++){
    if(ActiveStatsHeader.value[c].name == statGroupSelection.value){
      displayGroupIndex.value = 0;
      statGroupIndex.value = c;
    }
    for(let i = 0; i < ActiveStatsHeader.value[c].displayGroups.length; i++){
      ActiveStatsHeader.value[c].displayGroups[i].data = [];
    }
  }
console.log(statGroupSelection.value);
  let groupDaySearchParams = {
    StartDate: yesterday.value,
    EndDate: getToday.value,
    StatGroup: statGroupSelection.value
  }

  let tmpStart = new Date(yesterday.value);
  let tmpEnd = new Date(getToday.value);
  tmpStart.setDate(tmpStart.getDate() + 1);
  tmpEnd.setDate(tmpEnd.getDate() + 1);

  let dateArr = getDates(tmpStart, tmpEnd);
  console.log(dateArr);
  let indexMap = {};

  for(let c = 0; c < ActiveStatsHeader.value[statGroupIndex.value].displayGroups.length; c++){
    ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].data = [];
    
    for(let i = 0; i < dateArr.length; i++){
      let tmpObj = {};
      tmpObj['Date'] = dateArr[i];
      for(let x = 0; x < ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].statuses.length; x++){
        tmpObj[ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].statuses[x].name] = 0;
        indexMap[ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].statuses[x].name] = c;
      }
      ActiveStatsHeader.value[statGroupIndex.value].displayGroups[c].data.push(tmpObj);
    }
  }

  axios.get('api/Stats/ArchiveStats', { params: groupDaySearchParams }).then((resp) => {
    // console.log(resp.data);
    if (resp.status === 200) {
      for (let i = 0; i < resp.data.data.length; i++) {

        if(typeof indexMap[resp.data.data[i].stat_name] === 'undefined'){
          console.error(`UnHandled Stat Name: ${resp.data.data[i].stat_name}`);
        }
        else{
          for(let c = 0; c < ActiveStatsHeader.value[statGroupIndex.value].displayGroups[indexMap[resp.data.data[i].stat_name]].data.length; c++){
            if(resp.data.data[i].stat_date.split('T')[0] == ActiveStatsHeader.value[statGroupIndex.value].displayGroups[indexMap[resp.data.data[i].stat_name]].data[c].Date){
              ActiveStatsHeader.value[statGroupIndex.value].displayGroups[indexMap[resp.data.data[i].stat_name]].data[c][resp.data.data[i].stat_name] += resp.data.data[i].stat_sum;
            }
          }
        }

      }
      loadingVisible.value = false;
    } else {
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  })
  .finally(()=>{
    selectionChangedKey.value++;
  })
};

const graphToolTipFunctionByGroup = (data) => {
  return `${data.seriesName}: ${data.value}`;
};

const onExporting = (e) => {
  // let today = new Date();
  // let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  // let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  // let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `DaysStatGroup${ActiveStatsHeader.value[statGroupIndex.value].name}.xlsx`);
        });
  });
  e.cancel = true;
};

</script>

<style lang="scss">
.groupDaySearchContainerNotMobile {
  display: flex;
  align-items: center;
}
</style>

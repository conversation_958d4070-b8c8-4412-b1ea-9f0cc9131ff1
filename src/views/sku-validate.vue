<template>
		<div ref="contentBlockRef" class="content-block dx-card responsive-paddings" :class="{ 'maximized': isMaximized, 'red-border': isAdminOverride}">
		<DxLoadPanel
			v-model:visible="isLoading"
			shading-color="rgba(0,0,0,0.4)"
			:container="'.content-block'"
			:position="{ of: '.content-block' }"
		/>

		<div  v-if="isMaximized" class="accent" style="position: absolute;top:5px;margin-left: auto; margin-right: auto; left: 0; right: 0; text-align: center;">
			{{ auth._user.userFirst }} {{ auth._user.userFirst }}
		</div>

		<!-- Container controls -->
		<div style="display: flex; flex-direction: row-reverse; gap: 10px; position: absolute;top:10px;right:10px;">
			<div v-if="!isMaximizedFromUrl" class="flex">
				<DxButton class="custom-button" :width="22" :height="22"  v-if="isMaximized" type='default' styling-mode='outlined' icon='collapse'  @click='toggleMaximize' />
				<DxButton class="custom-button" :width="22" :height="22"  v-else type='default' styling-mode='outlined' icon='expandform' @click='toggleMaximize' />
			</div>
			
			<DxButton class="custom-button" :width="22" :height="22" type='default' styling-mode='outlined' icon='plus' @click="zoomContainer.decreaseZoom()" />
			<div style="text-align: center; align-self: center; font-size: x-small;">{{ (100 /currentZoom).toFixed()}}%</div>			
			<DxButton class="custom-button" type='default' :width="22" :height="22" styling-mode='outlined' icon='minus' @click="zoomContainer.increaseZoom()" />
		</div>

		<!-- admin mode msg -->
		<div v-if="isAdminOverride" class="admin-mode-msg">🚨 In admin mode, you get one chance to validate the order before needing to sign in again.</div>

		<!-- "totalQtyValidated === totalQtyPicked ? 'VALID': 'INVALID'" -->
<!--TYLER; do not let them close the popup if its valid we want to force them to re-print or click the complete button-->
		<DxPopup ref='popupValidate' container='.dx-viewport' 
			style="z-index: 9999; padding: 5px;" 
			:width="isMobile? 'auto': '550'"
			:height="isMobile? 'auto': '390'"
			:show-title='true' :hideOnOutsideClick='false' :drag-enabled='true' :show-close-button='lastAPISuccess ? false : true'
			:visible='popupValidateVisible'
			:onHidden='onValHidden' 
			>

			<DxPosition my="center" at="center" of=".container" />

			<DxToolbarItem v-if="!lastAPISuccess" widget="dxButton" toolbar="bottom"
        :options="{icon: ' ', stylingMode: 'contained', text:'Admin Override',  onClick: showAdminLogin, height:'31px'}"
      />
<!--TYLER; tyler needs to add re print all API call here!!!-->
			<DxToolbarItem v-if="lastAPISuccess && !isMobile" widget="dxButton" toolbar="bottom" location="before"
        :options="{icon: ' ', stylingMode: 'contained', text:'Reprint Labels', onClick: onReprintALL, height:'31px'}"
      />
<!--TYLER; tyler needs to add API call here where the act of pressing button unbinds tote instead of doing it automatically upon SKU validation-->
<!--TYLER; added only allowing users to click complete button if the lpn == order number.-->
      <DxToolbarItem v-if="showCompleteButton || (lastAPISuccess && order.lpn_barcode == order.wms_order_number)" ref="completeButton" widget="dxButton" toolbar="bottom" location="after"
        :options="{icon: ' ', stylingMode: 'contained',  text:'Complete Container', onClick: onComplete, height:'31px'}"
      />

<!-- TYLER; Commented out for now going to just have a reprint ALL button for now will add more granular re print control during week
			<DxToolbarItem v-if="lastAPISuccess" widget="dxButton" toolbar="bottom"  location="center"
        :options="{icon: ' ', stylingMode: 'contained', text:'Re Print Additional Labels', onClick:''}"
      />
	-->
			<!-- <DxToolbarItem widget="dxButton" toolbar="bottom" location="after"
        :options="{icon: ' ', stylingMode: 'contained', text:'Close', onClick: ()=> {popupValidateVisible = false}}"
      /> -->

			<template #titleTemplate>
				<div v-if="lastAPISuccess" class="row">
					<div style="font-size: large;" class="accent">VALID</div>
					<div class="text-color row" style="align-items: center;">
						<div>Items: <span class="accent">{{ totalQtyPicked }}</span></div>
						<div>Validated: <span class="accent">{{ totalQtyValidated }}</span></div>
					</div>
					<div style="flex: auto" class="accent"></div>
					<div>
						<!-- <DxButton class="custom-button" type='default' :width="22" :height="22" styling-mode='outlined' icon='close' @click="popupValidateVisible = false" /> -->
					</div>
				</div>

				<div v-else class="row">
					<div style="font-size: large;" class="accent">Invalid</div>
					<div class="text-color row" style="align-items: center;">
						<div>Items: <span class="accent">{{ totalQtyPicked }}</span></div>
						<div>Invalid: <span class="accent">{{ lastAPIErrorData.data?.length || 0}}</span></div>
					</div>
					<div style="flex: auto" class="accent"></div>
					<div>
						<DxButton class="custom-button" type='default' :width="22" :height="22" styling-mode='outlined' icon='close' @click="popupValidateVisible = false" />
					</div>
				</div>
			</template>

			<template #content>
				<div v-if="lastAPISuccess" class="container" style="padding: 10px; gap: 30px;">
					<div class="row">
						<div style="flex: auto;"></div>
						<div class="row" style="align-items: center;">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-qr-code mr-2"><rect width="5" height="5" x="3" y="3" rx="1"></rect><rect width="5" height="5" x="16" y="3" rx="1"></rect><rect width="5" height="5" x="3" y="16" rx="1"></rect><path d="M21 16h-3a2 2 0 0 0-2 2v3"></path><path d="M21 21v.01"></path><path d="M12 7v3a2 2 0 0 1-2 2H7"></path><path d="M3 12h.01"></path><path d="M12 3h.01"></path><path d="M12 16v.01"></path><path d="M16 12h1"></path><path d="M21 12v.01"></path><path d="M12 21v-1"></path></svg>
							<div class="accent" style="font-size: x-large;">Scan Label to Complete</div>
						</div>
						<div style="flex: auto;"></div>
					</div>

					<div class="row" style="align-items: center;">
						<div v-for="(item, index) in validatedPrinterList" :key="index">
							<div>Type: <span class="accent">{{item.printable_type}}</span></div>
							<div>Printer: <span class="accent">{{ item.printer_name }}</span></div>
						</div>

						<div style="flex:auto"></div>
						<div style="flex: auto; justify-items: center;">
							<!-- <div>Carrier</div> -->
							<div class="text-color" :class="carrierColor" style="font-size: x-large; padding: 5px 25px; border-radius: 5px;">{{order.shipping_carrier}}</div>
						</div>
					</div>
					
					<!-- <div class="row">
						<div style="flex: auto; justify-items: center;">
							<div class="accent" style="font-size: xx-large;">Scan Label</div>
						</div>
					</div> -->
					<!-- <div class="row">
						<div style="flex: auto; justify-items: right;">
							<div>Items</div>
							<div> {{ totalQtyPicked }} </div>
						</div>
						<div style="flex: auto; justify-items: left;">
							<div>Validated</div>
							<div> {{ totalQtyValidated }} </div>
						</div>
					</div> -->
					<div class="row">
						<div style="flex:auto">
							<div>Tracking Number</div>
							<DxTextBox ref="inputTrackingNumber" id="inputTrackingNumberID" :width="isMobile? 'auto':'100%'" height="40px" :disabled="isMobile" 
								placeholder="Scan Label" styling-mode="filled" v-model="trackingNumberValue"
								:onEnterKey="(e) => onCheckTrackingNumber()"></DxTextBox>
							<!-- onFetchOrder(e.component.option('value')) -->
						</div>
					</div>

					<!-- <div class="row">
						<div style="flex: auto; justify-items: center;">
							<div>Carrier</div>
							<div class="text-color" :class="carrierColor" style="font-size: x-large; padding: 5px 25px; border-radius: 5px;">{{order.shipping_carrier}}</div>
						</div>
					</div> -->

          <div class="row">
            <!--TYLER ADDED-->
            <!-- <div style="flex: auto;">
              <ul class="error-list">
                <li v-for="(item, index) in validatedPrinterList" :key="index" class="success-item">
                  <span class="error-message">Type: {{item.printable_type}}<br />Printer: {{ item.printer_name }}</span>
                </li>
              </ul>
            </div> -->
          </div>
					<!-- <div class="row" style="flex: auto;">
						<div style="flex: auto; justify-items: center; align-content: end;">
							<div style="display: flex; justify-items: center; gap:20px">
								<DxButton text="Print Label" class="custom-button" style="border-radius: 5px;" styling-mode="contained" type="default"></DxButton>
								<DxButton text="Print Pack Slip" class="custom-button" style="border-radius: 5px;" styling-mode="contained" type="default"></DxButton>
							</div>
						</div>
						
					</div> -->
				</div>
				<!-- INVALID... ERRORS -->
				<div v-else class="container" style="padding: 10px;">
					<div class="row">
						<div style="flex: auto; justify-items: center;">
							<div style="font-size: xx-large; color: #ff4147;">INVALID</div>
						</div>
					</div>
					<div class="row">
					
						<div style="flex: auto;">
							<!-- <div style="font-size: large; color: #ff4147;">Errors:</div> -->
							<ul class="error-list">
								<li v-for="(item, index) in lastAPIErrorData.data" :key="index" class="error-item">
									<svg class="error-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
										<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
									</svg>
									<span class="error-message">{{item.item_number}}: {{ item.error_message }}</span>
								</li>
							</ul>
						</div>
					</div>
					<!-- <div class="row" style="flex: auto;">
						<div style="flex: auto; justify-items: center; align-content: end;">
							<div style="display: flex; justify-items: center; gap:20px">
								<DxButton text="ADMIN OVERRIDE" class="custom-button" style="border-radius: 5px;" styling-mode="contained" type="default"></DxButton>	
							</div>
						</div>
						
					</div> -->
				</div>
			</template>
		</DxPopup>

		<!-- Add admin login popup -->
		<DxPopup
			ref="popupAdminLogin"
			:visible="popupAdminLoginVisible"
			title="Admin Login"
			container='.dx-viewport' 
			:show-title="true"
			:hide-on-outside-click="false"
			:show-close-button="true"
			:onHidden='onAdminHidden'
			:width="isMobile? 'auto': '400'"
			height="300"
		>
		<DxPosition my="center" at="center" of=".container" />
			<template #content>
			
					<div class="login-container">
						<DxTextBox
							v-model="adminCredentials.username"
							label="Username"
							mode="text"
							styling-mode="filled"
							:input-attr="{
								autocomplete: 'new-text',      
							}"
						/>
						<DxTextBox
							v-model="adminCredentials.password"
							label="Password"
							mode="password"
							styling-mode="filled"
							:input-attr="{
								autocomplete: 'new-password',
							}"
						/>
						<!-- <input type="text" name="Username" autocomplete="nope" v-model="adminCredentials.username">
						<input type="password" name="Password" autocomplete="new-password" v-model="adminCredentials.password"> -->

						<DxButton
							text="Login"
							type="default"
							styling-mode="contained"
							@click="handleAdminLogin"
						/>
					</div>

			</template>
		</DxPopup>

		<DxPopup
			:visible="imagePopupVisible"
			:show-title="false"
			:hide-on-outside-click="true"
			:show-close-button="true"
			@hidden="closeImagePopup"
			width="auto"
			height="auto"
			container=".app"
		>
			<DxPosition my="center" at="center" of=".app" />
			<template #content>
				<div class="image-lightbox">
					<img :src="currentImageSrc" style="max-width: 90vw; max-height: 80vh;" />
				</div>
			</template>
		</DxPopup>

		<ZoomableContainer ref="zoomContainer" :hide-controls="true" class="zoomContainer">

			<!-- Connect to station view -->
			<div v-if="!isConnected" style="display: flex; flex: auto; position: relative; justify-content: center; align-items: center;">
				<div class="card" style="z-index: 2; padding: 20px 40px; background-color: #31303A; width: fit-content; height: auto; transform: scale(1); font-size: small; box-shadow: 2px 4px 7px 1px rgba(0,0,0,0.25);">
					
					<div style="border-bottom: 1px solid #515159; margin-bottom: 20px;">Connect to a station</div>

					<div style="margin-left: 3px;">Select station</div>
					<div style="display: flex; gap: 8px;">
						<!-- <DxSelectBox style="border-radius: 5px;" styling-mode="filled" display-expr="disp"  -->
						<DxSelectBox style="border-radius: 5px;" styling-mode="filled"
							:data-source="stations" 
							v-model="selectedStation">
						</DxSelectBox>

						<DxButton text="Connect" class="custom-button" @click="onStationConnect()" style="border-radius: 5px;	height: 32px;" styling-mode="contained" type="default" ></dxButton>
					</div>
				</div>
			</div>

			<!-- Connected view -->
			<div v-else class="container" style="position: relative;" :style="isMobile ? 'margin-top: 20px;': 'margin-top: 5px;'">

				<div class="row" :class="{ 'mobile-row': isMobile  }">

					<!-- station login -->
					<div>
						<div style="flex: 1 1 100%; margin-left: 3px;">Station</div>
						<div style="display:flex; flex-wrap: nowrap; ">
							<DxTextBox :width="100" style="border-radius: 5px 0 0 5px;" :disabled="true" styling-mode="filled" :value="selectedStation"></DxTextBox>
							<dxButton class="custom-button" v-if="isConnected" text="DISC" style="border-radius: 0 5px 5px 0;	height: 31px;" styling-mode="contained" type="default" 
								@click="onStationDisconnect" >
							</dxButton>
						</div>
					</div>
					
					<!-- barcode scan -->
					<div style="display:flex; flex-direction:column; flex: auto;" :style="isMobile ? 'flex:auto':'flex:none'">
						<div style="flex: 1 1 100%; margin-left: 3px;">Scan Barcode</div>
						<DxTextBox ref="inputBarcode" :width="isMobile? 'auto':'170px'" :disabled="isMobile" 
							:placeholder="isMobile? 'Scan Barcode': 'Scan or Enter Barcode'" styling-mode="filled" v-model="barcodeValue"
							:onEnterKey="(e) => onFetchOrder(e.component.option('value'))"></DxTextBox>
						<!-- <DxTextBox ref="inputBarcode" :width="isMobile? 'auto':'170px'" placeholder="Scan or Enter Barcode" styling-mode="filled" value="25022213133970_10_LPN"
						:onEnterKey="(e) => onFetchOrder(e.component.option('value'))"></DxTextBox> -->
					</div>

					<!-- tracking number -->
					<!-- <div style="display:flex; flex-direction:column;">
						<div style="flex: 1 1 100%; margin-left: 3px;">Tracking Number</div>
						<DxTextBox :width="190" styling-mode="filled" :disabled="true" value="1Z374859201947365802WX"></DxTextBox>
					</div> -->

					<!-- Carrier -->
					<div style="display:flex; flex-direction:column;">
						<div style="flex: 1 1 100%; margin-left: 3px;">Carrier</div>
						<DxTextBox :width="60" styling-mode="filled" :disabled="true" :value="order.shipping_carrier"></DxTextBox>
					</div>

					<!-- item count -->
					<div style="display:flex; flex-direction:column;">
						<div style="flex: 1 1 100%; margin-left: 3px;">Items</div>
						<DxTextBox styling-mode="filled" style="width: 50px;" :disabled="true" :value='totalQtyPicked'></DxTextBox>
					</div>

					<!-- validated count -->
					<div style="display:flex; flex-direction:column;">
						<div style="flex: 1 1 100%; margin-left: 3px;">Valid</div>
						<DxTextBox  styling-mode="filled" style="width: 50px;" :disabled="true" :value='totalQtyValidated'></DxTextBox>
					</div>

					<!-- clear action-->
					<div style="display:flex; flex-direction:column; justify-content: flex-end;">
						<DxButton text="Clear" class="custom-button" style="border-radius: 5px;" styling-mode="contained" type="default"
							@click="onClear()">
						</DxButton>
					</div>

					<!-- space fill -->
					<div v-if="!isMobile" style="flex:auto"></div>

					<!-- validate action-->
					<div style="display:flex; flex-direction:column; justify-content: flex-end;" :style="isMobile ? 'flex:auto':'flex:none'">
						<DxButton text="Validate" class="custom-button" style="border-radius: 5px;" styling-mode="contained" type="default" :disabled="!order.wms_order_number"
							@click="onValidate">
						</DxButton>
					</div>

				</div>
				<!-- data table -->
				<div>
					<!-- <div>This will be hidden</div>
					<DxTextBox text="Scanned item sku" :width="165" :disabled="true" styling-mode="filled"></DxTextBox> -->
					
					<div style="display: flex;">
						<div class="tab">Order Detail - <span class="accent">{{ order.wms_order_number}}</span></div>
					</div>

					<!-- Mobile-friendly card layout for product data -->
					<div v-if="isMobile" class="mobile-wrap">
						<!-- Cards for mobile view -->
						<div v-for="(item) in paginatedItems || []" :key="item.sku" class="product-card" :class="{ 'valid-row': item.qty_validated === item.actual_qty, 'invalid-row': item.qty_validated > item.actual_qty }">
							
							<!-- Product header with image -->
							<div class="product-header">
								<a class="product-image" @click="openImagePopup(item.img_link)">
									<img :src="item.img_link || defaultImage" alt="Product image">
								</a>
								<div class="product-id">
									<div><span class="accent">SKU: </span>{{ item.sku }}</div>
									<div><span class="accent">Item #: </span>{{ item.item_number }}</div>
									<!-- Product description -->
									<div class="product-description">{{ item.description }}</div>
								</div>
							</div>
							
							<!-- Product data grid -->
							<div class="product-data">
								<div class="row">
									<div class="accent">Pick Status:</div>
									<div class="data-value">{{ item.completed_code }}</div>
								</div>

								<div class="row">
									<div class="row">
										<div class="accent">Qty Expected:</div>
										<div class="data-value">{{ item.expected_qty }}</div>
									</div>

									<div class="row">
										<div class="accent">Qty Picked:</div>
										<div class="data-value">{{ item.actual_qty }}</div>
									</div>

									<div class="row">
										<div class="accent">Qty Valid:</div>
										<div class="data-value"  :style="{ 'color': item.qty_validated > item.actual_qty ? 'red': item.qty_validated < item.actual_qty && item.qty_validated != 0 ? 'yellow': ''}">
											{{ item.qty_validated }}
										</div>
									</div>

								</div>

								
							</div>

							<!-- Action buttons -->
							<div class="product-actions">
								<DxButton 
									text="-" 
									class="custom-button" 
									@click="decreaseValidated(item.line_id)" 
									:disabled="item.qty_validated <= 0" 
									styling-mode="contained" 
									type="default">
								</DxButton>
								<DxButton 
									text="+" 
									class="custom-button" 
									@click="increaseValidated(item.line_id)" 
									styling-mode="contained" 
									type="default">
								</DxButton>
							</div>
						</div>
					</div>

					<table v-if="!isMobile">
						<thead>
							<tr>
								<th style="text-align: center;">Image</th>
								<th>SKU</th>
								<th>Item Number</th>
								<th>Description</th>
								<th>Pick Status</th>
								<th>Qty Expected</th>
								<th>Qty Picked</th>
								<th>Qty Validated</th>
								<th></th>
							</tr>
						</thead>
						<tbody>
							<tr v-for="(item) in paginatedItems" :key="item.sku" style="height: 83px;" :class="{ 'valid-row': item.qty_validated === item.actual_qty, 'invalid-row': item.qty_validated > item.actual_qty }" >
								<td width="60">
									<a style="display: block; cursor: pointer;" @click="openImagePopup(item.img_link)">
										<img 
											:src="item.img_link || defaultImage" alt="Product image"
											style="width: 50px; height: 50px; border-radius: 100%; background-color: #2B2A33;">
									</a>
								</td>
								<td width="20%">{{ item.sku }}</td>
								<td width="20%">{{ item.item_number }}</td>
								<td width="70%" style="min-width:200px">{{ item.description }}</td>
								<td >{{ item.completed_code }}</td>
								<td>{{ item.expected_qty }}</td>
								<td>{{ item.actual_qty }}</td>
								<td :style="{ 'color': item.qty_validated > item.actual_qty  ? 'red': item.qty_validated < item.actual_qty &&  item.qty_validated != 0 ? 'yellow': ''}">{{ item.qty_validated }}</td>
								<td>
									<div style="display: flex; gap:10px;white-space: nowrap;">
										<DxButton text="-" class="custom-button" @click="decreaseValidated(item.line_id)" :disabled="item.qty_validated <= 0" styling-mode="contained" type="default" ></dxButton>
										<DxButton text="+" class="custom-button" @click="increaseValidated(item.line_id)"  styling-mode="contained" type="default" ></dxButton>
									</div>
								</td>
							</tr>
						</tbody>
					</table>

					<!-- paging controls -->
					<div v-if="!isMobile" class="paging-control">
						<DxButton 
							icon="chevronleft"
							@click="currentPage--" 
							:disabled="currentPage === 1"
							styling-mode="outlined" 
							type="default"
						/>
						<span>Page {{ currentPage }} of {{ totalPages }}</span>
						<DxButton 
							icon="chevronright"
							@click="currentPage++" 
							:disabled="currentPage === totalPages"
							styling-mode="outlined" 
							type="default"
							
						/>
					</div>

				</div>

			</div>
		</ZoomableContainer>
	</div>
</template>

<script setup>

  window.scanFocus = (barcode) =>{
	if(popupValidateVisible.value == true)
	{
		if(lastAPISuccess.value == true)
		{
			trackingNumberValue.value = barcode;
			onCheckTrackingNumber();	
		}
	}
	else
	{
		barcodeValue.value = barcode;
		onFetchOrder(barcode);	
	}
    
  }

	import { onMounted, onUnmounted, ref, computed , nextTick, watch} from 'vue';
	import { DxSelectBox } from 'devextreme-vue';
	import { DxButton } from 'devextreme-vue/button';
	import { DxTextBox } from 'devextreme-vue/text-box';
	import { DxLoadPanel } from 'devextreme-vue/load-panel';
	import defaultImage from '../assets/Image.svg'
	import { DxPopup , DxPosition, DxToolbarItem } from 'devextreme-vue/popup';
	import notify from 'devextreme/ui/notify';
	import ZoomableContainer from '../components/zoomable-container.vue'
	import auth from '@/auth';
	import { useSkuValidate } from '@/composables/useSkuValidate'
	import { useScreenSize } from '@/composables/useScreenSize';
	import { getQueryParam } from '@/composables/urlUtils';
	import { useRouter, useRoute } from 'vue-router';

	const router = useRouter();
	const route = useRoute();
	/* 
		COMPONENT-LEVEL ELEMENT REFS  
	*/
	const zoomContainer = ref()
	const popupValidate = ref()	
	const popupAdminLogin = ref(null)
	const inputBarcode = ref(null)
	const inputTrackingNumber = ref(null)
	const completeButton = ref(null)
	
	/* 
		COMPONENT-LEVEL VARIABLES  
	*/
	const isServerConnected = ref(true);
	const popupValidateVisible = ref(false)
	const popupAdminLoginVisible = ref(false)
	// const selectedStation = ref({});
	//TYLER ADDED
	const lastAPISuccess = ref(false);
  const validatedPrinterList = ref([]);

	const barcodeValue = ref('')
	const failedTrackingAttempts = ref(0);
	const trackingNumberValue = ref('')
	const currentPage = ref(1)
	const itemsPerPage = ref(5)
	const isMaximized = ref(false)
	const isMaximizedFromUrl = ref(false);
	const imagePopupVisible = ref(false);
	const currentImageSrc = ref('');
	const adminCredentials = ref({ username: '', password: '' })
	const showCompleteButton = ref(false);
  const eloDebugText = ref(0);

  // Initialize the REST API functionality
  const {
		connect,
		disconnect,
		initialize,
		sendEstablishMessage,
		fetchOrder,
		validateOrder,
    completeContainer,
    reprintAllLabels,
		selectedStation,
		stations,
		order,
		isAdminOverride,
		isConnected,
		isLoading,
		lastAPIError,
		lastAPIErrorData,
		clearError,
		connectionStatus

	} = useSkuValidate()
	const { isMobile } = useScreenSize()

	const notifyOptions = ref(
		{
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 0 }},
			width: 'auto',
			animation: {
				show: { type: "fade", duration: 800, from: 0, to: 1 },
				hide: { type: "fade", duration: 800, from: 1, to: 0 }
			}
		}
	)

	// const order = ref({order_number: '123456', tracking_number: '1Z374859201947365802WX', 
	// 	items: 
	// 	[
	// 		{ sku_number: '123456', sku_barcode: null, completed_code: 'ML-9999', description: 'Lorem ipsum odor amet, consectetuer adipiscing elit. Placerat molestie scelerisque suspendisse class donec. Tempor torquent libero magnis morbi, amet scelerisque.', image_url: '/temp/cap.jpeg', qty_picked: 4, qty_validated: 0 },
	// 		{ sku_number: '789012', sku_barcode: null, completed_code: 'ML-9999', description: 'Lorem ipsum odor amet, consectetuer adipiscing elit. Placerat molestie scelerisque suspendisse class donec.', image_url: '/temp/tshirt_blue.jpeg', qty_picked: 2, qty_validated: 0 },
	// 		{ sku_number: '345678', sku_barcode: null, completed_code: 'ML-9999', description: 'BLorem ipsum odor amet, consectetuer adipiscing elit. Placerat molestie scelerisque suspendisse class donec.', image_url: '/temp/socks.jpeg', qty_picked: 5, qty_validated: 0 },
	// 		{ sku_number: '901234', sku_barcode: null, completed_code: 'ML-9999', description: 'Lorem ipsum odor amet, consectetuer adipiscing elit. Placerat molestie scelerisque suspendisse class donec.', image_url: '/temp/tshirt_red.jpeg', qty_picked: 3, qty_validated: 0 },
	// 		{ sku_number: '567890', sku_barcode: null, completed_code: 'ML-9999', description: 'Lorem ipsum odor amet, consectetuer adipiscing elit. Placerat molestie scelerisque suspendisse class donec.', image_url: '/temp/pjs.jpeg', qty_picked: 2, qty_validated: 0 },

	// 	]
	// });	

	const setupMidnightTimer = () => {
		
		// Calculate time until next midnight
		const calculateTimeUntilMidnight = () => {
			const now = new Date();
			const midnight = new Date();
			midnight.setHours(24, 0, 0, 0); // Set to next midnight
			return midnight - now;
		};
		
		// Function to disconnect at midnight
		const disconnectAtMidnight = () => {
			if (isConnected.value) {
				console.log('Midnight disconnect triggered');
				onStationDisconnect();
			}
			
			// Schedule next midnight timer
			const timeUntilNextMidnight = calculateTimeUntilMidnight();
			midnightTimerId = setTimeout(disconnectAtMidnight, timeUntilNextMidnight);
		};
		
		// Start the timer
		let midnightTimerId = setTimeout(disconnectAtMidnight, calculateTimeUntilMidnight());
		console.log(`Scheduled midnight disconnect in ${Math.floor(calculateTimeUntilMidnight()/1000/60)} minutes`);
		
		// Set up visibility change handler
		const handleVisibilityChange = () => {
			if (document.visibilityState === 'visible') {
				// Re-sync our timer when page becomes visible again
				clearTimeout(midnightTimerId);
				midnightTimerId = setTimeout(disconnectAtMidnight, calculateTimeUntilMidnight());
			}
		};
		
		// Add visibility change listener
		document.addEventListener('visibilitychange', handleVisibilityChange);
		
		// Return cleanup function
		return () => {
			document.removeEventListener('visibilitychange', handleVisibilityChange);
			clearTimeout(midnightTimerId);
		};
	};

	/* 
		COMPONENT-LEVEL LIFECYCLE HOOKS  
	*/
	onMounted(() => {
		// get station from local storage if available and set it as default
		const station = localStorage.getItem('Pack-Station');
		if (station){
			selectedStation.value = stations.value.find(s => s === station);
		}

		// override the station if it's passed as a query parameter
		const packStationParam = getQueryParam('packStation');
		if (packStationParam) {
			selectedStation.value = stations.value.find(s => s === packStationParam) || station;
		}
		
		// Get URL query parameters
		isMaximizedFromUrl.value = getQueryParam('max') === 'true';
		
		// Set isMaximized based on URL parameter if it exists
		if (isMaximizedFromUrl.value) {
			isMaximized.value = true;

		}

		// Initialize the midnight timer
		const cleanupMidnightTimer = setupMidnightTimer();

		// Clean up on unmount
		onUnmounted(() => {
			cleanupMidnightTimer();
		});
	});

  onUnmounted(()=>{
    eloTimerEnabled.value = false;	

    // Clear all intervals when component is unmounted
    const interval_id = window.setInterval(function(){}, Number.MAX_SAFE_INTEGER);
    for (let i = 1; i < interval_id; i++) {
      window.clearInterval(i);
    }
  });

  /*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	const paginatedItems = computed(() => {
		if (!order.value.lpn_lines) return [];

		if (isMobile.value) return order.value.lpn_lines

		const start = (currentPage.value - 1) * itemsPerPage.value
		const end = start + itemsPerPage.value
		return order.value.lpn_lines?.slice(start, end)
	})

	const totalPages = computed(() => {
		return Math.ceil((order.value.lpn_lines?.length || 0) / itemsPerPage.value) || 1;
	})

	const currentZoom = computed(() => {
		return zoomContainer.value?.zoomLevel || 1
	})

	const totalQtyPicked = computed(() => {
		return order.value.lpn_lines?.reduce((total, item) => total + item.actual_qty, 0) || 0;
	});

	const totalQtyValidated = computed(() => {
		//console.log('should be recalculating qty')
		return order.value.lpn_lines?.reduce((total, item) => total + (item.qty_validated || 0), 0) ?? 0;
		//return order.value.lpn_lines?.reduce((total, item) => total + item.qty_validated, 0);
	});

	const carrierColor = computed(() => {
		
		if (order.value.international) return 'Yellow'

		if (order.value.shipping_carrier === null) return 'other';

		switch (order.value.shipping_carrier.toUpperCase()) {
			case 'UPS':
				return 'green';
			case 'USPS':
				return 'blue';
			case 'PICKUPS':
				return 'purple';
			case 'DHL':
				return 'purple';
			case 'FEDEX':
				return 'blue';
			default:
				return 'other';
		}
	});
	// const getPickValidateMismatches = computed(() => {
	// 	return order.value.lpn_lines?.reduce((errors, item) => {
	// 		if (item.qty_picked !== item.qty_validated) {
	// 			errors.push({
	// 				sku_number: item.sku_number,
	// 				message: `SKU ${item.sku_number} (${item.name}) has a quantity mismatch - Picked: ${item.qty_picked}, Validated: ${item.qty_validated}`
	// 			});
	// 		}
	// 		return errors;
	// 	}, []);
	// });

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	// Monitor connection status changes
	watch(connectionStatus, (newStatus, oldStatus) => {
		if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED') {

			notify(getNotifyOptions('success', 'Connected to station ' + selectedStation.value, 3000));

			// Start the focus timer
			startFocusTimer();		

			// Automatically send ESTABLISH 
			sendEstablishMessage();
			
			focusTextBox();

		} else if (newStatus === 'ERROR' && oldStatus !== 'ERROR') {
			// notify({ ...notifyOptions.value, message: `Error! We're having trouble connecting to the live service.`, type: 'error', displayTime: 5000  });
			notify(getNotifyOptions('error', `Error! We're having trouble connecting to the live service.`, 5000));
		} else if (newStatus === 'DISCONNECTED' && oldStatus !== 'DISCONNECTED'){
			// notify({...notifyOptions.value,  message: 'Disconnected from station ' + selectedStation.value, type: 'success', displayTime: 3000 });
			notify(getNotifyOptions('success', 'Disconnected from station ' + selectedStation.value, 5000));
		}
	});
	

	/*=====================================================================
    FUNCTIONS
  =====================================================================*/
	// Function to focus the text box
	const focusTextBox = async () => {
		if (!lastAPISuccess.value && popupValidateVisible.value) return;

		await nextTick();

		// If we are on validate popup screen and validation is successful set focus 
		// to tracking number so that the user can scan and complete the order
		if (lastAPISuccess.value && popupValidateVisible.value){

			if (inputTrackingNumber.value && inputTrackingNumber.value.instance) {
				inputTrackingNumber.value.instance.focus();
			}
		} else if (inputBarcode.value && inputBarcode.value.instance) {
			inputBarcode.value.instance.focus();
		}
		
	};

	const onValHidden = async() => {
		//forceBlurAll()
		popupValidateVisible.value = false
		trackingNumberValue.value = ''
		//TYLER; add focus to text box on close
    focusTextBox();
		
	}
	const onAdminHidden = async() => {
		//forceBlurAll()
		popupAdminLoginVisible.value = false
		// adminCredentials.value = { username: '', password: '' }
		
	}

	// Create a notification manager to handle stacking
	const notificationManager = {
		activeNotifications: 0,
		notificationGap: 10, // Gap between notifications in pixels
		baseOffset: 10,      // Base offset from the bottom

		// Add a notification and return updated position
		addNotification() {
			const position = this.activeNotifications;
			this.activeNotifications++;
			return position;
		},

		// Remove a notification when it's closed
		removeNotification() {
			if (this.activeNotifications > 0) {
				this.activeNotifications--;
			}
		},

		// Calculate the y-offset for a notification based on its position
		getOffset(position) {
			return this.baseOffset + (position * (this.notificationGap - 50)); // 50 is approx height of notification
		}
	};

// Modified notification options function
const getNotifyOptions = (type, message, duration = 3000) => {
  const position = notificationManager.addNotification();
  
  return {
    message,
    type,
    displayTime: duration,
    position: { 
      my: "bottom", 
      at: "bottom", 
      of: isMaximized.value ? window : ".content-block", 
      offset: { x: 0, y: notificationManager.getOffset(position) }
    },
    animation: {
      show: { type: "fade", duration: 400, from: 0, to: 1 },
      hide: { type: "fade", duration: 400, from: 1, to: 0 }
    },
    onHidden: function() {
      notificationManager.removeNotification();
    }
  };
};

// Modified showNotification function
const showNotification = (result, successMessage) => {
  if (result.success) {
    notify(getNotifyOptions('success', successMessage, 2000));
		
  } else {
    notify(getNotifyOptions('error', result.error || 'Operation failed', 3000));
  }
};

// TYLER; move from button calling function in useSkuValidate to call like this to notify user of errors
  const onFetchOrder = async (barcode) => {
    try
    {
			// console.log('lastAPISuccess',lastAPISuccess.value)
			/* TYLER; this is not needed anymore and would get the screens stuck in a state
			if (lastAPISuccess.value) {
				await onComplete()
			}
			*/

			popupValidateVisible.value = false;
      eloDebugText.value++;
      //turnOffEloLight();
      const result = await fetchOrder(barcode);
      if(!result.success)
      {
        showNotification(result, result.message);
        //clear out the values that may be stored / mostly just clear the text box if back end ever rejects this call because NO Match found
        onClear();
      }
      else if(result.success)
      {
        changeEloColor("Blue");
      }
			barcodeValue.value = ''
    }
    catch(error)
    {
      // notify({...notifyOptions.value, message: 'Fetch Order Failed', type: 'error' });
			notify(getNotifyOptions('error', 'Fetch Order Failed', 3000));
			
			onClear();
    }
  };


  //TYLER; complete a container
  const onComplete = async () => {
    try
    {
      const result = await completeContainer(order.value.lpn_barcode);

      if(result.success){
        showNotification(result, result.message);
		//move clear the screen to only happen if the API returns that it completed the order
		//if change i made passing the barcode does not fix this will atleast draw the users attention
		//it will NOT allow them to complete it and will keep screen up so user looks at error message
		onClear();
      }
      else{
		console.log(result)
        showNotification(result, result.message);
      }
    }
    catch(error)
    {
      // notify({...notifyOptions.value, message: 'Complete Order Failed', type: 'error' });
		notify(getNotifyOptions('error', 'Complete Order Failed', 3000));
			
    }
  };

	const onCheckTrackingNumber = async () => {
		
		if (order.value.lpn_barcode == '' || trackingNumberValue.value == '') return;
		
		if (trackingNumberValue.value.includes(order.value.lpn_barcode)) {
			await onComplete();
		}else {
			failedTrackingAttempts.value++;
			//TYLER; on error with tracking we need to clear the value from the text box so they can re scan the correct label, Users were scanning wrong barcode onsite
			trackingNumberValue.value = '';
			if (failedTrackingAttempts.value >= 5)
			{
				showCompleteButton.value = true
				// notify({...notifyOptions.value, message: 'Complete button enabled for manual mode, please check label.', type: 'warning' , displayTime: 5000 });
				notify(getNotifyOptions('warning', 'Complete button enabled for manual mode, please check label.', 5000));
			}
			// notify({...notifyOptions.value, message: 'Order validated does not match the scanned label.', type: 'error' , displayTime: 1000 });
			notify(getNotifyOptions('error', 'Order validated does not match the scanned label.', 3000));
		}
	}

  //TYLER; re print all a containers barcodes
  const onReprintALL = async () => {
    try
    {
      const result = await reprintAllLabels();
      if(result.success){
        showNotification(result, result.message);
      }
      else{
        //remove this log later
        // console.log(result);
        showNotification(result, result.message);
      }
    }
    catch(error)
    {
      // notify({...notifyOptions.value, message: 'Reprint Failed', type: 'error' });
			notify(getNotifyOptions('error', 'Reprint Failed', 3000));
    }
  }

	const onValidate = async () => {
		try {
			// reset 
			validatedPrinterList.value = [];
			failedTrackingAttempts.value = 0;
			showCompleteButton.value = false

			const result = await validateOrder();
			lastAPISuccess.value = result.success;
			
			if (result.success){
				showNotification(result, result.message);
				
        changeEloColor("Green");
        validatedPrinterList.value = result.data;
        //TYLER; show popup on valid so that the user can re-print the label incase there is an issue with the printer
        //the act of completing this in the pack station will UNBIND the tote from the carton so they will be unable to print it unless they manually type the containers barcode(from shipping label they don't have) in at pack station
        popupValidateVisible.value = true;
				// Wait until popup is visible before focusing
				await nextTick();

				focusTextBox();
				//console.log(result)
        //TYLER; moved clear to new OnComplete function instead
				//onClear();
			} else {
        changeEloColor("Red");
				//invalid, show popup
        showNotification(result, result.message);
				popupValidateVisible.value = true;
			}

		} catch (error) {
				// notify({...notifyOptions.value, message: 'Validation failed', type: 'error' });
				notify(getNotifyOptions('error', 'Validation failed', 3000));
		}
	}

	const onClear = () => {
		order.value = {
    wms_order_number: '',
    shipping_carrier: '',
    lpn_barcode: '',
    international: false,
    lpn_lines: [] 
  }
		barcodeValue.value = ''
		trackingNumberValue.value = ''
		lastAPISuccess.value = false
		popupValidateVisible.value = false;
		failedTrackingAttempts.value = 0;
		showCompleteButton.value = false
		focusTextBox();
    turnOffEloLight();
	}

	const increaseValidated = (id) => {

		const item = order.value.lpn_lines.find(i => i.line_id === id)
		item.qty_validated = (item.qty_validated ?? 0) + 1;
		//order.value.lpn_lines[index].qty_validated++;
	}

	const decreaseValidated = (id) => {
		const item = order.value.lpn_lines.find(i => i.line_id === id)

		if (item.qty_validated > 0) {
			item.qty_validated = (item.qty_validated ?? 0) - 1;
			// order.value.lpn_lines[index].qty_validated--
		}
	}

	const onStationConnect = async() => {
		if (!selectedStation.value) {
			// notify({ ...notifyOptions.value, message: 'Please select a station!', type: 'error', displayTime: 1000 });
			notify(getNotifyOptions('error', 'Please select a station!', 1000));
			return;
		}

		if (isMobile.value && !selectedStation.value.includes('Mobile')) {
			// notify({ ...notifyOptions.value, message: 'Mobile station selection required for this device!', type: 'error', displayTime: 1000 });
			notify(getNotifyOptions('error', 'Mobile station selection required for this device!', 1000));
			return;
		}

		// Set the station in local storage for use as default
		localStorage.setItem('Pack-Station', selectedStation.value);

		// connect websocket
		await connect();
		// isSocketConnected.value = true;

		// notify({ ...notifyOptions.value, message: 'Connected to station ' + selectedStation.value.value, type: 'success', displayTime: 2000 });
	};

	const onStationDisconnect =() => {

		disconnect()

		if (isMaximizedFromUrl.value){
			auth.logOut();
			router.push({
				path: "/login-form",
				query: { returnUrl: route.path + '?max=true&packStation=' + selectedStation.value }
			});
		}
		//console.log('disconnecting', isSocketConnected.value);
		//orders.value = ogArray.value 
	
		// notify({ ...notifyOptions.value, message: 'Disconnected from station ' + selectedStation.value.value, type: 'success', displayTime: 2000 });
	};

	const toggleMaximize = () => {
		isMaximized.value = !isMaximized.value

		if (!isMaximized.value)  {
			// console.log('.content-block')
			notifyOptions.value.position = { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}
		} else {
			// console.log('.container')
			notifyOptions.value.position = { my: "bottom", at: "bottom", of:  window, offset: { x: 0, y: -10 }}
		}
		
	}

	const showAdminLogin = () => {
		popupAdminLoginVisible.value = true
	}


	// const forceBlurAll = () => {
	// 	const focusable = document.querySelectorAll('input, button, [tabindex]')
	// 	focusable.forEach(element => {
	// 		if (element instanceof HTMLElement) {
	// 			console.log(element)
	// 			element.blur()
	// 		}
	// 	})
		
	// 	if (document.activeElement instanceof HTMLElement) {
	// 		document.activeElement.blur()
	// 	}
	// 	document.body.focus()
	// }

	const handleAdminLogin = async () => {
		try {
			const result = await auth.verifyAdmin(
				adminCredentials.value.username,
				adminCredentials.value.password
			);
			
			if (result.isOk) {
				isAdminOverride.value = true;

				
				popupAdminLoginVisible.value = false;
				popupValidateVisible.value = false;
				
			} else {
				// notify({ ...notifyOptions.value, message: result.message || 'Login failed', type: 'error' });
				notify(getNotifyOptions('error', 'Login failed', 3000));
			}
		} catch (error) {
			// notify({...notifyOptions.value,  message: error.message || 'Login failed', type: 'error'  });
			notify(getNotifyOptions('error', 'Login failed', 3000));
		}
	};

	const openImagePopup = (imageSrc) => {
		currentImageSrc.value = imageSrc || defaultImage;
		imagePopupVisible.value = true;
	};

	const closeImagePopup = () => {
		imagePopupVisible.value = false;
	};

  //elo stuff
  const eloReady = ref(false);
  
  const eloCmdList = ref([]);
  const eloTimerEnabled = ref(true);
  const eloCurrentColor = ref('N/A');

  const eloTimer = () => {
    if(eloTimerEnabled.value)
    {
      setTimeout(() => {
        // console.log('im looking for cmd');
        checkForEloCmd();
        eloTimer();
      }, 300);
    }
  }
  setTimeout(() => {
    eloTimer();
  }, 1000);

  const checkForEloCmd = () =>{
    if(eloCmdList.value.length > 0)
    {
      let cmd = eloCmdList.value.pop();
      //eloDebugText.value = cmd.colorInt;
      if(eloReady.value)
      {
        EloPeripheralManager.setLight(cmd.colorInt, cmd.status);
      }
      else
      {
        console.log("elo not connected or initialized to change the color on the light bar");
      }
    }
  }

  const checkInitElo = () => {
    if(typeof EloPeripheralManager !== 'undefined'){
      EloPeripheralManager.initialize("onEloPeripheralManagerReady");
      console.log("found elo on this page");
      //eloDebugText.value = "found elo on this page";
      eloReady.value = true;
      setTimeout(() => { turnOffEloLight(); }, 2000);
    }
    else{
      console.log("cannot find elo on this page");
      //eloDebugText.value = "cannot find elo on this page";
      eloReady.value = false;
    }
  }
  checkInitElo();

  const GetEloInt = (colorString) => {
    switch(colorString)
    {
      case 'Red':
        return 0;
      case 'Green':
        return 7;
      case 'Blue':
        return 8;
      default:
        return -1;
    }
  }

  const changeEloColor = (colorString) => {

    console.log(`-------colorString = ${colorString}`);
    if(eloReady.value)
    {
      
      if(colorString != eloCurrentColor.value)
      {
        let oldColor = GetEloInt(eloCurrentColor.value);
        if(oldColor != -1)
        {
          eloCmdList.value.push({colorInt: oldColor, status: false});  
        }
        

        eloCmdList.value.push({colorInt: GetEloInt(colorString), status: true});

        eloCurrentColor.value = colorString;
      }
      
    }
  }

  const turnOffEloLight = () => {
    if(eloReady.value)
    {
      eloCmdList.value.push({colorInt: 0, status: false});
      eloCmdList.value.push({colorInt: 7, status: false});
      eloCmdList.value.push({colorInt: 8, status: false});
    }
    else
    {
      console.log("elo not connected or initalized to change the color on the light bar");
    }
  }


  function onEloPeripheralManagerReady(connected) {
    if (connected === "true") {
        eloReady.value = true;
        console.log("elo ready!!!");
    } else {
        eloReady.value = false;
        console.log("elo NOT ready!!!");
    }
  }

  function scanFocus(barcode)
  {
    try{
      barcodeValue.value = barcode += '\r\n';
    }catch(error){
      console.log(error);
    }
    
  }
  //end elo sdk stuff

  const startFocusTimer = () => {
    setInterval(() => {
      focusTextBox();
    }, 1000); // 1000 ms = 1 second
  };

</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";
	.error-list {
		list-style: none;
		padding: 0;
		margin: 0;
	}

	.error-item {
		display: flex;
		align-items: center;
		gap: 12px;
		padding: 8px;
		margin-bottom: 8px;
		background-color: #fef2f2;
		border: 1px solid #fecaca;
		border-radius: 6px;
		color: #dc2626;
		// font-size: 12px;
	}

	.blue{
		background-color: #2563eb;
	}
	.green {
		background-color: #22c55e;	
	}
	.yellow {
		background-color: #facc15;
	}
	.purple {
		background-color: #a855f7;
	}

  .success-item {
		display: flex;
		align-items: center;
		gap: 12px;
		padding: 8px;
		margin-bottom: 8px;
		background-color: #f4fef2;
		border: 1px solid #d4feca;
		border-radius: 6px;
		color: #26dc29;
		// font-size: 12px;
	}

	.error-icon {
		flex-shrink: 0;
		width: 20px;
		height: 20px;
	}

	.error-message {
		line-height: 1.4;
	}

	/* For different severity levels */
	.error-item.warning {
		background-color: #fffbeb;
		border-color: #fde68a;
		color: #d97706;
	}

	.error-item.info {
		background-color: #eff6ff;
		border-color: #bfdbfe;
		color: #2563eb;
	}

	.red-border {
		border: 1px solid #704040;
	}
	.content-block {
		
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		padding: 25px;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		&.maximized {
			visibility: visible;
			position: fixed;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			z-index: 950;
			margin: 0;
			
		}
	}

	.container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		flex: 1 1 auto;
		overflow-y: auto;
		padding-right: 5px; 
		color: rgba($base-text-color, alpha($base-text-color)  * 0.65);
		letter-spacing: 0.3px;
	}

	.card {
		position: relative;
		height: 280px;
		width: 220px;
		padding: 20px 40px;
		border-radius: 8px;
		align-content: center;
		background-color: rgba($base-bg-dark, 0.5);
		font-size: medium;
		letter-spacing: 2px;
		transition: transform .3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.row {
		display: flex;
		gap: 10px;
		flex-shrink: 0; 
	}

	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}

	.text-color {
		color: rgba($base-text-color, alpha($base-text-color)  * 0.65);
	}


	.tab{
		background-color: $base-bg-dark;
		padding: 5px 15px;
		border-radius: 8px 8px 0 0;
	}
	// Table styles
	table {
		border-collapse: collapse;
		width: 100%;
		position: sticky;
		top: 0;
		z-index: 1;
	}
	tr {
		transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
	}
	th, td {
		border: none;
		padding:12px;
		text-align: left;
	}

	th {
		background-color: $base-bg-dark;
		white-space: nowrap;
		padding: 8px;
		font-weight: normal;
	}

	th:first-of-type {
		border-radius: 0px 0 0 8px;
	}

	th:last-of-type {
		border-radius: 0 8px 8px 0;
	}

	.paging-control {
		margin-top: 10px; 
		display: flex; 
		gap: 10px; 
		align-items: center;
		justify-content: center;
	}

	.valid-row {
		background-color: rgba($base-accent,0.3)!important;
		// transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.invalid-row {
		background-color: rgba(red,0.3)!important;
	}

	/* DX Controls Override  */
	.dx-texteditor {
		border: 0px;
		border-radius: 5px;
		background-color: $base-bg-dark;
	}
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: $base-bg-dark;
		opacity: 1;
	}
	.dx-texteditor::before {
		content: none;
	}
	.dx-texteditor::after {
		content: none;
	}

	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);

	}

	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);

	}

	.custom-button.dx-button-has-icon ,.dx-button-content {
		// min-width: unset!important;
		// height: unset!important;
	}
	::v-deep(.dx-button-has-icon ){
		// width: unset!important;
		// height: unset!important;
	}

	::v-deep(.dx-button) {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color;
	}


	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		// width: unset;
		// height: unset;
	}


	.dx-button-has-icon {
		width: 31px;
	}
	.custom-button.dx-button-has-icon {
		min-width: unset!important;
	}
	::v-deep(.dx-button-has-icon >.dx-button-content) {
    padding-inline-start: 2px;
  }

	::v-deep(.dx-toolbar-item-content>.dx-button) {
			height: 31px!important;
	}
		
	.login-container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		padding: 20px;
	}

	.admin-mode-msg{
		position: absolute;
		text-align: center;
		background-color: #704040;
		display: flex;
		justify-self: center;
		top: 0;
		left: 0;
		right: 0;
		padding: 2px 5px;
		border-radius: 0 0 5px 5px;
	}


	// MOBILE STYLES
	.mobile-wrap {
		height: 75dvh;
		overflow: auto;
	}
	.mobile-row {
		flex-wrap: wrap;
	}

	.product-card {
		margin-bottom: 16px;
		border-radius: 8px;
		padding: 12px;
		box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		background-color: rgba($base-bg-dark, 0.5);
	}

	.product-header {
		display: flex;
		align-items: center;
		margin-bottom: 12px;
	}

	.product-image {
		cursor: pointer;
		margin-right: 12px;
	}

	.product-image img {
		width: 50px;
		height: 50px;
		border-radius: 100%;
		background-color: #2B2A33;
		object-fit: cover;
	}



	.product-description {
		margin-bottom: 12px;
		word-break: break-word;
	}

	.product-data {
		margin-bottom: 12px;
	}

	.data-row {
		display: flex;
		flex: auto;
		padding: 6px 0;

	}

	.data-row:last-child {
		border-bottom: none;
	}

	.data-label {
		font-weight: 500;

	}

	.product-actions {
		display: flex;
		gap: 10px;
		justify-content: flex-end;
	}



</style>
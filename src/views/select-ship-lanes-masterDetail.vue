<template>
  <div>
    <DxLoadPanel
      v-model:visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <div v-if="showShipDetail">
      <div style="display: flex; align-items: center; justify-content: space-between;">
        <h4>Cartons Associated With Shipment</h4>
        <div>
          <DxSelectBox
              :items="filterCartonsInShipmentData"
              v-model:value="filterForCartonsSelected"
              display-expr="disp"
              value-expr="val"
              styling-mode="underlined"
              width="150"
              @value-changed="clickToFilterCartonsInShipmentGrid"
            />
        </div>
      </div>
      <div>
        <DxDataGrid
          :data-source="lpnsOfShipmentDataSource"
          id="Cartons_Associated_With_Shipment"
          class="selectShipLaneDetailGridBorder"
          ref="lpnsOfShipmentRef"
          :column-hiding-enabled="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          :column-auto-width="true"
          @exporting="onExporting"
        >
          <!-- @editor-preparing="modifySearchPanelStyleEzSearch" -->
          <DxPaging :page-size="10"/>
          <DxPager
            :show-page-size-selector="true"
            :allowed-page-sizes="[10 , 20, 40]"
            :show-info="true" 
          />
          <DxExport   
            :enabled="true"
          />
          <DxFilterRow
            :visible="true"
          />
          <!-- <DxSearchPanel
            :visible="true"
            :width="170"
            placeholder="Filter Results..."
          /> -->
            <DxColumn
              data-field="lpn_barcode"
              caption="Carton Number"
              alignment="left"
            />
            <DxColumn
              data-field="container_opt_value"
              caption="Bin"
              alignment="left"
            />
            <DxColumn
              data-field="cont_created_datetime"
              caption="Imported"
              data-type="datetime"
              alignment="left"
            />
            <DxColumn
              data-field="cont_completed_datetime"
              caption="Completed"
              data-type="datetime"
              alignment="left"
            />
            <DxMasterDetail
              :enabled="true"
              template="selectShipLaneMasterDetailTemplate"
            />
              <template #selectShipLaneMasterDetailTemplate="{data: dataObj}">
                <selectShipLaneMasterDetail
                  :templateData="dataObj"
                  :showShip="false"
                  :showUpc="true"
                />
              </template>
        </DxDataGrid>
      </div>
    </div>
    <div v-if="showUpcDetail">
      <h4>UPCs Associated With Cartons</h4>
      <DxDataGrid
        :data-source="upcsOfLpnsDataSource"
        class="selectShipLaneDetailGridBorder"
        id="UPCs_Associated_With_Cartons"
        :column-hiding-enabled="true"
        :row-alternation-enabled="true"
        :show-borders="true"
        :column-auto-width="true"
        @exporting="onExporting"
      >
        <!-- @editor-preparing="modifySearchPanelStyleEzSearch" -->
        <DxPaging :page-size="10"/>
        <DxPager
          :show-page-size-selector="true"
          :allowed-page-sizes="[10 , 20, 40]"
          :show-info="true" 
        />
        <DxExport   
          :enabled="true"
        />
        <DxFilterRow
          :visible="true"
        />
        <!-- <DxSearchPanel
          :visible="true"
          :width="170"
          placeholder="Filter Results..."
        /> -->
          <DxColumn
            data-field="upc"
            caption="UPC"
            alignment="left"
          />
      </DxDataGrid>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, computed } from 'vue';
import axios from 'axios';
import selectShipLaneMasterDetail from './select-ship-lanes-masterDetail'
import DxLoadPanel from 'devextreme-vue/load-panel';
import notify from 'devextreme/ui/notify';
import { DxSelectBox } from 'devextreme-vue/select-box';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxFilterRow,
  DxPager,
  DxMasterDetail,
  DxExport,
  //DxHeaderFilter
  // DxScrolling,
} from 'devextreme-vue/data-grid';

const loadingVisible = ref(false);

const props = defineProps({
  templateData: Object,
  showShip: Boolean,
  showUpc: Boolean
});

const showShipDetail = computed(() => {
  return props.showShip
});
const showUpcDetail = computed(() => {
  return props.showUpc
});

const lpnsOfShipmentDataSource = ref([]);
const upcsOfLpnsDataSource = ref([]);
const getShipmentDetailData = () => {
  let searchParams = {
    DetailMethod: '',
    Barcode: ''
  }
  if (props.showShip) {
    searchParams.DetailMethod = 'SHIP',
    searchParams.Barcode = props.templateData.data.shipment_id
  } else {
    searchParams.DetailMethod = 'LPN',
    searchParams.Barcode = props.templateData.data.lpn_barcode
  }

  loadingVisible.value = true;
  axios.get('api/Order/AnnexShipmentsMasterDetails', { params: searchParams }).then((resp) => {
    //console.log('drill',resp.data.data);
    if (props.showShip) {
      lpnsOfShipmentDataSource.value = resp.data.data.ShipmentsLPNs;
    } else {
      upcsOfLpnsDataSource.value = resp.data.data.ContainerUPCs;
    }
    loadingVisible.value = false;
  }).catch(error=>{
    lpnsOfShipmentDataSource.value = [];
    upcsOfLpnsDataSource.value = [];
    loadingVisible.value = false;
    console.log(error);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
getShipmentDetailData();


//FILTER CARTONS OF SHIPMENTS BY COMPLETED
const lpnsOfShipmentRef = ref(null);

const filterCartonsInShipmentData = ref([
  { disp: "Show All", val: "all" },
  { disp: "Completed", val: "complete" },
  { disp: "Not Completed", val: "notComplete" }
]);

const filterForCartonsSelected = ref(filterCartonsInShipmentData.value[0].val);

const clickToFilterCartonsInShipmentGrid = () => {
  const dataGrid = lpnsOfShipmentRef.value.instance;
  if (filterForCartonsSelected.value == 'complete') {
    dataGrid.filter([
      ['cont_completed_datetime', 'contains', '-']
    ])
  } else if (filterForCartonsSelected.value == 'notComplete') {
    dataGrid.filter([
      ['cont_completed_datetime', '=', null]
    ])
  } else {
    dataGrid.clearFilter();
  }
  //getListOfErrorMessages();
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ":" + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `${e.element.id}_${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};

</script>

<style lang="scss">
.selectShipLaneDetailGridBorder {
  border: 1px solid white;
}
</style>
<template>
  <div>
    <!-- <h2 class="content-block">EZ Search</h2> -->
    <!-- <h5 class="content-block">Search All Activity</h5> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        Search By:
        <DxRadioGroup
          id="radioBtnGroup"
          :items="radioBtnArrSearch"
          :value="radioBtnArrSearch[0]"
          layout="horizontal"
          @value-changed="handleRadioBtnChangeSearch"
        />
        Data Options:
        <DxRadioGroup
          :items="radioBtnArr"
          :value="isReporting"
          display-expr="disp"
          value-expr="Reporting"
          layout="horizontal"
          @value-changed="handleRadioBtnChange"
        />
        <div id="wildsearchTextBoxandLoad">
          <DxTextBox 
            class="searchTextBox"
            :placeholder="searchPlaceholder"
            value-change-event="keyup"
            @value-changed="updateSearch"
            :width="200"
            styling-mode="underlined"
          />
          <DxLoadIndicator
            class="wildsearchLoadIndicator"
            v-model:visible="loadingVisible"
            :height="30"
            :width="30"
          />
        </div>
        <div class="dataGridContainer">
          <DxDataGrid
            :data-source="ezSearchGridDataSource"
            :column-hiding-enabled="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :column-auto-width="true"
            @editor-preparing="modifySearchPanelStyleEzSearch"
            @exporting="onExporting"
          >
            <DxPaging :page-size="10"/>
            <DxPager
              :show-page-size-selector="true"
              :allowed-page-sizes="[10 , 20, 40]"
              :show-info="true" 
            />
            <DxExport   
              :enabled="true"
            />
            <DxFilterRow
              :visible="true"
            />
            <DxSearchPanel
              :visible="true"
              :width="170"
              placeholder="Filter Results..."
            />
              <DxColumn
                data-field="lpn_barcode"
                caption="LPN"
                alignment="left"
              />
              <DxColumn
                data-field="created_datetime"
                caption="Created"
                data-type="datetime"
                alignment="left"
                name="createdFirst"
              />
              <DxColumn 
                data-field="created_datetime"
                caption="Seconds.Milliseconds"
                alignment="left"
                v-model:visible="isProgrammerLevel"
                name="createdSecond"
                cell-template="isPro"
              />
                <template #isPro="{data}">
                  <div>{{ searchSecsAndMilli(data.text) }}</div>
                </template>
              <DxColumn 
                data-field="lpn_tracking_number"
                caption="Shipping ID"
                alignment="left"
              />
              <DxColumn 
                data-field="container_calc_weight"
                caption="Wave ID"
                alignment="center"
              />
              <!-- <DxColumn 
                data-field="container_actual_weight"
                caption="Scale Weight"
                alignment="center"
              /> -->
              <DxColumn 
                data-field="lpn_condition_code"
                caption="Container Type"
                alignment="left"
              />
              <DxColumn 
                data-field="cancled"
                caption="Special Handling"
                alignment="left"
                cell-template="specialHandling"
              />
                <template #specialHandling="{ data }">
                  <div>{{ changeTrueFalseToWord(data.text) }}</div>
                </template>
                
              <DxColumn
                data-field="altered_datetime"
                caption="Last Altered"
                data-type="datetime"
                alignment="left"
                name="alteredFirst"
              />
              <DxColumn
                data-field="altered_datetime"
                caption="Seconds.Milliseconds"
                alignment="left"
                v-model:visible="isProgrammerLevel"
                name="alteredSecond"
                cell-template="isProAltered"
              />
                <template #isProAltered="{data}">
                  <div>{{ searchSecsAndMilli(data.text) }}</div>
                </template>
              <DxColumn
                data-field="completed_datetime"
                caption="Completed"
                data-type="datetime"
                alignment="left"
                name="completedFirst"
              />
              <DxColumn
                data-field="completed_datetime"
                caption="Seconds.Milliseconds"
                alignment="left"
                v-model:visible="isProgrammerLevel"
                name="completedSecond"
                cell-template="isProCompleted"
              />
                <template #isProCompleted="{data}">
                  <div>{{ searchSecsAndMilli(data.text) }}</div>
                </template>
              <!-- <DxColumn 
                data-field="ship_via"
                caption="Ship Via"
                alignment="left"
              /> -->
              <DxMasterDetail
                :enabled="true"
                template="masterDetailTemplate"
              />
                <template #masterDetailTemplate="{data: dataObj}">
                  <masterDetailView
                    :templateData="dataObj"
                  />
                </template>
          </DxDataGrid>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import auth from '../auth';
import masterDetailView from './masterDetailView';
import DxTextBox from 'devextreme-vue/text-box';
// import DxButton from 'devextreme-vue/button';
import DxRadioGroup from 'devextreme-vue/radio-group';
import { DxLoadIndicator } from 'devextreme-vue/load-indicator';
import notify from 'devextreme/ui/notify';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow,
  DxMasterDetail,
  DxExport,
  // DxLookup
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

const SearchWithFilter = ref({
  SearchMethod: "LPN",
  SearchValue: "",
  isReporting: "0",
});
const ezSearchGridDataSource = ref([]);
const radioBtnArrSearch = ref([
  'LPN', 
  'Shipment ID',
  'Wave ID'
]);
const searchedValue = ref('');
// const searchedValue = ref("");
const searchPlaceholder = ref('Search LPN...');
const loadingVisible = ref(false);
const radioBtnArr = ref(['Active Data < 7 days', 'Archived Data > 7 days']);
const isReporting = ref("0");
// const searchType = ref("pId");
// const searchTitle = ref('PIECEID');
// const databaseInfo = ref(Object);

databaseName.checkWebsiteVersion();
radioBtnArr.value = databaseName.getNumberOfDays();

const updateSearch = (data) => {
  searchedValue.value = data.value;
  autoSearchLoop();
};
const autoSearchLoop = () => {
  loadingVisible.value = true;
  let temp = searchedValue.value;
  setTimeout(()=>{
    if(temp === searchedValue.value){
      if(temp !== ""){
        getEZSearchData(searchedValue.value);
      } else {
        ezSearchGridDataSource.value = [];
        loadingVisible.value = false;
      }
    }
  },2000)
};

const getEZSearchData = (number) => {
  SearchWithFilter.value.SearchValue = number.toString();
  // console.log(SearchWithFilter.value);
  axios.get('api/Order/OrderDetails', { params: SearchWithFilter.value }).then(resp=>{
    console.log(resp.data);
    if(resp.status === 200) {
      resp.data.data.forEach(obj=>{
        obj.isReporting = isReporting.value;
        obj.isProgrammerLevel = isProgrammerLevel.value;
      });
      ezSearchGridDataSource.value = resp.data.data;
      loadingVisible.value = false;
      // notify(resp.data.message, 'success', 4000);
    } else {
      ezSearchGridDataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    // console.log(error.response);
    // notify(resp.data.message, 'error', 10000);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the serve have not lost connection');
      return;
    }
  });
};

const handleRadioBtnChangeSearch = (data) => {
  if(data.value === "LPN"){
    searchPlaceholder.value = "Search LPN...";
    // searchType.value = "order";
    // searchTitle.value = "Order Number";
    SearchWithFilter.value.SearchMethod = "LPN";
    ezSearchGridDataSource.value = [];
  
  }else if (data.value === "Wave ID") {
    searchPlaceholder.value = "Search Wave ID...";
    // searchType.value = "order";
    // searchTitle.value = "Order Number";
    SearchWithFilter.value.SearchMethod = "Wave ID";
    ezSearchGridDataSource.value = [];
} else {
    searchPlaceholder.value = "Search Shipment ID...";
    // searchType.value = "lpn";
    // searchTitle.value = "LPN";
    SearchWithFilter.value.SearchMethod = "ShipmentID";
    ezSearchGridDataSource.value = [];
  }
};
const handleRadioBtnChange = (data) => {
  if(data.value == 0){
    isReporting.value = "0";
    SearchWithFilter.value.isReporting = "0";
    //this.getData();
    // autoSearchLoop();
  } else {
    SearchWithFilter.value.isReporting = "1";
    isReporting.value = "1";
    //this.getData();
    // autoSearchLoop();
  }
};

const searchSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `EZ Search ${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};

const changeTrueFalseToWord = (data) => {
  console.log(typeof(data));
  if (data == 'true') {
    return 'Yes';
  } else {
    return 'No';
  }
};

const modifySearchPanelStyleEzSearch = (e) => {
  if (e.parentType === "searchPanel") {
      e.editorOptions.stylingMode = "underlined";
  }
};
</script>

<style lang="scss">
#radioBtnGroup {
  padding-bottom: 20px;
}
#wildsearchTextBoxandLoad {
  position: relative;
}
.searchTextBox {
  top: 10px;
}
.wildsearchLoadIndicator {
  position: absolute;
  left: 210px;
  top: 10px;
}
.dataGridContainer {
  margin-top: 30px;
}
</style>


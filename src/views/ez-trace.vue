<template>
  <div>
    <!-- <h2 class="content-block">EZ Trace</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div id="pick-menu-cont-trace">
          <div class="pick-audit-select-trace">
            Data Limit:
            <DxSelectBox
              :data-source="selectBoxDS"
              :value="selectBoxDS[0].num"
              display-expr="title"
              value-expr="num"
              styling-mode="underlined"
              @value-changed="handleDataAmountChange"
            />
          </div>
          <DxButton
            class="pick-refresh-btn-trace"
            :width="120"
            text="Refresh"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="getEZTraceData"
          />
        </div>
        <div id="easyTraceRadioGroupTrace">
          Data Options:
          <DxRadioGroup
            :items="radioBtnArr"
            :value="isReporting"
            display-expr="disp"
            value-expr="Reporting"
            layout="horizontal"
            @value-changed="handleRadioBtnChange"
          />
        </div>
        <DxDataGrid
            :data-source="gridDataSource"
            :column-hiding-enabled="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :column-auto-width="true"
            :word-wrap-enabled="true"
            @editor-preparing="modifySearchPanelStyleEzTrace"
            @exporting="onExporting"
          >
          <DxPaging :page-size="10"/>
          <DxPager
            :show-page-size-selector="true"
            :allowed-page-sizes="[10 , 20, 40]"
            :show-info="true" 
          />
          <DxExport   
            :enabled="true"
          />
          <DxFilterRow
            :visible="true"
          />
          <DxSearchPanel
            :visible="true"
            :width="170"
            placeholder="Filter Results..."
          />
          <DxColumn
            data-field="lpn_barcode"
            caption="Carton"
            alignment="left"
          />
          <DxColumn
            data-field="shipment_id"
            caption="Shipment ID"
            alignment="left"
          />
          <DxColumn
            data-field="created_datetime"
            caption="Created"
            alignment="left"
            data-type="datetime"
            name="createdFirst"
          />
          <DxColumn 
            data-field="created_datetime"
            caption="Seconds.Milliseconds"
            alignment="left"
            v-model:visible="isProgrammerLevel"
            name="createdSecond"
            cell-template="isPro"
          />
            
          <DxColumn
            data-field="completed_datetime"
            caption="Completed"
            data-type="datetime"
            alignment="left"
            name="alteredFirst"
          />
          <DxColumn
            data-field="completed_datetime"
            caption="Seconds.Milliseconds"
            alignment="left"
            v-model:visible="isProgrammerLevel"
            name="alteredSecond"
            cell-template="isPro"
          />
          <DxColumn
            data-field="transaction_datetime"
            caption="Last Trans Date"
            alignment="left"
            data-type="datetime"
          />
          <DxColumn
            data-field="scannerpoint"
            caption="Last Scanner"
            alignment="left"
          />
          <DxColumn
            data-field="transaction_code"
            caption="Last Trans Code"
            alignment="left"
          />
            <template #isPro="{data}">
              <div>{{ traceSecsAndMilli(data.text) }}</div>
            </template>
          <DxMasterDetail
            :enabled="true"
            template="masterDetailTemplate"
          />
            <template #masterDetailTemplate="{data: dataObj}">
              <masterDetailView
                :templateData="dataObj"
              />
            </template>
        </DxDataGrid>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import auth from '../auth';
import masterDetailView from './masterDetailView';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import DxSelectBox from 'devextreme-vue/select-box';
import notify from 'devextreme/ui/notify';
import DxRadioGroup from 'devextreme-vue/radio-group';
import { custom } from 'devextreme/ui/dialog';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow,
  DxExport,
  // DxFormat,
 // DxLookup,
  DxMasterDetail,
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

const router = useRouter();
const BasicDataClass = ref({
  Limit: "1000",
  // Filter: 'KVKLPNTransactionLogID',
  isReporting: "0"
});
const radioBtnArr = ref(['Active Data < 7 days', 'Archived Data > 7 days']);
const isReporting = ref("0");
const limit = ref(1000);
// const filter = ref('KVKLPNTransactionLogID');
const selectBoxDS = ref([{title: "1,000", num: 1000},
              // {title: "2,000", num: 2000},
              {title: "5,000", num: 5000},
              {title: "10,000", num: 10000},
              {title: "20,000", num: 20000},
              {title: "30,000", num: 30000},
              {title: "Unlimited", num: -1}]);

databaseName.checkWebsiteVersion();
radioBtnArr.value = databaseName.getNumberOfDays();

const loadingVisible = ref(false);

const gridDataSource = ref([]);
const getEZTraceData = () => {
  loadingVisible.value = true;
  axios.get('api/Order/EZTrace', { params: BasicDataClass.value }).then((resp) => {
    console.log(resp.data);
    if (resp.status === 200) {
      resp.data.data.forEach((obj) => {
        obj.isReporting = isReporting.value;
        obj.isProgrammerLevel = isProgrammerLevel.value;
      });
      gridDataSource.value = resp.data.data;
      loadingVisible.value = false;
      //notify(resp.data.message, 'success', 4000);
    } else {
      gridDataSource.value = [];
      loadingVisible.value = true;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    // console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
getEZTraceData();

const handleDataAmountChange = async (e) => {
  if(e.value > 5000 || e.value < 0) {
    limit.value = e.value;
    BasicDataClass.value.Limit = e.value.toString();
    let myConfirm = custom({
      title: 'Confirm Search Data Limit Increase',
      messageHtml: "<p>Are you sure you want to change the data limit for your search?</p><p>Doing so may cause your browser to slow.</p><p>Try using EZ Search if you know the Piece ID you are looking for</p>",
      width: 500,
      height: 500,
      buttons: [{
        text: "Go to EZ Search",
        onClick: () => {
          return { newRoute: router.push('/ez-search') };
        }
      },
      {
        text: "Increase Data Search Limit",
        onClick: () => {
          return { getData: getEZTraceData() };
        }
      }]        
    });
    myConfirm.show().then((dialogResult) => {
        if(dialogResult){
            dialogResult.newRoute;
        }else {
          dialogResult.getData;
        }
      });
  } else {
    limit.value = e.value;
    BasicDataClass.value.Limit = e.value.toString();
    getEZTraceData();
  }
};
const handleRadioBtnChange = (data) => {
  if(data.value == 0){
    isReporting.value = "0";
    BasicDataClass.value.isReporting = "0";
    getEZTraceData();
  }
  else{
    isReporting.value = "1";
    BasicDataClass.value.isReporting = "1";
    getEZTraceData();
  }
};

const modifySearchPanelStyleEzTrace = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};

const traceSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `EZ Trace ${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};
</script>

<style lang="scss">
#pick-menu-cont-trace{
  //width: 600px;
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.pick-audit-select-trace{
  float: left;
  width: 100px;
  margin-right: 20px;
  margin-bottom: 20px;
}
// #easyTraceRadioGroupTrace{
//   margin-top: 70px;
// }
// .pick-refresh-btn-trace{
//   float: left;
//   margin-right: 20px;
//   margin-bottom: 20px;
//   margin-top: 15px;
// }
</style>


<template>
  <div>
    <!-- <h2 class="content-block">Active Scanner Stats</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div :class="showMobileViewOrNot.activeScannerSearchContainer">
          <div>
            <strong>Time Range</strong>
            <DxSelectBox 
              :data-source="timeRangeSource"
              display-expr="disp"
              value-expr="value"
              v-model:value="timeRangeSelection"
              styling-mode="underlined"
              style="margin-right: 25px;"
              @value-changed="timeRangeSelectToDisable5Mins"
            />
          </div>
          <div>
            <strong>Interval</strong>
            <DxSelectBox 
              :data-source="minuteIntervalSource"
              display-expr="disp"
              value-expr="value"
              v-model:value="minuteIntervalSelection"
              styling-mode="underlined"
              style="margin-right: 25px;"
            />
          </div>
          <div>
            <DxButton 
              text="Search"
              type="default"
              styling-mode="contained"
              @click="getRecentScannerStats"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxTabPanel
          :key="selectionChangedKey"
          :height="800"
          :data-source="ActiveScannerHeader.ScannerGroups"
          v-model:selected-index="scannerGroupIndex"
          :loop="true"
          :animation-enabled="false"
          :swipe-enabled="true"
          @selection-changed="onTabChanged"
        >
          <template #title="{ data: group }">
            <span>{{group.groupName}}</span>
          </template>
          <template #item="{ data: group }">
            <div>
            <DxDataGrid
              :height="300"
              :data-source="group.data"
              :column-hiding-enabled="false"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              :column-auto-width="true"
              @exporting="onExporting"
              @cell-click="onScannerDataGridClick"
            >
            <DxExport   
              :enabled="true"
            />
            <DxScrolling mode="virtual"/>
            <DxSorting mode="none"/>
              <DxColumn
                :width="160"
                data-field="scannerName"
                caption="Scanner"
                :fixed="true" 
                fixed-position="left"
                cell-template="scanner-header"
              />
              <template #scanner-header="{data}">
                <div class="scannerTitleLink">{{data.text}}</div>
              </template>
              <DxColumn v-for="(val) in ActiveScannerHeader.DefaultScannerStatus" :key="val.name"
                :data-field="val.name"
                :caption="val.caption"
                alignment="center"
                cell-template="data-template"
              />
              <DxColumn v-for="(val) in group.extraStatuses" :key="val.name"
                :data-field="val.name"
                :caption="val.caption"
                alignment="center"
                cell-template="data-template"
              />

              <template #data-template="{data}">
                <div class="scannerDataLink">{{data.text}}</div>
              </template>

              <DxSummary>
                <DxTotalItem v-for="(val) in ActiveScannerHeader.DefaultScannerStatus" :key="val.name"
                  :column="val.name"
                  summary-type="sum"
                  display-format="{0}"
                  css-class="gridTotals"
                />
                <DxTotalItem v-for="(val) in group.extraStatuses" :key="val.name"
                  :column="val.name"
                  summary-type="sum"
                  display-format="{0}"
                  css-class="gridTotals"
                />
              </DxSummary>
            </DxDataGrid>
            <h5>***Click Scanner In Grid To Update Chart***</h5>
              <DxChart
                :data-source="group.scannersInGroup[group.chartDisplayIndex].data"
                :title="group.scannersInGroup[group.chartDisplayIndex].scannerName"
              >
                <DxTooltip
                  :enabled="true"
                  :content-template="graphToolTipFunctionRecentScans"
                />
                <DxCommonSeriesSettings
                  argument-field="timeDisplay"
                  type="line"
                  hover-mode="allArgumentPoints"
                />

                <DxArgumentAxis
                  argument-type="datetime"
                >
                  <DxLabel
                    :staggering-spacing="10"
                    display-mode="stagger"
                  />
                </DxArgumentAxis>
                <DxSeries v-for="(val) in ActiveScannerHeader.DefaultScannerStatus" :key="val.value"
                  :value-field="val.name"
                  :name="val.caption"
                />
                <DxSeries v-for="(val) in group.extraStatuses" :key="val.value"
                  :value-field="val.name"
                  :name="val.caption"
                />
                <DxLegend 
                  vertical-alignment="bottom"
                  horizontal-alignment="center"
                >
                  <DxMargin :top="25"/>
                </DxLegend>
              </DxChart>
            </div>
            
          </template>
        </DxTabPanel>
        <DxPopup
          v-model:visible="scannerMessageTypeVisible"
          :drag-enabled="false"
          :hide-on-outside-click="true"
          :show-close-button="true"
          :show-title="true"
          height="85%"
          width="85%"
          :title="scannerMessageTypeTitle"
        >
          <DxChart
            :data-source="scannerMessageTypeData"
            height="100%"
            width="100%"
          >
            <!-- @point-click="graphDataPointOpenPopup" -->
            <DxTooltip 
              :enabled="true"
              :z-index="2000"
            />
            <DxCommonSeriesSettings
              type="line"
              argument-field="timeDisplay"
              hover-mode="allArgumentPoints"
            >
            </DxCommonSeriesSettings>
            <DxSeries 
              :value-field="scannerMessageTypeFieldName"
              :name="scannerMessageTypeCaption"
            />
            <DxArgumentAxis
              argument-type="datetime"
            /> 
            <DxLegend 
              :customizeText="getLegendLabel"
              vertical-alignment="bottom"
              horizontal-alignment="center"
            >
              <DxMargin :top="25"/>
            </DxLegend>
          </DxChart>
        </DxPopup>
      </div>
    </div>

  </div>
</template>

<script setup>
import DxTabPanel from 'devextreme-vue/tab-panel';
import { ref } from 'vue';
import axios from 'axios';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import databaseName from '../myFunctions/databaseName';
import { DxSelectBox } from 'devextreme-vue/select-box';
import DxButton from 'devextreme-vue/button';
import notify from 'devextreme/ui/notify';
import DxPopup from 'devextreme-vue/popup';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxExport,
  DxSummary,
  DxTotalItem,
  DxScrolling,
  DxSorting
} from 'devextreme-vue/data-grid';
import DxChart, {
    DxSeries,
    DxCommonSeriesSettings,
    DxTooltip,
    DxLabel,
    DxMargin,
    DxLegend,
    DxArgumentAxis,
} from 'devextreme-vue/chart';



databaseName.checkWebsiteVersion();
const AmtOfScanners = ref(Number);
AmtOfScanners.value = databaseName.getAmtOfScanners();

//TYLER ADDED VARS

const ActiveScannerHeader = ref({});
ActiveScannerHeader.value = databaseName.getActiveScannerData();
console.log(ActiveScannerHeader.value);

const scannerGroupIndex = ref(0);

const scannerMessageTypeVisible = ref(false);
const scannerMessageTypeTitle = ref("");
const scannerMessageTypeData = ref([]);
const scannerMessageTypeFieldName = ref("");
const scannerMessageTypeCaption = ref("");

const onScannerDataGridClick = (e) =>{
  console.log(e);
  if(e.rowType == "data"){
    if(e.columnIndex == 0){
      let value = e.data.scannerName;
      for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups.length; c++){
        for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup.length; i++){
          if(ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup[i].scannerName == value){
            ActiveScannerHeader.value.ScannerGroups[c].chartDisplayIndex = i;
            break;
          }
        }
      }
    }
    else{
      scannerMessageTypeTitle.value = `${e.data.scannerName} - ${e.column.caption}`;
      scannerMessageTypeFieldName.value = e.column.dataField;
      scannerMessageTypeCaption.value = e.column.caption;

      for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups.length; c++){
        for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup.length; i++){
          if(ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup[i].scannerName == e.data.scannerName){
            scannerMessageTypeData.value = ActiveScannerHeader.value.ScannerGroups[c].scannersInGroup[i].data;
            break;
          }
        }
      }

      scannerMessageTypeVisible.value = true;      
    }
  }
}
const selectionChangedKey = ref(0);
const onTabChanged = () =>{
  console.log("i was clicked");
  selectionChangedKey.value++;
  console.log(ActiveScannerHeader.value);
  console.log(`tab: ${scannerGroupIndex.value}`);
}

//END OF TYLER ADDED VARS

const loadingVisible = ref(false);

const isMobile = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunction = () => {
  let activeScannerSearchContainer = '';
  if (isMobile) {
    activeScannerSearchContainer = '';
  } else {
    activeScannerSearchContainer = 'activeScannerSearchContainerNotMobile';
  }
  return {
    activeScannerSearchContainer,
  }
};
const showMobileViewOrNot = showMobileViewOrNotFunction();

const timeRangeSource = ref([
  { id: 1, disp: '1 Hour', value: 1 },
  { id: 2, disp: '2 Hours', value: 2 },
  { id: 3, disp: '3 Hours', value: 3 },
  { id: 4, disp: '4 Hours', value: 4 },
  { id: 5, disp: '5 Hours', value: 5 },
  { id: 6, disp: '6 Hours', value: 6 },
  { id: 7, disp: '7 Hours', value: 7 },
  { id: 8, disp: '8 Hours', value: 8 },
  { id: 9, disp: '9 Hours', value: 9 },
  { id: 10, disp: '10 Hours', value: 10 },
  { id: 11, disp: '11 Hours', value: 11 },
  { id: 12, disp: '12 Hours', value: 12 },
]);
const timeRangeSelection = ref(1);
const disable5MinInterval = ref(false);
const minuteIntervalSource = ref([
  { id: 1, disp: '5 Minutes', value: 5, disabled: disable5MinInterval },
  { id: 2, disp: '15 Minutes', value: 15 },
  { id: 3, disp: '30 Minutes', value: 30 },
  // { id: 4, disp: '45 Minutes', value: 45 },
  { id: 5, disp: '60 Minutes', value: 60 },
]);
const minuteIntervalSelection = ref(15);
const timeRangeSelectToDisable5Mins = (data) => {
  switch (data.value) {
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
      disable5MinInterval.value = true;
      minuteIntervalSelection.value = 15;
      break;
    case 1:
      disable5MinInterval.value = false;
      break;
  }
};

const getRecentScannerStats = () => {
  loadingVisible.value = true;

  //TYLER STUFF
  for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups.length; i++){
    ActiveScannerHeader.value.ScannerGroups[i].data = [];
    for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup.length; c++){
      ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].data = [];
    }
  }
  //HoursTimeRange
  //IntervalMinutes
  let recentSearchParams = {
    HoursTimeRange: timeRangeSelection.value,
    IntervalMinutes: minuteIntervalSelection.value
  }
  //END TYLER STUFF
  axios.get('api/Stats/ScannerStats', { params: recentSearchParams }).then((resp) => {
    let scannerIndexArray = {};
    // console.log(resp.data);
    if (resp.status === 200) {
      for (let t = 0; t < resp.data.data.TimeSpanHeader.length; t++) {
        //TYLER STUFF
        //timeDisplay
        for(let i = 0; i < ActiveScannerHeader.value.ScannerGroups.length; i++){
          for(let c = 0; c < ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup.length; c++){
            
            //only construct the TOTALS DATA one time
            if(t == 0){
              //create an object that is the scanner name and initalize it as an empty array;
              // i = the index of the scanners ScannerGroups
              // c = the index of the scanner itself inside of scannersInGroup
              scannerIndexArray[ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName] = [i, c];

              let totalsObj = {};
              totalsObj['scannerName'] = ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].scannerName;
              //construct the scanner totals based on the default scanner stats
              for(let x = 0; x < ActiveScannerHeader.value.DefaultScannerStatus.length; x++){
                totalsObj[ActiveScannerHeader.value.DefaultScannerStatus[x].name] = 0;
              }
              //construct the scanner groups extra statuses
              for(let x = 0; x < ActiveScannerHeader.value.ScannerGroups[i].extraStatuses.length; x++){
                totalsObj[ActiveScannerHeader.value.ScannerGroups[i].extraStatuses[x].name] = 0;
              }
              ActiveScannerHeader.value.ScannerGroups[i].data.push(totalsObj);
            }

            //construct the data per scanner per timeslot
            let scannerObj = {};
            scannerObj['timeDisplay'] = resp.data.data.TimeSpanHeader[t].StartDate.substring(0, resp.data.data.TimeSpanHeader[t].StartDate.length - 4)
            //construct the scanner totals based on the default scanner stats
            for(let x = 0; x < ActiveScannerHeader.value.DefaultScannerStatus.length; x++){
              scannerObj[ActiveScannerHeader.value.DefaultScannerStatus[x].name] = 0;
            }
            //construct the scanner groups extra statuses
            for(let x = 0; x < ActiveScannerHeader.value.ScannerGroups[i].extraStatuses.length; x++){
              scannerObj[ActiveScannerHeader.value.ScannerGroups[i].extraStatuses[x].name] = 0;
            }
            ActiveScannerHeader.value.ScannerGroups[i].scannersInGroup[c].data.push(scannerObj);
            
          }
        }
        //END TYLER STUFF
      }

      for (let s = 0; s < resp.data.data.ScannerData.length; s++) {
        //TYLER STUFF
        if(typeof scannerIndexArray[resp.data.data.ScannerData[s].ScannerPoint] === 'undefined'){
          console.error(`Scanner: ${resp.data.data.ScannerData[s].ScannerPoint} Does Not Exist In ActiveScannersData OBJECT`);
        }
        else{

          let ScannerGroupsIndex = scannerIndexArray[resp.data.data.ScannerData[s].ScannerPoint][0];
          let scannersInGroupIndex = scannerIndexArray[resp.data.data.ScannerData[s].ScannerPoint][1];

          ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].scannersInGroup[scannersInGroupIndex].data[resp.data.data.ScannerData[s].MasterRecordsIndex][resp.data.data.ScannerData[s].StatName] += resp.data.data.ScannerData[s].Count;

          ActiveScannerHeader.value.ScannerGroups[ScannerGroupsIndex].data[scannersInGroupIndex][resp.data.data.ScannerData[s].StatName] += resp.data.data.ScannerData[s].Count;
        }
        //END TYLER STUFF
      }
      loadingVisible.value = false;
    } else {
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  })
  .finally(()=>{
    selectionChangedKey.value++;
  });
};
// getRecentScannerStats();

const getLegendLabel = () => {
  return 'Amount In Each Time Frame';
};

const graphToolTipFunctionRecentScans = (data) => {
  return `${data.seriesName}: ${data.value}`;
};

const onExporting = (e) => {
  // let today = new Date();
  // let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  // let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  // let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Recent Scanner Stats ${minuteIntervalSelection.value}Minute Intervals In ${timeRangeSelection.value}Hours.xlsx`);
        });
  });
  e.cancel = true;
};

</script>

<style lang="scss">
.scannerTitleLink {
  cursor: pointer;
  color: darkgray;
  text-decoration: underline;
}
.scannerDataLink{
  cursor: pointer;
  color: lightgray;
}
.activeScannerSearchContainerNotMobile {
  display: flex;
  align-items: center;
}
.gridLinkRecentScanStats {
  cursor: pointer;
  color: lightgray;
  text-decoration: underline;
}
.gridTotals {
  color: #09b0d2!important;
  text-decoration: underline;
}
</style>

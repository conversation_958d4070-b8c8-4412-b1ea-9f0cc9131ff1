<template>
  <div>
    <!-- <h2 class="content-block">Hospital Lane Alerts</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div style="display: flex; justify-content: space-between; margin-bottom: 50px;">
          <div>
            <DxButton 
              text="Refresh"
              type="success"
              styling-mode="contained"
              icon="refresh"
              @click="getEmailDataForAlerts"
            />
          </div>
          <div v-if="!emailAlertsOnOrOff">
            <h3>Email Alerts Turned Off</h3>
          </div>
          <div>
            <DxButton 
              :text="settingsBtnTitle"
              :type="settingsBtnType"
              styling-mode="contained"
              @click="toggleEmailAlerts"
            />
          </div>
        </div>
        <div>
          <DxDataGrid
            v-model:data-source="emailDataSource"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            :column-hiding-enabled="false"
            :column-auto-width="true"
            style="margin-top: 15px;"
            ref="addRowToEmailGridRef"
            @init-new-row="initNewRow"
            @row-inserted="addNewEmail"
            @row-updated="UpdateRow"
            @row-removed="DeleteRow"
          >
            <DxPaging :enabled="false"/>
            <DxScrolling mode="standard"/>
            <DxFilterRow
              :visible="true"
            />
            <DxEditing     
              :allow-updating="true"
              :allow-deleting="true"
              :allow-adding="true"
              mode="row"
            />
            <DxColumn 
              data-field="email_address"
              caption="Email"
              alignment="left"
            />
            <DxColumn 
              data-field="first_name"
              caption="First Name"
              alignment="left"
            />
            <DxColumn 
              data-field="last_name"
              caption="Last Name"
              alignment="left"
            />
            <DxColumn 
              data-field="email_address_enabled"
              caption="Enabled"
              alignment="center"
              :enable-three-state-behavior="false"
            />
            <DxToolbar>
              <DxItem
                location="after"
                template="addRowTemplateNewEmail"
              />
            </DxToolbar>
              <template #addRowTemplateNewEmail>
                <DxButton
                  text="Add New Email Address"
                  type="default"
                  styling-mode="contained"
                  icon="add"
                  @click="customAddRowBtnClickNewEmail"
                />
              </template>
          </DxDataGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import auth from '../auth';
import { useRouter } from 'vue-router';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import notify from 'devextreme/ui/notify';
import DxButton from 'devextreme-vue/button';
import { confirm } from 'devextreme/ui/dialog';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxScrolling,
  DxEditing,
  DxFilterRow,
  DxToolbar,
  DxItem,
} from 'devextreme-vue/data-grid';

const loadingVisible = ref(false);
databaseName.checkWebsiteVersion();
const router = useRouter();

function arrClone(source){
  return JSON.parse(JSON.stringify(source));
}

const userToken = ref('');
const userName = ref('');
const userSecurity = ref('');
const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  loadingVisible.value = true;
  //console.log(user.data);
  if(user.data.userSecurity === 'administrator' || user.data.userSecurity === 'programmer' || user.data.userSecurity === 'editor'){
    userToken.value = user.data.userJwtToken;
    userSecurity.value = user.data.userSecurity;
    userName.value = user.data.userIdName;
    loadingVisible.value = false;
  } else {
    alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");
    loadingVisible.value = false;
    router.push('/login-form?redirect=/jackpot-lane-alerts');
  }
  if (user.data.userSecurity === 'programmer') {
    isProgrammerLevel.value = true;
  } else {
    isProgrammerLevel.value = false;
  }
}).catch(error=>{
  loadingVisible.value = false;
  //console.log(error.response);
  if(error.response){
    alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else if(error.request){
    alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else{
    alert('Unknown error\nMake sure that you and the server have not lost connection');
    return;
  }
});

const emailDataSource = ref([]);
const emailAlertsOnOrOff = ref(Boolean);
const settingsBtnTitle = ref('Turn Alerts Off');
const settingsBtnType = ref('default');
const getEmailDataForAlerts = () => {
  loadingVisible.value = true;
  axios.get('api/Admin/EmailsForHospital').then((resp) => {
    //console.log(resp.data);
    emailDataSource.value = resp.data.data.Emails;
    emailAlertsOnOrOff.value = resp.data.data.Settings[0].setting_active;
    if (emailAlertsOnOrOff.value) {
      settingsBtnTitle.value = 'Turn Alerts Off';
      settingsBtnType.value = 'danger';
    } else {
      settingsBtnTitle.value = 'Turn Alerts On';
      settingsBtnType.value = 'default';
    }
    loadingVisible.value = false;
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    } else {
      if(error.response){
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the server have not lost connection');
        return;
      }
    }
  });
};
getEmailDataForAlerts();

//Add New Grid Row Button
const addRowToEmailGridRef = ref(null);
const customAddRowBtnClickNewEmail = () => {
  const dataGrid = addRowToEmailGridRef.value.instance;
  dataGrid.addRow();
};
const initNewRow = (e) => {
  e.data.email_address_enabled = true;
};
const emailReg = /^\w+@[a-zA-Z_]+?\.[a-zA-Z]{2,3}$/;
//ADD NEW EMAIL
const addNewEmail = (e) =>{
  if(typeof e.data.email_address === 'undefined')
  {
    notify('You Must Specify An Email Address', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  if (!emailReg.test(e.data.email_address)) 
  {
    notify('You Must Enter A Proper Email Address', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  if(typeof e.data.first_name === 'undefined')
  {
    notify('You Must Specify A First Name', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  if(typeof e.data.last_name === 'undefined')
  {
    notify('You Must Specify A Last Name', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }

  let returnJSON = arrClone(e.data);
  loadingVisible.value = true;
  axios({
    method: 'PUT',
    url: 'api/Admin/EmailsForHospital',
    data: returnJSON,
    headers:{
      'Authorization': `Bearer ${userToken.value}`,
      'Content-Type': 'application/json'
    }
  }).then((resp)=>{
    notify(resp.data.message, 'success', 6000);
  }).catch(error=>{
    if(error.response){
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the serve have not lost connection');
      return;
    }
  }).finally(()=>{
    loadingVisible.value = false;
    getEmailDataForAlerts();
  });
};

//UPDATE ROWS
const UpdateRow = (e) =>{
  if(e.data.email_address === '')
  {
    notify('You Must Specify An Email Address', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  if (!emailReg.test(e.data.email_address)) 
  {
    notify('You Must Enter A Proper Email Address', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  if(e.data.first_name === '')
  {
    notify('You Must Specify A First Name', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  if(e.data.last_name === '')
  {
    notify('You Must Specify A Last Name', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }

  let ds_index = emailDataSource.value.findIndex(p => p.id_kvkemailsforhospital == e.data.id_kvkemailsforhospital);
  if(ds_index == -1)
  {
    notify('Error KOZ Unable To Find Index Of Altered Record In Data Set', 'error', 10000);
    getEmailDataForAlerts();
    return;
  }
  else
  {
    let returnJSON = arrClone(e.data);
    loadingVisible.value = true;
    axios({
      method: 'PATCH',
      url: 'api/Admin/EmailsForHospital',
      data: returnJSON,
      headers:{
        'Authorization': `Bearer ${userToken.value}`,
        'Content-Type': 'application/json'
      }
    }).then((resp)=>{
      notify(resp.data.message, 'success', 6000);
    }).catch(error=>{
      if(error.response){
        notify(error.response.data.message, 'error', 10000);
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the serve have not lost connection');
        return;
      }
    }).finally(()=>{
      loadingVisible.value = false;
      getEmailDataForAlerts();
    });
  }
};

//DELETE ROW
const DeleteRow = (e) =>{
  let returnJSON = arrClone(e.data);
  loadingVisible.value = true;
  axios({
    method: 'DELETE',
    url: 'api/Admin/EmailsForHospital',
    data: returnJSON,
    headers:{
      'Authorization': `Bearer ${userToken.value}`,
      'Content-Type': 'application/json'
    }
  }).then((resp)=>{
    notify(resp.data.message, 'success', 6000);
  }).catch(error=>{
    if(error.response){
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the serve have not lost connection');
      return;
    }
  }).finally(()=>{
    loadingVisible.value = false;
    getEmailDataForAlerts();
  });
};


//TURN EMAIL ALERTS ON OR OFF
const toggleEmailAlerts = () => {
  let boolToSend = {
    Setting: false
  };
  let confirmMsgWord = '';
  if (emailAlertsOnOrOff.value) {
    boolToSend.Setting = false;
    confirmMsgWord = 'Off';
  } else {
    boolToSend.Setting = true;
    confirmMsgWord = 'On';
  }
  let settingChangeConfirm = confirm(`Are You Sure You Want To Turn Alerts <u>${confirmMsgWord}</u>`, 'Confirm Change');
  settingChangeConfirm.then((dialogResult) => {
    if (dialogResult) {
      loadingVisible.value = true;
      axios({
        method: 'PATCH',
        url: 'api/Admin/EmailSetting',
        params: boolToSend,
        headers:{
          'Authorization': `Bearer ${userToken.value}`,
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        if (resp.status === 200) {
          getEmailDataForAlerts();
          notify(resp.data.message, 'success', 4000);
          loadingVisible.value = false;
        } else {
          notify(resp.data.message, 'error', 10000);
          getEmailDataForAlerts();
          loadingVisible.value = false;
        }
      }).catch(error=>{
        loadingVisible.value = false;
        if (error.response.status === 500) {
          notify(error.response.data.message, 'error', 10000);
          getEmailDataForAlerts();
          return;
        } else {
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the server have not lost connection');
            return;
          }
        }
      })
    } else {
      notify('Email Alert Setting NOT Changed', 'info', 5000);
      getEmailDataForAlerts();
    }
  })
};
</script>

<style lang="scss">
</style>

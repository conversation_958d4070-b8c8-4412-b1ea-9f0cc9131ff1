<template>
  <div>
    <DxLoadPanel
      v-model:visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <h4>Transaction Log</h4>
    <div>
      <DxDataGrid
        class="gridBorder"
        :data-source="lpnTransLogDataSource"
        :row-alternation-enabled="true"
        :show-borders="true"
        :column-auto-width="true"
        :allow-column-reordering="true"
      >
        <DxPaging :page-size="5"/>
        <DxPager
          :show-page-size-selector="true"
          :allowed-page-sizes="[5, 10, 20]"
          :show-info="true" 
        />
        <DxFilterRow
          :visible="true"
        />
          <DxColumn 
            data-field="transaction_datetime"
            caption="Date"
            data-type="datetime"
            alignment="left"
            name="transLogFirst"
          />
          <DxColumn 
            data-field="transaction_datetime"
            caption="Seconds.Milliseconds"
            v-model:visible="checkProgrammerLevel"
            name="transLogSecond"
            cell-template="isPro"
          />
            <template #isPro="{data}">
              <div>{{ detailViewSecsAndMilli(data.text) }}</div>
            </template>
          <DxColumn 
            data-field="transaction_code"
            caption="Trans Code"
            alignment="left"
          />
          <DxColumn 
            data-field="scannerpoint"
            caption="Scanner"
            alignment="left"
          />
          <DxColumn 
            data-field="plc"
            caption="PLC"
            alignment="left"
          />
          <DxColumn 
            data-field="transaction_message"
            caption="Message"
            alignment="left"
          />
      </DxDataGrid>
    </div>
    <!-- <h4>Pick Zones</h4>
    <DxDataGrid
        class="gridBorder"
        :data-source="pickZonesDataSource"
        :row-alternation-enabled="true"
        :show-borders="true"
        :column-auto-width="true"
        :allow-column-reordering="true"
      >
        <DxPaging :page-size="5"/>
        <DxPager
          :show-page-size-selector="true"
          :allowed-page-sizes="[5, 10, 20]"
          :show-info="true" 
        />
        <DxFilterRow
          :visible="true"
        />
          <DxColumn 
            data-field="pick_added_datetime"
            caption="Import Date"
            data-type="datetime"
            alignment="left"
            name="transLogFirst"
          />
          <DxColumn 
            data-field="pick_zone"
            caption="Pick Zone"
            alignment="left"
          />
          <DxColumn 
            data-field="pick_zone_complete_time"
            caption="Completed Date"
            data-type="datetime"
            alignment="left"
            name="transLogFirst"
          />
      </DxDataGrid> -->

  </div>
</template>

<script setup>
import { ref, defineProps, computed } from 'vue';
import DxLoadPanel from 'devextreme-vue/load-panel';
import axios from 'axios';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxFilterRow,
  DxPager,
  // DxScrolling,
} from 'devextreme-vue/data-grid';


const loadingVisible = ref(false);
const props = defineProps({
  templateData: Object
});

const checkProgrammerLevel = computed(() => {
  return props.templateData.data.isProgrammerLevel
});
const searchParams = ref({
  LPNBarcode: props.templateData.data.lpn_barcode,
  isReporting: props.templateData.data.isReporting
});

const lpnTransLogDataSource = ref([]);
const pickZonesDataSource = ref([]);
const getLPNTransLogData = () => {
  //EZTraceMasterDetails
  //TransLog
  //PickZones
  loadingVisible.value = true;
  axios.get('api/Order/EZTraceMasterDetails', { params: searchParams.value }).then((resp) => {
    if (resp.status === 200) {
      lpnTransLogDataSource.value = resp.data.data.TransLog;
      pickZonesDataSource.value = resp.data.data.PickZones;
      loadingVisible.value = false;
    } else {
      lpnTransLogDataSource.value = [];
      pickZonesDataSource.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    // console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
getLPNTransLogData();

const detailViewSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};
</script>

<style lang="scss">
.gridBorder {
  border: 1px solid white;
}
</style>


<template>
  <form class="login-form" @submit.prevent="onSubmit">
    <dx-form :form-data="formData" :disabled="loading">
      <dx-item
        data-field="email"
        editor-type="dxTextBox"
        :editor-options="{ stylingMode: 'filled', placeholder: 'Email or Username' }"
      >
        <dx-required-rule message="Email or Username is required" />
        <dx-label :visible="false" />
      </dx-item>
      <dx-item
        data-field='password'
        editor-type='dxTextBox'
        :editor-options="{ stylingMode: 'filled', placeholder: 'Password', buttons: seePWordButton, mode: passwordMode }"
      >
        <dx-required-rule message="Password is required" />
        <dx-label :visible="false" />
      </dx-item>
      <dx-button-item>
        <dx-button-options
          width="100%"
          type="default"
          template="signInTemplate"
          :use-submit-behavior="true"
        >
        </dx-button-options>
      </dx-button-item>
      <dx-button-item>
        <dx-button-options
          text="VIEW ONLY"
          width="100%"
          styling-mode="outlined"
          :on-click="viewOnlyLogin"
        />
      </dx-button-item>
      <template #signInTemplate>
        <div>
          <span class="dx-button-text">
            <dx-load-indicator v-if="loading" width="24px" height="24px" :visible="true" />
            <span v-if="!loading">Sign In</span>
          </span>
        </div>
      </template>
    </dx-form>
  </form>
</template>

<script setup>
import { ref} from 'vue';
import { useRouter, useRoute } from 'vue-router';
import DxLoadIndicator from "devextreme-vue/load-indicator";
import DxForm, {
  DxItem,
  DxRequiredRule,
  DxLabel,
  DxButtonItem,
  DxButtonOptions
} from "devextreme-vue/form";
import notify from 'devextreme/ui/notify';

import auth from "../auth";

const formData = ref({});
const loading = ref(false);
const router = useRouter();
const route = useRoute();

const passwordMode = ref('password');
const seePWordButton = ref(
  [  
    {
      name: 'revealPWord',
      location: 'after',
      options: {
        icon: 'eyeopen',
        onClick: ()=>{
          showHidePWord();
        }
      }
    }
  ]
);
const showHidePWord = () => {
  passwordMode.value = passwordMode.value === "text" ? "password" : "text";
};

const viewOnlyLogin = () => {
  loading.value = true;
  auth.defaultLogIn();
  router.push(route.query.returnUrl || 'home');
};


const onSubmit = async () => {
  const { email, password } = formData.value;
  loading.value = true;

  const result = await auth.logIn(email, password);
  if (!result.isOk) {
    loading.value = false;
    notify(result.message, "error", 2000);
  } else {
    loading.value = false;

    // Check if user is using default credentials or has had password reset
    if(result.data.userIdName === result.data.userPass){
      alert("You have either logged in for the first time or your password has been reset.\nPlease change your password.");
      router.push("/profile");
      return; // Exit early to avoid the other redirect logic
    }
    
    
    // Check if there was a redirect URL
    const returnUrl = route.query.returnUrl;
    
    if (returnUrl) {
      try {
        // Try to resolve the route
        const targetRoute = router.resolve(returnUrl);
        if (targetRoute && targetRoute.matched && targetRoute.matched.length > 0) {
          // Find required roles from the matched route
          const userSecurity = [];
          targetRoute.matched.forEach(record => {
            if (record.meta && record.meta.userSecurity) {
              userSecurity.push(...record.meta.userSecurity);
            }
          });
          
          // If the route requires specific roles but user doesn't have them
          if (userSecurity.length > 0 && !auth.hasPermission(userSecurity)) {
            // console.log(route.query.returnUrl)
            notify(`Access denied: You don't have permission to the this module. Returning home.`, "error", 4000);
            router.push("/home");
            return;
          }
        }
        
        // User has permission or no specific permission needed, proceed with redirect
        router.push(returnUrl);
        
      } catch (error) {
        console.error("Error resolving return URL:", error);
        notify("Could not redirect to the requested page", "error", 3000);
        router.push("/home");
      }
    } else {
      router.push("/home");
    }
  }
};
</script>

<style lang="scss">
@import "../themes/generated/variables.base.scss";

.login-form {
  flex: auto;
  .link {
    text-align: center;
    font-size: 16px;
    font-style: normal;

    a {
      text-decoration: none;
    }
  }

  .form-text {
    margin: 10px 0;
    color: rgba($base-text-color, alpha($base-text-color) * 0.7);
  }
}
</style>

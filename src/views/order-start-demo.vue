<template>
	<div ref="contentBlockRef" class="content-block dx-card responsive-paddings" :class="{ 'maximized': isMaximized }">
		<!-- Container controls -->
		<div style="display: flex; flex-direction: row-reverse; gap: 10px; position: absolute;top:10px;right:10px;">
			<DxButton class="custom-button" :width="22" :height="22"  v-if="isMaximized" type='default' styling-mode='outlined' icon='collapse'  @click='toggleMaximize' />
			<DxButton class="custom-button" :width="22" :height="22"  v-else type='default' styling-mode='outlined' icon='expandform' @click='toggleMaximize' />

			<DxButton class="custom-button" :width="22" :height="22" type='default' styling-mode='outlined' icon='plus' @click="zoomContainer.decreaseZoom()" />
			<div style="text-align: center; align-self: center; font-size: x-small;">{{ (100 /currentZoom).toFixed()}}%</div>			
			<DxButton class="custom-button" type='default' :width="22" :height="22" styling-mode='outlined' icon='minus' @click="zoomContainer.increaseZoom()" />


		</div>

		<ZoomableContainer ref="zoomContainer" :hide-controls="true">
			
			<!-- Connect to station view -->
			<div v-if="!isSocketConnected" style="flex: auto; position: relative;; justify-items: center; align-content: center;">
				
				<!-- <SkeletonLoader style="z-index: 1; position: absolute; top: 0;" :isLoading="true" ></SkeletonLoader> -->
				
				<div class="card" style="z-index: 2; background-color: #31303A; width: auto; height: auto;transform: scale(1); font-size: small; box-shadow: 2px 4px 7px 1px rgba(0,0,0,0.25);">
					
					<div style="border-bottom: 1px solid #515159; margin-bottom: 20px;">Connect to a station</div>

					<div style="margin-left: 3px;">Select station</div>
					<div style="display: flex; gap: 8px;">
						<DxSelectBox style="border-radius: 5px; " styling-mode="filled" display-expr="disp" 
							:data-source="stations" 
							v-model="selectedStation">
						</DxSelectBox>

						<DxButton text="Connect" @click="onStationConnect()" style="border-radius: 5px;	height: 32px;" styling-mode="contained" type="default" ></dxButton>
					</div>
				</div>
			</div>

			<!-- Connected view -->
			<div v-else class="container" style="position: relative;">
				<div class="row">
					<!-- station connect -->
					<div style="display:flex; flex-wrap: wrap; flex:0%;">
						<div style="flex: 1 1 100%; margin-left: 3px;">Station</div>
						<!-- <DxSelectBox style="border-radius: 5px 0 0 5px;" disabled="true" styling-mode="filled" value-expr="value" display-expr="disp" 
							:data-source="stations" 
							v-model="selectedStation">
						</DxSelectBox> -->
						<DxTextBox style="border-radius: 5px 0 0 5px;" :disabled="true" styling-mode="filled" :value="selectedStation.disp"></DxTextBox>
						<DxButton v-if="isSocketConnected" text="Disconnect" style="border-radius: 0 5px 5px 0;	height: 32px;" styling-mode="contained" type="default" 
							@click="onStationDisconnect()" >
						</DxButton>
						<!-- <dxButton v-else text="Connect" style="border-radius: 0 5px 5px 0;	height: 32px;" styling-mode="contained" type="default" ></dxButton> -->
					</div>
					

					<!-- space fill -->
					<div style="flex: auto;"></div>

					<div style="display: flex; align-items: end; gap: 20px;">
						<div>
							<div>Queue Count</div>
							<span>In Queue: </span><span class="accent">{{ filteredOrders.length - scannedOrders.length }}</span>
						</div>

						<!-- order stats -->
						<div>
							<div>Completed Orders</div>
							<div>
								<span>Today</span>: <span class="accent">{{scannedOrders.length}}</span> | <span>Last Hour</span>: <span class="accent">{{lastHourOrders}}</span>
							</div>
						</div>
					</div>
				</div>

				<!-- the queue -->
				<div style="flex: auto;" class="row">
					<div style="display: flex; flex: auto; flex-direction: column; justify-content: center; align-items: center; align-content: center;">
						<div style="display: flex; align-items: center; gap: 25px;">
							
							<!-- order cards -->
							<TransitionGroup name="card-slide" tag="div" style="display: flex; align-items: center; gap: 25px;">
								<div v-if=" (filteredOrders.length - scannedOrders.length )== 0">No orders in queue with the current filters</div>
								<div v-for="order in getLastNRecords(3)" :key="order.id">
									<div class="card" :class="getCardStatus(order)" > 
										<!-- <div style="position: absolute; top: 0;">Current</div> -->
										<div style="font-size: x-large; text-align: center;">{{ order.container }}</div> 
										<div style="font-size: x-large; text-align: center">{{ order.size }}</div>
										<!-- steps -->
										<div>
											<div style="text-align: center; margin-top: 10px;">Steps</div>
											<div v-for="step in order.steps" style="text-align: center;" :key="step">{{ step }}</div>
											<div style="text-align: center; margin-top: 10px;" class="accent">{{ order.scannedBarcode? '#'+ order.scannedBarcode: order.scannedBarcode  }}</div>
											<!-- `reprint action -->
											<div style="text-align: center; margin-top: 10px;">
												<dxButton v-if="order.id == currentOrder.id && order.container != 'Tote'" text="Reprint" styling-mode="contained" type="default" @click="onReprint(order)" ></dxButton>
											</div>
										</div>
									</div>
								</div>
							</TransitionGroup>

						</div>

						
						<div style="display: flex; flex-direction: column; gap: 10px; margin-top: 20px; align-items: center;">
							<dxTextBox  placeholder="Scan or Enter Barcode" styling-mode="filled" style="width: 280px; height: 45px;"
								:onEnterKey="onBarcodeScanned"
								v-model="barcodeInput">
							</dxTextBox>
							<!-- connection status -->
							<div :style="{ display: 'flex', alignItems: 'center', gap: '8px' }">
								<div :style="{ width: '10px', height: '10px', borderRadius: '50%', backgroundColor: isServerConnected ? 'green' : 'red' }"></div>
								<span>Server {{ isServerConnected ? 'Connected' : 'Disconnected' }}</span>
							</div>
						</div>
					
					</div>
				</div>

				<!-- log / filters / queue table -->
				<div class="row">
					<!-- log -->
					<div style="flex: auto;">
						<div style="padding-left: 3px; padding-bottom: 2px;">Scanned Log</div>
						<div class="table-container">
							<table>
								<thead> <tr><th>Time</th> <th>Type</th> <th>Barcode</th></tr></thead>
								<tbody>
									<tr v-for="item in [...scannedOrders].reverse()" :key="item.id">
										<td>{{ new Date(item.scannedDateTime).toLocaleTimeString() }}</td>
										<td>{{ item.container }} {{ item.size }}</td>
										<td>{{ item.scannedBarcode }}</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>

					<!-- Filters -->
					<div style="margin: 0 20px; align-content: center;">
						<div style="padding-bottom: 2px; ">Active Filters</div>
						<div v-for="item in activeStationFilters" :key="item.type">
							<span style="display: inline-block; width: 10px; height: 10px;border-radius: 50%; margin-right: 5px;" class="bg-accent"></span>
							<span>{{ item.type }}: </span> 
							<span class="accent">{{ item.values.join(', ') }}</span>
						</div>
					</div>
					
					<!-- queue tables-->
					<div style="flex: auto; display: flex; flex-direction: row; gap: 10px;">

						<!--  container queue -->
						<div style="flex: auto;">
							<div style="padding-left: 3px; padding-bottom: 2px;">Container Queue</div>
							<div class="table-container">
								<table>
									<thead> <tr><th>Type</th> <th>Filtered / All</th></tr></thead>
									<tbody>
										<tr v-for="item in containerQueue" :key="item">
											<td>{{ item.container == 'Box' ? item.size: item.container}}</td>
											<td>{{ item.filteredCount }} / {{ item.count }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<!--  aisle queue -->
						<div style="flex: auto;">
							<div style="padding-left: 3px; padding-bottom: 2px;">Aisle Queue</div>
							<div class="table-container">
								<table>
									<thead> <tr><th>Aisle</th> <th>count</th></tr></thead>
									<tbody>
										<tr v-for="item in aisleQueue" :key="item">
											<td>{{ item.aisle }}</td>
											<td>{{ item.filteredCount }} / {{ item.count }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

					</div>
				</div>
			</div>
		</ZoomableContainer>
	</div>
</template>

<script setup>

	import { DxSelectBox } from 'devextreme-vue';
	import { DxButton } from 'devextreme-vue/button';
	import { DxTextBox } from 'devextreme-vue/text-box';
	import notify from 'devextreme/ui/notify';
	import { ref, onMounted, computed, nextTick, onUnmounted } from 'vue';
	import ZoomableContainer from '../components/zoomable-container.vue'
	import SkeletonLoader from '../components/skeleton-loader.vue'

	/* 
		COMPONENT-LEVEL ELEMENT REFS  
	*/
	const zoomContainer = ref()
	/* 
		COMPONENT-LEVEL VARIABLES  
	*/
	const isServerConnected = ref(true);
	const isSocketConnected = ref(false);
	const barcodeInput = ref('');
	const selectedStation = ref({}); 
	const ogArray = ref([]);
	const isMaximized = ref(false)
	const notifyOptions = ref(
		{
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }},
			
			width: 'auto',
			animation: {
				show: { type: "fade", duration: 800, from: 0, to: 1 },
				hide: { type: "fade", duration: 800, from: 1, to: 0 }
			}
		}
	)
	// const activeStationFilters = ref([ { type: 'Shipping', values: ['Domestic',] }, { type: 'Container', values: ['Box', 'Tote'] }, { type: 'Carrier', values: ['FedEx'] }, { type: 'Size', values: ['8x8x8', '8x6x6'] }, { type: 'Aisle', values: ['3', '1', '4'] }]);

	const stations = ref([
		{ disp: 'Station 1', value: 1 , filters: [ { type: 'Shipping', values: ['Domestic',] }, { type: 'Container', values: ['Box', 'Tote'] }, { type: 'Carrier', values: ['FedEx'] }, { type: 'Size', values: ['8x8x8', '8x6x6'] }, { type: 'Aisle', values: ['3', '1', '4'] }]},
		{ disp: 'Station 2', value: 2 , filters: [ { type: 'Shipping', values: ['International',] }, { type: 'Container', values: ['Box'] }, { type: 'Carrier', values: ['DHL'] }, { type: 'Size', values: ['8x8x8', '8x6x6'] }, { type: 'Aisle', values: ['3', '1', '4'] }]},
		{ disp: 'Station 3', value: 3 , filters: [ { type: 'Shipping', values: ['Domestic',] }, { type: 'Container', values: ['Box', 'Tote'] }, { type: 'Carrier', values: ['USPS'] }, { type: 'Size', values: ['8x8x8', '8x6x6'] }, { type: 'Aisle', values: ['3', '1', '4'] }]},
	]);

	const orders = ref([
		{ id: 1, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'1', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: 453549, scannedDateTime: '2025-02-12 1:30:00' },
		{ id: 2, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'1', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: 453550, scannedDateTime: '2025-02-12 1:30:00' },
		{ id: 3, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'2', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: 453551, scannedDateTime: '2025-02-12 1:30:00' },
		{ id: 4, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'2', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: 453552, scannedDateTime: '2025-02-12 1:30:00' },
		{ id: 5, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'1', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: 453553, scannedDateTime: '2025-02-12 1:30:00' },
		{ id: 6, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'3', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null },
		{ id: 7, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'3', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null},
		{ id: 8, container: 'Box', size: '8x6x6', shipping: 'Domestic', carrier: 'FedEx', aisle:'3', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null},
		{ id: 9, container: 'Box', size: '8x6x6', shipping: 'International', carrier: 'DHL', aisle:'3', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null },
		{ id: 10, container: 'Tote', size: null, shipping: 'Domestic', carrier: 'FedEx', aisle:'3', steps: ['1 Scan Tote'], scannedBarcode: null, scannedDateTime: null },
		{ id: 11, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'USPS', aisle:'4', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null  },
		{ id: 12, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'4', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null  },
		{ id: 13, container: 'Box', size: '8x8x8', shipping: 'Domestic', carrier: 'FedEx', aisle:'5', steps: ['1 Make Box', '2 Apply Label', '3 Scan Label'], scannedBarcode: null, scannedDateTime: null  },
		{ id: 14, container: 'Tote', size: null, shipping: 'Domestic', carrier: 'USPS', aisle:'3', steps: ['1 Scan Tote'], scannedBarcode: null, scannedDateTime: null },
	]);


	/* 
	COMPONENT-LEVEL LIFECYCLE HOOKS  
	*/
	onMounted(() => {
		console.log('mounted');
		ogArray.value = [...orders.value];

		// get station from local storage if available and set it as default
		const station = localStorage.getItem('station');
		const isValidJSON = str => { try { JSON.parse(str); return true; } catch { return false; }}
		if (station){
			if (isValidJSON(station)) {
				selectedStation.value = stations.value.find(s => s.value === JSON.parse(station).value);
			}
		}
	});

	onUnmounted(() => {
		
	});

	/* 
		COMPONENT-LEVEL COMPUTED PROPERTIES  
	*/
	const activeStationFilters = computed(() => {

		const station = stations.value.find(s => s.value === selectedStation.value.value);
		return station ? station.filters : [];
	});

	// filtered orders by station filters
	const filteredOrders = computed(() => {
		if (!isSocketConnected.value) return [];
		const filtersByType = activeStationFilters.value.reduce((acc, { type, values }) => { 
			acc[type.toLowerCase()] = values; 
			return acc; 
		}, {});

		return orders.value.filter(item => {
			return Object.entries(filtersByType).every(([type, values]) => {
				const itemValue = type === 'size' ? item[type] ?? '' : item[type];
				return type === 'size' && itemValue === '' ? true : values.includes(itemValue);
			});
		});
	});

	const scannedOrders = computed(() => {
		return filteredOrders.value
			.filter(order => order.scannedBarcode !== null)
			.sort((a, b) => {
				const dateA = new Date(a.scannedDateTime.replace(' ', 'T'));
				const dateB = new Date(b.scannedDateTime.replace(' ', 'T'));
				return dateB.getTime() - dateA.getTime();
			});
	});

	const lastHourOrders = computed(() => {
		const oneHourAgo = new Date().getTime() - (60 * 60 * 1000); // One hour in milliseconds
		
		return scannedOrders.value.filter(order => {
			return order.timestamp > oneHourAgo;
		}).length;
	});

	const currentOrder = computed(() => {
		// Find the first order that hasn't been scanned yet from filtered orders
		return filteredOrders.value.find(order => order.scannedBarcode === null) || null;
	});

	const containerQueue = computed(() => {
		return Array.from(
			orders.value
				.filter(({ scannedBarcode }) => scannedBarcode === null) // Pre-filter processed items
				.reduce((acc, item) => {
					const { container, size } = item; 
					const key = `${container}-${size}`;
					const entry = acc.get(key) ?? { container, size, count: 0, filteredCount: 0 };

					entry.count++;

					// Use filteredOrders to check if item matches filters
					if (filteredOrders.value.some(filtered => filtered.id === item.id)) {
						entry.filteredCount++;
					}

					acc.set(key, entry);
					return acc;
				}, new Map())
		).map(([, value]) => value);
	});

	const aisleQueue = computed(() => {
		return Array.from(
			orders.value
				.filter(({ scannedBarcode }) => scannedBarcode === null) // Pre-filter processed items
				.reduce((acc, item) => {
					const { aisle } = item; 
					const key = `${aisle}`;
					const entry = acc.get(key) ?? { aisle, count: 0, filteredCount: 0 };

					entry.count++;

					// Use filteredOrders to check if item matches filters
					if (filteredOrders.value.some(filtered => filtered.id === item.id)) {
						entry.filteredCount++;
					}

					acc.set(key, entry);
					return acc;
				}, new Map())
		).map(([, value]) => value);
	});

	const currentZoom = computed(() => {
		return zoomContainer.value?.zoomLevel || 1
	})


	/* 
		COMPONENT-LEVEL FUNCTIONS
	*/

	const onStationConnect = () => {
		if (!selectedStation.value.value) {
			notify({ ...notifyOptions.value, message: 'Please select a station!', type: 'error', displayTime: 1000 });
			return;
		}

		// Set the station in local storage for use as default
		localStorage.setItem('station', JSON.stringify(selectedStation.value));
		isSocketConnected.value = true;

		notify({ ...notifyOptions.value, message: 'Connected to station ' + selectedStation.value.value, type: 'success', displayTime: 2000 });
	};

	const onStationDisconnect = () => {

		isSocketConnected.value = false;
		console.log('disconnecting', isSocketConnected.value);
		orders.value = ogArray.value 
		
		notify({ ...notifyOptions.value, message: 'Disconnected from station ' + selectedStation.value.value, type: 'success', displayTime: 2000 });
	};

	const getCardStatus = (order) => {
		return {
			'card-active': order.id == currentOrder.value.id,
			'card-previous': order.scannedBarcode != null,
			'card-next': order.scannedBarcode == null && order.id != currentOrder.value.id
		};
	};

	const getLastNRecords = (n) => {
		if (currentOrder.value == null) return;

		let indx = filteredOrders.value.findIndex(obj => obj.id == currentOrder.value.id);
		const half = Math.floor(n / 2);
		const startIndex = Math.max(0, indx - half);

		return filteredOrders.value.slice(startIndex, startIndex + n);
	};

	const onReprint = (order) => {
		notify({ ...notifyOptions.value, message: 'Reprinting label for order ' + order.id, type: 'success', displayTime: 2000 });

	};

	const onBarcodeScanned = (e) => {
		if (e.event.key != 'Enter') return 
		if (!barcodeInput.value) return;

		// Use currentOrder computed property
		if (currentOrder.value) {
			const now = new Date();

			const orderIndex = orders.value.findIndex(order => order.id === currentOrder.value.id);
			orders.value[orderIndex] = {
				...orders.value[orderIndex],
				scannedBarcode: barcodeInput.value,
				scannedDateTime: now.toISOString().slice(0, 19).replace('T', ' '),
				timestamp: now.getTime()
			};
		}

		// Clear the input
		barcodeInput.value = '';
	};

	const toggleMaximize = () => {
		isMaximized.value = !isMaximized.value

		if (!isMaximized.value)  {
			console.log('.content-block')
			notifyOptions.value.position = { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}
		} else {
			console.log('.container')
			notifyOptions.value.position = { my: "bottom", at: "bottom", of:  window, offset: { x: 0, y: -10 }}
		}
		
	}


</script>

<style scoped lang="scss">

	@import "../themes/generated/variables.additional.scss";

	.content-block {
		
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		&.maximized {
			position: fixed;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			z-index: 950;
			margin: 0;
		}
	}

	.container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		flex: 1 1 auto;
		overflow-y: auto;
		letter-spacing: 0.3px;
	}

	.row {
		display: flex;
		gap: 20px;
		flex-shrink: 0; 
	}

	/* color classes */
	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}

	
	/* cards */
	.card {
		position: relative;
		height: 280px;
		width: 220px;
		padding: 20px 40px;
		border-radius: 8px;
		align-content: center;
		background-color: rgba($base-bg-dark, 0.5);
		font-size: medium;
		letter-spacing: 2px;
		transition: transform .3s cubic-bezier(0.4, 0, 0.2, 1);

		&.card-active {
			border: 1px solid rgba($base-accent,0.6);
			transform: scale(1);
			background-color: $base-bg-dark;
			box-shadow: 0px 0px 21px -4px rgba(60,175,169,0.18);
			color: $base-text-color;
			&:before {
				content: 'Current';
				display: block;
				position: absolute;
				top: -30px;
				text-align: center;
				width: 100%;
				left: 0;
				font-size: smaller;
			}
		};

		&.card-previous {
			box-shadow: -2px 3px 7px 1px rgba(0,0,0,0.25);
			font-size: small;
			transform: scale(.8);
			&:before {
				content: 'Previous';
				display: block;
				position: absolute;
				top: -30px;
				text-align: center;
				width: 100%;
				left: 0;
			}
		};

		&.card-next {
			box-shadow: 2px 4px 7px 1px rgba(0,0,0,0.25);
			font-size: small;
			transform: scale(.8);
			&:before {
				content: 'Next';
				display: block;
				position: absolute;
				top: -30px;
				text-align: center;
				width: 100%;
				left: 0;
			}
		};
	}

	/* card transition classes */
	.card-slide-move {
		transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.card-slide-enter-active {
		transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
			// position: absolute;
	}

	.card-slide-leave-active {
			transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
			position: absolute;
			pointer-events: none;  /* Add this to prevent interaction during exit */
	}

	.card-slide-enter-from {
			opacity: 0;
			transform: translateX(100px) scale(0.1);
	}

	.card-slide-leave-to {
			opacity: 0;
			transform: translateX(-30px) scale(0.1);
	}

	/* Table style */
	.table-container {
		height: 150px;
		overflow-y: auto;
		border-radius: 8px;
		background-color: rgba($base-bg-dark, 0.3);
	}

	table {
		border-collapse: collapse;
		width: 100%;
		position: sticky;
		top: 0;
		z-index: 1;
	}

	th, td {
		border: none;
		padding: 1px 12px;
		text-align: left;
	}

	th {
		background-color: $base-bg-dark;
	}

	th:first-of-type {
		border-radius: 8px 0 0 8px;
	}

	th:last-of-type {
		border-radius: 0 8px 8px 0;
	}

	/* DX Controls Override  */
	.dx-texteditor {
		border: 0px;
		min-width: 120px;
		
		border-radius: 5px;
		background-color: $base-bg-dark;

	}
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: $base-bg-dark;
		opacity: 1;
	}
	.dx-texteditor::before {
		content: none;
	}
	.dx-texteditor::after {
		content: none;
	}
	
	.dx-button {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon  {
		min-width: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 8px !important;
		width: unset;
		height: unset;
	}

</style>
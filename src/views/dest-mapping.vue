<template>
  <div style="flex: auto;">
    <!-- <h2 class="content-block">Destination Mapping</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div>
          <DxSelectBox
            v-model:data-source="ScannerList"
            display-expr="displayValue"
            value-expr="scanner_name"
            styling-mode="underlined"
            style="margin-bottom: 20px;"
            :width="400"
            @value-changed="UpdateScanner"
            placeholder="Select Device..."
            :disabled="!webSocketConnected"
          />
        </div>
        <!-- Display the grid of mappings -->
        <div v-if="SelectedScannerObj.scanner_is_sorter == true">
          <div v-if="SelectedScannerObj.mapping_type == '1_to_1'">
            <!-- 1 to 1 display by dest code -->
            <div v-if="SelectedScannerObj.display_by_lane == false">
              <p>System Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerGoodData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="bydestcode"
                />
                  <template #bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="SorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="150"
                  cell-template="unassigntemplate"
                />
                  <template #unassigntemplate="{ data }">
                    <div>
                      <DxButton
                      text="Unassign"
                      type="danger"
                      styling-mode="contained"
                      @click="UnassignSorterButtonClick(data)"
                      :disabled="data.data.koz_dest_name == ''"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>

              <p>Standard Error Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerBadData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="1for1bydestcode"
                />
                  <template #1for1bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="nonSorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>
            </div>
            <!-- 1 to 1 display by destination -->
            <div v-else>
              <p>System Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerGoodData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  cell-template="bydestination"
                />
                  <template #bydestination="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestCodes"
                        v-model:value="data.data.dest_code"
                        value-expr="dest_code"
                        display-expr="dest_code"
                        height="30px"
                        @value-changed="SorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="150"
                  cell-template="unassigntemplate"
                />
                  <template #unassigntemplate="{ data }">
                    <div>
                      <DxButton
                      text="Unassign"
                      type="danger"
                      styling-mode="contained"
                      @click="UnassignSorterButtonClick(data)"
                      :disabled="data.data.dest_code == ''"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>

              <p>Standard Error Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerBadData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="1for1bydestcode"
                />
                  <template #1for1bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="nonSorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>
            </div>
          </div>
          <div v-else-if="SelectedScannerObj.mapping_type == '1_to_many'">
            <div v-if="SelectedScannerObj.display_by_lane == false">
              1 to many by destination is not recommended
            </div>
            <div v-else>
              <p>System Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerGoodData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  cell-template="bydestination"
                />
                  <template #bydestination="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestCodes"
                        v-model:value="data.data.dest_code"
                        value-expr="dest_code"
                        display-expr="dest_code"
                        height="30px"
                        @value-changed="SorterGoodDestUpdate(data, true)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="150"
                  cell-template="unassigntemplate"
                />
                  <template #unassigntemplate="{ data }">
                    <div>
                      <DxButton
                      text="Unassign"
                      type="danger"
                      styling-mode="contained"
                      @click="UnassignSorterButtonClick(data)"
                      :disabled="data.data.dest_code == ''"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>

              <p>Standard Error Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerBadData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="bydestcode"
                />
                  <template #bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="nonSorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>
            </div>
          </div>
          <div v-else-if="SelectedScannerObj.mapping_type == 'many_to_1'">
            <div v-if="SelectedScannerObj.display_by_lane == false">
              <p>System Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerGoodData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="bydestcode"
                />
                  <template #bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="SorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="150"
                  cell-template="unassigntemplate"
                />
                  <template #unassigntemplate="{ data }">
                    <div>
                      <DxButton
                      text="Unassign"
                      type="danger"
                      styling-mode="contained"
                      @click="UnassignSorterButtonClick(data)"
                      :disabled="data.data.koz_dest_name == ''"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>

              <p>Standard Error Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerBadData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="1for1bydestcode"
                />
                  <template #1for1bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="nonSorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>
            </div>
            <div v-else>
              many to 1 by lane is not recommended
            </div>
          </div>
          <div v-else-if="SelectedScannerObj.mapping_type == 'many_to_many'">
            <div v-if="SelectedScannerObj.display_by_lane == false">
              <p>System Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerManyToManyDisplay"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
                @row-updated="ManyToManyByDestCodeValChange"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxEditing     
                  :allow-updating="true"
                  mode="cell"
                  :allow-editing="false"
                />
                <DxColumn v-for="(val) in SelectedScannerDestinations" :key="val.koz_dest_name"
                  :data-field="val.koz_dest_name"
                  :caption="val.wms_dest_name"
                />
              </DxDataGrid>

              <p>Standard Error Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerBadData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="1for1bydestcode"
                />
                  <template #1for1bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="nonSorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>
            </div>
            <div v-else>
              <p>System Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerManyToManyDisplay"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
                @row-updated="ManyToManyByDestinationValChange"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxEditing     
                  :allow-updating="true"
                  mode="cell"
                  :allow-editing="false"
                />
                <DxColumn v-for="(val) in SelectedScannerDestCodes" :key="val.dest_code"
                  :data-field="val.dest_code"
                  :caption="val.dest_code"
                />
              </DxDataGrid>

              <p>Standard Error Map:</p>
              <DxDataGrid
                :key="datagridRefreshKey"
                height="80%"
                v-model:data-source="SelectedScannerBadData"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
              >
                <DxScrolling mode="virtual"/>
                <DxColumn
                  :width="250"
                  data-field="dest_code"
                  caption="Dest Code"
                  :fixed="true"
                  fixed-postions="left"
                />
                <DxColumn
                  :width="250"
                  data-field="koz_dest_name"
                  caption="Destination"
                  cell-template="1for1bydestcode"
                />
                  <template #1for1bydestcode="{ data }">
                    <div>
                      <DxLookup
                        v-model:data-source="SelectedScannerDestinations"
                        v-model:value="data.data.koz_dest_name"
                        value-expr="koz_dest_name"
                        display-expr="wms_dest_name"
                        height="30px"
                        @value-changed="nonSorterGoodDestUpdate(data)"
                      />
                    </div>
                  </template>
                <DxColumn
                  :width="180"
                  data-field="created_datetime"
                  caption="Last Altered"
                  data-type="datetime"
                />
                <DxColumn
                  data-field="user_id"
                  caption="Last Altered By"
                />
              </DxDataGrid>
            </div>
          </div>
          <!-- catch all this should never happen since we set the mapping type -->
          <div v-else>
            {{ SelectedScannerObj.mapping_type }} is not supported
          </div>
        </div>
        <!-- NON SORTER SCANNER -->
        <div v-else-if="SelectedScannerObj.scanner_is_sorter == false">
          <!-- non sorter scanners are always displayed by dest_code not by destination and is a many to 1 relationship -->
          <p>System Map:</p>
          <DxDataGrid
            :key="datagridRefreshKey"
            height="80%"
            v-model:data-source="SelectedScannerGoodData"
            :column-hiding-enabled="false"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
          >
            <DxScrolling mode="virtual"/>
            <DxColumn
              :width="250"
              data-field="dest_code"
              caption="Dest Code"
              :fixed="true"
              fixed-postions="left"
            />
            <DxColumn
              :width="250"
              data-field="koz_dest_name"
              caption="Destination"
              cell-template="1for1bydestcode"
            />
              <template #1for1bydestcode="{ data }">
                <div>
                  <DxLookup
                    v-model:data-source="SelectedScannerDestinations"
                    v-model:value="data.data.koz_dest_name"
                    value-expr="koz_dest_name"
                    display-expr="wms_dest_name"
                    height="30px"
                    @value-changed="nonSorterGoodDestUpdate(data)"
                  />
                </div>
              </template>
            <DxColumn
              :width="180"
              data-field="created_datetime"
              caption="Last Altered"
              data-type="datetime"
            />
            <DxColumn
              data-field="user_id"
              caption="Last Altered By"
            />
          </DxDataGrid>

          <p>Standard Error Map:</p>
          <DxDataGrid
            :key="datagridRefreshKey"
            height="80%"
            v-model:data-source="SelectedScannerBadData"
            :column-hiding-enabled="false"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
          >
            <DxScrolling mode="virtual"/>
            <DxColumn
              :width="250"
              data-field="dest_code"
              caption="Dest Code"
              :fixed="true"
              fixed-postions="left"
            />
            <DxColumn
              :width="250"
              data-field="koz_dest_name"
              caption="Destination"
              cell-template="1for1bydestcode"
            />
              <template #1for1bydestcode="{ data }">
                <div>
                  <DxLookup
                    v-model:data-source="SelectedScannerDestinations"
                    v-model:value="data.data.koz_dest_name"
                    value-expr="koz_dest_name"
                    display-expr="wms_dest_name"
                    height="30px"
                    @value-changed="nonSorterGoodDestUpdate(data)"
                  />
                </div>
              </template>
            <DxColumn
              :width="180"
              data-field="created_datetime"
              caption="Last Altered"
              data-type="datetime"
            />
            <DxColumn
              data-field="user_id"
              caption="Last Altered By"
            />
          </DxDataGrid>
        </div>
        <div v-else>
          <!-- yes im aware center is bad this is just a placeholder -->
          <center><h3>Select Scanner To View/Alter Mapping Table</h3></center>
        </div>
          
      </div>
    </div>
    <!-- SERVER CONNECTION STATUS DIV -->
    <h4 class="content-block">Server Connection Status</h4>
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div v-if="webSocketConnected" style="height: 60px;">
          <div style="font-size: 20px;">
            <div class="client-connected-state-rt-device">
              Status: {{webSocketStatusMsg}}
            </div>
          </div>
        </div>
        <div v-else style="height: 60px;">
          <div style="font-size: 20px;">
            <div class="client-not-connected-state-rt-device">
              Status: {{webSocketStatusMsg}}
            </div>
            <div style="float: left; padding-left: 5%;">
            Auto Re-Try Connection In: {{refreshWaitTimeCounter}}
            </div>
            <div style="float: left; padding-left: 2%;">
            <DxLoadIndicator
              style="margin-bottom: -10px;"
              v-model:visible="loadingVisible2"
              :height="30"
              :width="30"
            />
            </div>
          </div>
        </div>
        <DxButton
          @click="initWebSocket"
          :width="250"
          text="Bypass Auto Reconnect"
          type="success"
          styling-mode="contained"
          style="margin-top: 10px;"
          :disabled="webSocketConnected"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onUnmounted, onMounted } from 'vue';
import auth from '../auth';
import { useRouter, useRoute } from 'vue-router';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import { DxSelectBox } from 'devextreme-vue/select-box';
import notify from 'devextreme/ui/notify';
import { DxLoadIndicator } from 'devextreme-vue/load-indicator';
import DxButton from 'devextreme-vue/button';
import { DxLookup } from 'devextreme-vue/lookup';
import {
  DxDataGrid,
  DxColumn,
  DxScrolling,
  DxEditing
} from 'devextreme-vue/data-grid';


/* VUE Events */
onMounted(() => {
  LoadScanners();
});

//close the websocket connection when the view is unmounted
onUnmounted(() => {
  try {
    webSocket.value.close();
    webSocket.value = null;
  } catch (error) {
    console.log(error);
  }
  //make sure to clear interval on a page leave since this is a single page web app
  //if you do not it will run in the background
  leftPage.value = true;

  clearInterval(literalCountdownTimerInterval.value); 
});
/* END VUE Events */

/* Variables */

const datagridRefreshKey = ref(0);

const router = useRouter();
const route = useRoute();
const userToken = ref('');
const userName = ref('');
const userSecurity = ref('');

const ScannerList = ref([]);

const SelectedScanner = ref('');
const SelectedScannerObj = ref({});
const SelectedScannerDestCodes = ref([]);
const SelectedScannerDestinations = ref([]);
const SelectedMessageGroup = ref('');
const SelectedScannerManyToManyDisplay = ref([]);

const SelectedScannerGoodData = ref([]);
const SelectedScannerBadData = ref([]);

/* END Variables */

/* STD Functions */

const UpdateScanner = (e) =>{
  //set the selected scanner
  SelectedScanner.value = e.value;

  //store the selected scanners obj that contains all the info for how the data is going to be displayed
  let tmpIndex = ScannerList.value.findIndex(p => p.scanner_name == SelectedScanner.value);
  SelectedScannerObj.value = ScannerList.value[tmpIndex];

  //set the selected message group
  SelectedMessageGroup.value = `${SelectedScanner.value}_MAPPING`;

  writeEstablishMessage(SelectedMessageGroup.value);
  GetMappingData(SelectedScanner.value);
}

const nonSorterGoodDestUpdate = (data) => {
  //data.data.koz_dest_name = the new dest code
  //data.data.id_kvkdestmap = id value of the mapping in the table
  //if its ever changed by a user we will default to send with overwrite flag as FALSE
  console.log(data);
  UpdateMapping(SelectedScanner.value, data.data.id_kvkdestmap, data.data.dest_code, data.data.koz_dest_name, false);
}

const UnassignSorterButtonClick = (data) => {
  console.log(data);
  DeleteMapping(SelectedScanner.value, data.data.id_kvkdestmap);
}

const SorterGoodDestUpdate = (data, overwrite = false) => {
  console.log(data);
  //data.data.koz_dest_name = the new dest code
  //data.data.id_kvkdestmap = id value of the mapping in the table
  //data.value EMPTY STRING means its being created, data.value NON EMPTY STRING means its an update
  //if its ever changed by a user we will default to send with overwrite flag as FALSE, unless they are directly overwriting a clearly seen value in certain instances of sorting by lane mode then we automatically overwrite
  if(data.value == '')
  {
    CreateNewMapping(SelectedScanner.value, data.data.dest_code, data.data.koz_dest_name, overwrite);
  }
  else
  {
    UpdateMapping(SelectedScanner.value, data.data.id_kvkdestmap, data.data.dest_code, data.data.koz_dest_name, overwrite);
  }
  
}

const ManyToManyByDestCodeValChange = (e) => {
  //console.log(e);
  //console.log(SelectedScannerGoodData.value);
  let index = SelectedScannerGoodData.value.findIndex(p => p.dest_code == e.data.dest_code);

  for (let c = 0; c < SelectedScannerDestinations.value.length; c++)
  {
    //check to see if created
    if(typeof(SelectedScannerGoodData.value[index][SelectedScannerDestinations.value[c].koz_dest_name].id_kvkdestmap) === 'undefined' && e.data[SelectedScannerDestinations.value[c].koz_dest_name])
    {
      CreateNewMapping(SelectedScanner.value, e.data.dest_code, SelectedScannerDestinations.value[c].koz_dest_name, false);
      break;
    } //check to see if deleted
    else if(typeof(SelectedScannerGoodData.value[index][SelectedScannerDestinations.value[c].koz_dest_name].id_kvkdestmap) !== 'undefined' && !e.data[SelectedScannerDestinations.value[c].koz_dest_name])
    {
      DeleteMapping(SelectedScanner.value, SelectedScannerGoodData.value[index][SelectedScannerDestinations.value[c].koz_dest_name].id_kvkdestmap);
      break;
    }
  }
}

const ManyToManyByDestinationValChange = (e) => {
  let index = SelectedScannerGoodData.value.findIndex(p => p.koz_dest_name == e.data.koz_dest_name);

  for (let c = 0; c < SelectedScannerDestCodes.value.length; c++)
  {
    //check to see if created
    if(typeof(SelectedScannerGoodData.value[index][SelectedScannerDestCodes.value[c].dest_code].id_kvkdestmap) === 'undefined' && e.data[SelectedScannerDestCodes.value[c].dest_code])
    {
      CreateNewMapping(SelectedScanner.value, SelectedScannerDestCodes.value[c].dest_code, e.data.koz_dest_name, false);
      break;
    }
    else if(typeof(SelectedScannerGoodData.value[index][SelectedScannerDestCodes.value[c].dest_code].id_kvkdestmap) !== 'undefined' && !e.data[SelectedScannerDestCodes.value[c].dest_code])
    {
      DeleteMapping(SelectedScanner.value, SelectedScannerGoodData.value[index][SelectedScannerDestCodes.value[c].dest_code].id_kvkdestmap);
      break;
    }
  }
}

/* END STD Functions */


/* API Calls */
const LoadScanners = () =>{
  axios.get('api/Admin/MappableScanners').then((resp)=>{
    for(let c = 0; c < resp.data.data.length; c++)
    {
      // this can be changed but im just dynamically creating a display value that shows the scanner name and man readable text in one string field
      resp.data.data[c].displayValue = `${resp.data.data[c].scanner_name} - ${resp.data.data[c].heartbeat_value}`;
    }
    ScannerList.value = resp.data.data;
  }).catch(error=>{
    loadingVisible2.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      console.error(error);
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
/* END API Calls */

/* Functions For Altering Mapping Table / Adding / Removing Dest Codes */

//Requests mapping data from server for this specific websocket user
const GetMappingData = (scanner_name) => {
  console.log(`---------------------${scanner_name}----------------------------`);
  writeCommandMessage('MAPPING-GET', scanner_name, false);
}


//create a new mapping in the mapping table
//overwrite is a boolean flag that should only ever be flagged on if the SERVER responds saying a call failed but can be overwriten, or its an obvious action that the user is overwriting an old value in a select box
const CreateNewMapping = (scanner_name, dest_code, koz_dest_name, overwrite) => {

  const mappingOBJ = {
    scanner_name: scanner_name,
    dest_code: dest_code,
    koz_dest_name: koz_dest_name,
    overwrite: overwrite
  };

  writeCommandMessage('MAPPING-CREATE', mappingOBJ, true);
};


//update mapping in the mapping table
//overwrite is a boolean flag that should only ever be flagged on if the SERVER responds saying a call failed but can be overwriten, or its an obvious action that the user is overwriting an old value in a select box
const UpdateMapping = (scanner_name, id_kvkdestmap, dest_code, koz_dest_name, overwrite) => {

  const mappingOBJ = {
    scanner_name: scanner_name,
    id_kvkdestmap: id_kvkdestmap,
    dest_code: dest_code,
    koz_dest_name: koz_dest_name,
    overwrite: overwrite
  };

  writeCommandMessage('MAPPING-UPDATE', mappingOBJ, true);
};


//delete a mapping from the mapping table
const DeleteMapping = (scanner_name, id_kvkdestmap) => {

  const mappingOBJ = {
    scanner_name: scanner_name,
    id_kvkdestmap: id_kvkdestmap
  };

  writeCommandMessage('MAPPING-DELETE', mappingOBJ, true);
};

/*
//create a new destination code for a scanner, ONLY VALID FOR SORTERS
const CreateNewDestCode = (scanner_name, dest_code) => {

  const mappingOBJ = {
    scanner_name: scanner_name,
    dest_code: dest_code
  }

  writeCommandMessage('DESTCODE-CREATE', mappingOBJ, true);
};
*/
/*
//delete a destination code for a scanner, ONLY VALID FOR SORTERS
const DeleteDestCode = (scanner_name, dest_code) => {

  const mappingOBJ = {
    scanner_name: scanner_name,
    dest_code: dest_code
  }

  writeCommandMessage('DESTCODE-DELETE', mappingOBJ, true);
};
*/

//There is no update dest code function we will want the users to only every create or delete

/* END Functions */


/* WebSocket */

//holds the JSON for the establish websocket message
const initWsJsonMsg = databaseName.getWebsocketMessageBase();
//holds the actuall endpoint the websocket is trying to reach
const webSocketEndpoint = databaseName.getMonitorWebsocketEndpoint();

//Actual WebSocket Object
const webSocket = ref(null);
//Connection Status Flag
const webSocketConnected = ref(false);
//Message Displayed on the Server Connection Status DIV
const webSocketStatusMsg = ref("Not Connected");

//Connection Variables
const reconnectCounter = ref(0);       //attempts to reconnect
const reconnectMinWaitTime = ref(0.1); //in minutes
const reconnectMaxWaitTime = ref(2);   //in minutes
const reconnectConnectedWaitTime = ref(60);  //in seconds, dont want to have this go into loop to ofter even though it wont hurt anything
const reconnectIncreaseTimeAtCount = ref(10);  //change check time to reconnectMaxWaitTime
const reconnectRealWaitTime = ref(0.1);        //this will be the actuall wait time 
const refreshWaitTimeCounter = ref("");

const literalCountdownTimerInterval = ref(Number);
const reconnectTimeLeftMS = ref(5000);
const cancelReconnect = ref(true);

const loadingVisible2 = ref(false);
const leftPage = ref(false);

// Timer that handles reconnecting to the websocket
const countdownTimer = () => {
  cancelReconnect.value = false;
  clearInterval(literalCountdownTimerInterval.value);
  literalCountdownTimerInterval.value = setInterval(()=>{
    if(reconnectTimeLeftMS.value >= 0 && !cancelReconnect.value){
      countdownTimerInterval();
    }
    else{
      clearInterval(literalCountdownTimerInterval.value);
      console.log("Stopped Interval");
    }
  }, 1000)
};

// function that handles updating the countdown and actaully calls the INIT function
const countdownTimerInterval = () => {
  reconnectTimeLeftMS.value = reconnectTimeLeftMS.value - 1000;

  let minutes = Math.floor((reconnectTimeLeftMS.value) / (1000*60)%60);
  let seconds = (reconnectTimeLeftMS.value / 1000) % 60;

  let strMinutes = minutes > 9 ? minutes : '0' + minutes;
  let strSeconds = seconds > 9 ? seconds : '0' + seconds;

  refreshWaitTimeCounter.value = strMinutes + ':' + strSeconds;

  if(reconnectTimeLeftMS.value <= 0){
    clearInterval(literalCountdownTimerInterval.value);
    initWebSocket();
  }
};

//handles creating the connection and sets up event listeners to handle events
const initWebSocket = () => {
  reconnectCounter.value++;

  webSocket.value = new WebSocket(webSocketEndpoint.value);
  
  loadingVisible2.value = true;
  
  webSocket.value.onopen = function() {
    cancelReconnect.value = true;
    webSocketConnected.value = true;
    reconnectCounter.value = 0;
    reconnectRealWaitTime.value = reconnectConnectedWaitTime.value;
    webSocketStatusMsg.value = "Connected To Server";
    loadingVisible2.value = false;

    clearInterval(literalCountdownTimerInterval.value);

    notify('Connection Attempt Success.', 'success', 2500);

    let tmpRealJson = initWsJsonMsg.value;
      tmpRealJson.msgType = "ESTABLISH";
        tmpRealJson.msgFromService = "";
        tmpRealJson.msgToService = "HOST-SERVER";
        tmpRealJson.EstablishConnObj.userIdName = userName;
        tmpRealJson.EstablishConnObj.userSecurity = userSecurity;
        tmpRealJson.EstablishConnObj.message = "connecting for the first time";
        tmpRealJson.EstablishConnObj.messageGroup = "GENERAL";
    webSocket.value.send(JSON.stringify(tmpRealJson));
  };
    
  webSocket.value.onmessage = function (evt) { 
    //console.log('evt', evt);
    try{
      processCommand(evt.data);
    }
    catch{
      console.error("Caught message exception");
    }
    
  };
    
  webSocket.value.onclose = function() { 
    console.log("onclose recon timer=>",reconnectCounter.value)
    webSocketStatusMsg.value = "Connection Closed";
    webSocketConnected.value = false;
    webSocket.value = null; 
    
    if(!leftPage.value){
      notify('Connection Attempt Failed.', 'error', 2500);
      if(reconnectCounter.value >= 0 && reconnectCounter.value < reconnectIncreaseTimeAtCount.value){          
        reconnectTimeLeftMS.value = reconnectMinWaitTime.value * 60000; 
        countdownTimer();
      }
      else{
        reconnectTimeLeftMS.value = reconnectMaxWaitTime.value * 60000;
        countdownTimer();
      }
    }
    loadingVisible2.value = false;
  };
};

//Process the websocket command message
/*
Return object from the mapping altering messages is below
{
  msgNameCalled: 'the MSG that was called that generated the reply',
  success: boolean flag for if the operation was successful,
  message: 'string message that is the reason for what happend OR for success TRUE will be generic accept message',
  canOverwrite: boolean flag if set to true it means the FAILED message can be resent with the FLAG ON to overwrite the value that was preventing the action,
  originalMessage: obj that is the original message the client sent with the request
}
*/
const processCommand = async(msgJson) =>{
  //parse the command message as JSON
  msgJson = JSON.parse(msgJson);
  let tmp, manyToManyTmp, manyToManyIndex;
  switch(msgJson.CommandObj.cmdName){
    case 'ALERT':
      //This is an alert message that should display a message as an error with what the error is
      //The error is msgJson.CommandObj.cmdMessage
      notify(msgJson.CommandObj.cmdMessage, 'error', 7500)

      break;
    case 'MAPPING-DATA':
      //This is a message signifying that the mapping data has been updated
      //console.log(JSON.parse(msgJson.CommandObj.cmdMessage));

      //parse the message
      tmp = JSON.parse(msgJson.CommandObj.cmdMessage);
      console.log(tmp);

      //SelectedScannerManyToManyDisplay
      
      if(SelectedScannerObj.value.mapping_type == 'many_to_many')
      {
        manyToManyTmp = [];
        if(SelectedScannerObj.value.display_by_lane)
        {
          for(let c = 0; c < tmp.destinations.length; c++)
          {
            manyToManyIndex = tmp.current_bindings.findIndex(p => p.koz_dest_name == tmp.destinations[c].koz_dest_name);

            manyToManyTmp.push({ koz_dest_name: tmp.destinations[c].koz_dest_name });
            for(let i = 0; i < tmp.dest_codes.length; i++)
            {
              //if we cannot find that its bound we set it to false in the display object
              if(typeof(tmp.current_bindings[manyToManyIndex][tmp.dest_codes[i].dest_code].id_kvkdestmap) === 'undefined')
              {
                manyToManyTmp[c][tmp.dest_codes[i].dest_code] = false;
              }
              else
              {
                manyToManyTmp[c][tmp.dest_codes[i].dest_code] = true;
              }
            }
          }
        }
        else
        {
          for(let c = 0; c < tmp.dest_codes.length; c++)
          {
            manyToManyIndex = tmp.current_bindings.findIndex(p => p.dest_code == tmp.dest_codes[c].dest_code);

            manyToManyTmp.push({ dest_code: tmp.dest_codes[c].dest_code });
            for(let i = 0; i < tmp.destinations.length; i++)
            {
              //if we cannot find that its bound we set it to false in the display object
              if(typeof(tmp.current_bindings[manyToManyIndex][tmp.destinations[i].koz_dest_name].id_kvkdestmap) === 'undefined')
              {
                manyToManyTmp[c][tmp.destinations[i].koz_dest_name] = false;
              }
              else
              {
                manyToManyTmp[c][tmp.destinations[i].koz_dest_name] = true;
              }
            }
          }
        }
        SelectedScannerManyToManyDisplay.value = manyToManyTmp;
        //console.log(manyToManyTmp);
      }

      //set arrays that are used for re-mapping
      SelectedScannerDestCodes.value = tmp.dest_codes;
      SelectedScannerDestinations.value = tmp.destinations;
      console.log(SelectedScannerDestinations.value);
      //set the display GOOD array
      SelectedScannerGoodData.value = tmp.current_bindings;
      SelectedScannerBadData.value = tmp.std_error_bindings;

      datagridRefreshKey.value++;
      break;
    case 'MAPPING-CREATE_REPLY':
      //This is a message in reply to the MAPPING-CREATE message sent by this client
      tmp = JSON.parse(msgJson.CommandObj.cmdMessage);
      if(tmp.success)
      {
        notify(tmp.message, 'success', 5000);
      }
      else
      {
        //add logic with like a popup or something that if success = false AND canOverwrite = true will prompt user to overwrite old value blocking the update
        notify(tmp.message, 'error', 7500);
        //im not sure how you guys will want to handle loading back the correct map from altering not working or if you want to do it a different way so for now im just
        //reloading the map if there is an error to get the valid values back ing
        GetMappingData(SelectedScanner.value);
      }
      break;
    case 'MAPPING-UPDATE_REPLY':
      //This is a message in reply to the MAPPING-UPDATE message sent by this client
      //console.log(JSON.parse(msgJson.CommandObj.cmdMessage));
      tmp = JSON.parse(msgJson.CommandObj.cmdMessage);
      if(tmp.success)
      {
        notify(tmp.message, 'success', 5000);
      }
      else
      {
        //add logic with like a popup or something that if success = false AND canOverwrite = true will prompt user to overwrite old value blocking the update
        notify(tmp.message, 'error', 7500);
        //im not sure how you guys will want to handle loading back the correct map from altering not working or if you want to do it a different way so for now im just
        //reloading the map if there is an error to get the valid values back ing
        GetMappingData(SelectedScanner.value);
      }
      
      break;
    case 'MAPPING-DELETE_REPLY':
      //This is a message in reply to the MAPPING-DELETE message sent by this client
      tmp = JSON.parse(msgJson.CommandObj.cmdMessage);
      if(tmp.success)
      {
        notify(tmp.message, 'success', 5000);
      }
      else
      {
        //add logic with like a popup or something that if success = false AND canOverwrite = true will prompt user to overwrite old value blocking the update
        notify(tmp.message, 'error', 7500);
        //im not sure how you guys will want to handle loading back the correct map from altering not working or if you want to do it a different way so for now im just
        //reloading the map if there is an error to get the valid values back ing
        GetMappingData(SelectedScanner.value);
      }
      break;
    case 'DESTCODE-CREATE_REPLY':
      //This is a message in reply to the DESCCODE-CREATE message sent by this client
      break;
    case 'DESTCODE-DELETE_REPLY':
      //This is a message in reply to the DESTCODE-DELETE message sent by this client
      break;
    default:
      console.error(`Unhandled cmdName: ${msgJson.CommandObj.cmdName}, Seen At processCommand Function`, msgJson);
      break;
  }
};

// function for writing a command message
const writeCommandMessage = (cmdName, cmdMessage, isObject) =>{
  let tmpRealJson = initWsJsonMsg.value;
  tmpRealJson.msgType = 'COMMAND';
  tmpRealJson.msgToService = 'KOZ-CONVEYOR';
  tmpRealJson.CommandObj.cmdName = cmdName;
  tmpRealJson.CommandObj.cmdMessage = isObject == true ? JSON.stringify(cmdMessage) : cmdMessage;

  webSocket.value.send(JSON.stringify(tmpRealJson));
}

const writeEstablishMessage = (msgGroup) =>{
  let tmpRealJson = initWsJsonMsg.value;
      tmpRealJson.msgType = "ESTABLISH";
        tmpRealJson.msgToService = "HOST-SERVER";
        tmpRealJson.EstablishConnObj.userIdName = userName;
        tmpRealJson.EstablishConnObj.userSecurity = userSecurity;
        tmpRealJson.EstablishConnObj.message = "im switching";
        tmpRealJson.EstablishConnObj.messageGroup = msgGroup;
    webSocket.value.send(JSON.stringify(tmpRealJson));
}

/* END WebSocket */

/* INIT */
//make sure that we are NOT looking at an old cached browser version
databaseName.checkWebsiteVersion();

//make sure the user is authorized for this page and if not kick them off the page
auth.getUser().then(user=>{
  //console.log(user.data);
  if(user.data.userSecurity === 'administrator' || user.data.userSecurity === 'programmer' || user.data.userSecurity === 'editor'){
    userToken.value = user.data.userJwtToken;
    userSecurity.value = user.data.userSecurity;
    userName.value = user.data.userIdName;
    //init the websocket after we have gotten the creds
    initWebSocket();
  } else {
    alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");

    router.push({  path: "/login-form",  query: { returnUrl: route.fullPath }})
  }
}).catch(error=>{
  //console.log(error.response);
  if(error.response){
    alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else if(error.request){
    alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else{
    alert('Unknown error\nMake sure that you and the server have not lost connection');
    return;
  }
});

/* END INIT */

</script>

<style lang="scss">
.client-connected-state-rt-device {
  background-color: #90EE90;
  width: 35%;
  text-align: center;
  float: left;
}
.client-not-connected-state-rt-device {
  background-color: #FF0000;
  color: white;
  width: 35%;
  text-align: center;
  float: left;
}
</style>

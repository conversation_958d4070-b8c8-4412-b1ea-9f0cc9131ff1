<template>
  <div class="content-block container" style="flex: auto;">
    <DxLoadPanel
      :height="500"
      :width="700"
      :visible="loadingVisible"
      :show-indicator="true"
      :show-pane="true"
      :shading="true"
      shading-color="rgba(0,0,0,0.4)"
    />
    <!-- <h4 class="">{{userInfo.userFirst}}'s Profile</h4> -->
    <div class="dx-card responsive-paddings">
      <div>
        If any of the below items need to be changed contact your system administrator.<br>
        <b class="accent">Name:</b> {{userInfo.userFirst}} {{userInfo.userLast}}<br>
        <b class="accent">Username:</b> {{userInfo.userIdName}}<br>
        <b class="accent">Email:</b> {{userInfo.userEmail}}<br>
        <b class="accent">Security Level:</b> {{userInfo.userSecurity}}
      </div>
      <!-- <div v-if="userInfo.userSecurity == 'programmer'">
        Nerd Info:<br>
        <b class="accent">window.devicePixelRatio:</b> {{_window.devicePixelRatio}}<br>
        <b class="accent">window.screen.width:</b> {{_window.screen.width}}<br>
        <b class="accent">window.screen.height:</b> {{_window.screen.height}}<br>
        <b class="accent">window.screen.availWidth:</b> {{_window.screen.availWidth}}<br>
        <b class="accent">window.screen.availHeight:</b> {{_window.screen.availHeight}}<br>
        <b class="accent">window.screen.pixelDepth:</b> {{_window.screen.pixelDepth}}

      </div> -->
    </div>

    <nerdStuff v-if="userInfo.userSecurity == 'programmer'"></nerdStuff>

    <!-- <h4 class="">User Options</h4> -->
    <div class="dx-card responsive-paddings">
      <div style="display: flex; justify-content: flex-end;">
        <!-- <DxButton 
          text="Reset LPNs"
          type="default"
          styling-mode="contained"
          @click="changeMainView"
        /> -->
      </div>
      <div v-if="showResetLPNsView">
        <div>
          Enter LPN(s) You Would Like To Reset (Can Copy From Excel or Type in Manually):
          <DxTextArea 
            placeholder="LPN(s)..."
            styling-mode="filled"
            width="35%"
            :auto-resize-enabled="true"
            :min-height="100"
            :max-height="200"
            style="margin-bottom: 10px;"
            v-model:value="enteredLPN"
          />
          <div>
            <DxButton 
              text="Add LPN(s)"
              type="default"
              styling-mode="contained"
              style="margin-bottom: 15px;"
              @click="addLPNToList"
            />
          </div>
        </div>
        <div v-if="showArrayOfLPNs" style="margin: 25px;">
          <p style="font-size: medium;">LPNs That Will Be Reset. Click The Red 'X' If You Do Not Want To Reset An LPN:</p>
          <div v-for="(lpn) in arrayOfLPNs" :key="lpn">
            <div style="display: flex; align-items: center;">{{ lpn.lpn }}
              <span @click="clickToRemoveLPNFromList(lpn.lpn)" id="deleteLPN" class="dx-icon-close"></span>
            </div>
          </div>
          <DxButton 
            text="Reset the Above LPN(s)"
            type="default"
            styling-mode="contained"
            @click="confirmAndResetLPNS"
            style="margin-top: 10px;"
            :disabled="arrayOfLPNs.length < 1"
          />
        </div>
        <DxPopup
          v-model:visible="confirmPopup"
          :drag-enabled="false"
          :hide-on-outside-click="true"
          :show-close-button="true"
          :show-title="true"
          height="30%"
          width="30%"
          title="Confirm Reset LPNs"
        >
          <div v-show="confirmPopup">
            <p style="font-size: medium;">Are You Sure You Want To Reset These LPN(s)?</p>
            <div v-for="(lpn) in arrayOfLPNs" :key="lpn">
              <div style="display: flex; align-items: center;">{{ lpn.lpn }}</div>
            </div>
          </div>
          <div style="margin-top: 25px; display: flex; justify-content: space-between;">
            <DxButton 
              text="YES"
              type="default"
              styling-mode="contained"
              @click="sendReset('yes')"
            />
            <DxButton 
              text="NO"
              type="default"
              styling-mode="contained"
              @click="sendReset('no')"
            />
          </div>
        </DxPopup>
      </div>
      <div v-else>
        <DxTabPanel
          :height="400"
          :data-source="userOptions"
          :swipe-enabled="false"
          :animation-enabled="false"
          :show-nav-buttons="true"
          :item-template="itemSelected"
          @title-click="handleTitleClick"
        >
          <template #title="{data: userOptions}">
            {{userOptions.title}}
          </template>
          <template #pass>
            <DxForm
              id="changePassForm"
              :form-data="pass"
            >
              <DxGroupItem :col-span="1">
                <DxSimpleItem
                  data-field="OldPass"
                  :editor-options="{ mode: 'password' }"
                  text="Old Password"
                />
                <DxSimpleItem
                  data-field="NewPass"
                  :editor-options="{ mode: 'password' }"
                  text="New Password"
                />
                <DxSimpleItem
                  data-field="ReNewPass"
                  :editor-options="{ mode: 'password' }"
                  text="Re Enter New Password"
                />
                <DxButtonItem
                  horizontal-alignment="left"
                  :button-options="changeButton"
                  
                />
              </DxGroupItem>
            </DxForm>
          </template>
        </DxTabPanel>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import auth from '../auth';
import axios from 'axios';
import DxTabPanel from 'devextreme-vue/tab-panel';
import { DxSimpleItem, DxGroupItem, DxButtonItem, DxForm } from 'devextreme-vue/form';
import notify from 'devextreme/ui/notify';
import databaseName from '../myFunctions/databaseName';
import DxButton from 'devextreme-vue/button';
// import DxTextBox from 'devextreme-vue/text-box';
import DxTextArea from 'devextreme-vue/text-area';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
//import { confirm } from 'devextreme/ui/dialog';
import DxPopup from 'devextreme-vue/popup';
import nerdStuff from '../components/nerdStuff.vue';

// export default {
//   components: {
//     DxTabPanel,
//     DxSimpleItem,
//     DxGroupItem,
//     DxButtonItem,
//     DxForm,
//   },

// databaseInfo: Object,
const _window = ref(window);

const router = useRouter();
const userInfo = ref(Object);
const loadingVisible = ref(false);
const userOptions = ref([{title: "Change Password", option: "pass", id: 0}]);
// const index = ref(0);
// const programmerDS = ref([]);
const pass = ref({});
const itemSelected = ref('pass');
const changeButton = ref({
  text: 'Change',
  stylingMode: "contained",
  type: "default",
  onClick: ()=>{
    changePass();
  }
});
    
// this.databaseInfo = databaseName.getPaths();
databaseName.checkWebsiteVersion();
//this function gets the user's info to display on the page
const getUser = async() => {
  let temp = await auth.getUser();
  userInfo.value = temp.data;

  if(userInfo.value.userIdName === 'view only'){
    alert("You are required to login to view your profile.");
    router.push('/login-form');
  }
};
getUser();

//used to change the toolbar to toggle between its options
const handleTitleClick = (data) => {
  itemSelected.value = data.itemData.option;
};
/*
this takes the data the user put in the form for change password
and then it post's it to the update api to change the users password
also handles error checking to make sure that the passwords are valid
*/
const changePass = () => {
  
  if(pass.value.OldPass === userInfo.value.userPass && typeof pass.value.OldPass !== 'undefined'){
    if(pass.value.NewPass === pass.value.ReNewPass && typeof pass.value.NewPass !== 'undefined'){

      const bodyObj = {
        // "newPass": pass.value.NewPass.toString(),
        "newPass": databaseName.GeneratePasswordHash(pass.value.NewPass),
        "newUserIdNum": userInfo.value.userIdNum.toString()
      }
      //const token = Buffer.from(`${this.userInfo.userIdNum}:${this.userInfo.userPass}`, 'utf8').toString('base64');
      axios({
        method: 'PATCH',
        url: 'api/Auth/UpdatePassword',
        data: bodyObj,
        headers:{
          'Authorization': `Bearer ${userInfo.value.userJwtToken}`,
          'Content-Type': 'application/json'
        }
      })
      .then(resp=>{
        // console.log(resp);
        if(resp.status == 200){
          loadingVisible.value = false;
          notify(resp.data.message, 'success', 5000);
          router.push('/login-form');
          //this.getData();
        }
        else{
          loadingVisible.value = false;
          notify(resp.data.message, 'error', 10000);
          //this.getData();
          return;
        }
      }).catch(error=>{
        loadingVisible.value = false;
        console.log(error);
        if(error.response.status === 401) {
          alert('Unauthorized \nPlease See Manager to Change Password for You');
          return;
        }
        if(error.response){
          alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
          return;
        }
        else if(error.request){
          alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
          return;
        }
        else{
          alert('Unknown error\nMake sure that you and the server have not lost connection');
          return;
        }
      });
    }
    else{
      notify("New Passwords do not match", 'warning', 10000);
    }
  }
  else{
    notify("Incorrect Old Password", 'warning', 10000);
  }
};

const showResetLPNsView = ref(false);
const enteredLPN = ref('');
const objToSendLPNArray = ref({});
const arrayOfLPNs = ref([]);
/*
const changeMainView = () => {
  showResetLPNsView.value = !showResetLPNsView.value;
  enteredLPN.value = '';
  arrayOfLPNs.value = [];
};
*/

const showArrayOfLPNs = ref(false);
const addLPNToList = () => {
  arrayOfLPNs.value = [];
  let singleLPN = enteredLPN.value.split(/\s+|[ ,]+|[ /]+|[ .]+/g);
  singleLPN.forEach((s) => {
    if (s) {
      arrayOfLPNs.value.push({ lpn: s });
    }
  })
  objToSendLPNArray.value = {
    LPNList: arrayOfLPNs.value
  };
  showArrayOfLPNs.value = true;
};
const clickToRemoveLPNFromList = (lpn) => {
  let lpnIndex = arrayOfLPNs.value.findIndex(l => l.lpn == lpn);
  if (lpnIndex != -1) {
    arrayOfLPNs.value.splice(lpnIndex, 1);
  }
};
const confirmPopup = ref(false);
const confirmAndResetLPNS = () => {
 confirmPopup.value = !confirmPopup.value;
};
const sendReset = (yesOrNo) => {
  if (yesOrNo == 'no') {
    confirmPopup.value = false;
    notify('LPNs NOT Reset', 'info', 5000);
  } else {
    loadingVisible.value = true;
      axios({
        method: 'PATCH',
        url: 'api/Admin/ResetLPNS',
        data: objToSendLPNArray.value,
        headers:{
          'Authorization': `Bearer ${userInfo.value.userJwtToken}`,
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        if (resp.status === 200) {
          notify(resp.data.message, 'success', 4000);
          loadingVisible.value = false;
        } else {
          notify(resp.data.message, 'error', 10000);
          loadingVisible.value = false;
        }
      }).catch(error=>{
        loadingVisible.value = false;
        console.log(error.response);
        if (error.response.status === 500) {
          notify(error.response.data.message, 'error', 10000);
          return;
        } else {
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the server have not lost connection');
            return;
          }
        }
      })
      // .finally(() => {
      //   // getAllCarrierLaneData();
      // })
    confirmPopup.value = false;
    enteredLPN.value = '';
    arrayOfLPNs.value = [];
    objToSendLPNArray.value = {};
    //notify('LPNs Reset', 'success', 5000);
  }
};
</script>

<style scoped lang="scss">
  @import "../themes/generated/variables.additional.scss";
.container {
    display: flex;
    flex-direction: column;
    flex-grow:1;
    gap: 20px;
    flex: 1 1 auto;
    overflow-y: hidden;
    padding-right: 5px; 
    color: rgba($base-text-color, alpha($base-text-color)  * 0.65);
    letter-spacing: 0.3px;
    width: 100%; 
  }

  .accent {
		color: $base-accent;
	}

	.bg-accent {
		background-color: $base-accent;
	}

  #changePassForm{
    width: 50%;
    position: relative;
    margin: 20px auto;
    padding: 20px 10px 10px 10px;
    border: 2px solid #3aafa9; 
    border-radius: 25px;
    height: auto;
  }
  #deleteLPN {
    margin-left: 5px;
    color: red;
    cursor: pointer;
  }
</style>
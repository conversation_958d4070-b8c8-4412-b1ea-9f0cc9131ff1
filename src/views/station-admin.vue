<template>
  <div class="content-block dx-card responsive-paddings">
    <OrderStartStations 
      :order-start-stations="orderStartStations.data" 
      :filter-groups="filterGroups"
      @toggle-station="handleToggleStation"
      @update-filter-group="handleUpdateStationFilterGroup">
    </OrderStartStations>
    
    <div style="height: 1px;" class="bg-border"></div>
    
    <div class="row">
      <FilterGroupsList 
        :filter-groups="filterGroups" 
        :active-group-id="activeGroupId"
        @select-group="selectGroup"
        @create-group="handleCreateGroup">
      </FilterGroupsList>

      <!-- separator -->
      <div style="width: 1px;" class="bg-border"></div>

      <div class="container">
        <div v-if="isOrderStartActive" class="notice">
          <span>🚨 Editing is disabled for this filter group because it is linked to an active Order Start Station.</span>
        </div>
        
        <!-- Group Details -->
        <FilterGroupDetails 
          :group="activeGroup"
          :available-conditions="availableConditions"
          :available-zones="availableZones"
          :condition-types="uniqueConditionTypes"
          :conditions-by-type="conditionsByType"
          :active-conditions="activeConditions"
          :group-counts="filterGroupCounts"
					@update-name="handleUpdateGroupName"
          @update-desc="handleUpdateGroupDesc"
          @add-condition="handleAddCondition"
          @remove-condition="handleRemoveCondition"
          @toggle-zone="handleToggleZone"
          @delete-group="handleDeleteGroup"
          :class="isOrderStartActive ? 'disabled' : 'enable'"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
  // Vue core
  import { ref, computed, onMounted, nextTick } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
  import { confirm } from 'devextreme/ui/dialog';

	// Composables
	import { useStationAdmin } from '@/composables/useStationAdmin';
  import { useOrderStart } from '@/composables/useOrderStart';

	// Child components
	import FilterGroupsList from '@/components/stationAdmin/FilterGroupsList.vue';
	import FilterGroupDetails from '@/components/stationAdmin/FilterGroupDetails.vue';
	import OrderStartStations from '@/components/stationAdmin/OrderStartStations.vue';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { 
		filterGroups,
		activeGroupId,
		activeGroup,
		availableConditions,
		activeConditions,
		availableZones,
		uniqueConditionTypes,
		conditionsByType,
		filterGroupCounts,
		
		// Data fetching methods
		fetchFilterGroups,
		fetchGroupBindings,
		
		// Condition management
		addCondition,
		removeCondition,
		
		// Zone management
		toggleZone,
		
		// Station management
		toggleStation,
		updateStationFilterGroup,
		
		// Group management
		createGroup,
		updateGroupName,
		updateGroupDesc,
		deleteGroup
	} = useStationAdmin();

	const { orderStartStations, fetchOrderStartStations } = useOrderStart();

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	onMounted(async () => {
		await fetchFilterGroups();
		await fetchGroupBindings();
		await fetchOrderStartStations();
	});

	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/
  // No element refs 

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	// Notification configuration object
	const notifyOptions = ref({
		position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }},
		width: 'auto',
		animation: {
			show: { type: "fade", duration: 800, from: 0, to: 1 },
			hide: { type: "fade", duration: 800, from: 1, to: 0 }
		}
	});

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
  // No watchers

	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	const isOrderStartActive = computed(() => {
		// Ensure data is available
		if (!orderStartStations.value?.data || !activeGroup.value) {
			return false;
		}

		// Get the filter group name to match against
		const filterGroupName = activeGroup.value.filter_group_name;

		// Find all matching stations
		const matchedStations = orderStartStations.value.data.filter(station => 
			station.filter_group_name === filterGroupName
		);

		// If no stations match, return false
		if (matchedStations.length === 0) {
			return false;
		}

		// Return true if at least one station is active
		return matchedStations.some(station => station.active);
	});

	/*=====================================================================
			FUNCTIONS
	=====================================================================*/
	const showNotification = (result, successMessage) => {
		if (result.success) {
			notify({ ...notifyOptions.value, message: successMessage, type: 'success', displayTime: 2000 });
		} else {
			notify({ ...notifyOptions.value, message: result.error, type: 'error', displayTime: 3000 });
		}
	};

	const selectGroup = (groupId) => {
		activeGroupId.value = groupId;
	};

	const handleToggleZone = async (zone) => {
		const result = await toggleZone(zone);
		showNotification(
			result, 
			`Zone ${zone.order_start_pick_zone} ${zone.id_kvkorderstartactivepickzones ? 'removed from' : 'added to'} the filter group`
		);
	};

	const handleRemoveCondition = async (conditionId) => {
		const result = await removeCondition(conditionId);
		showNotification(result, 'Condition successfully removed');
	};

	const handleAddCondition = async (conditionData) => {
		//had to remove toUpperCase for condition values, carl is not sending the data in all upper case
		//may add this back, main place of impact will be in the SKU serach because his SKU's are not numbers
		//in a perfect world the filter values will all be constrained in a table but everything here is to adhock to do rules like that
		const result = await addCondition(conditionData.type, conditionData.value);
		showNotification(result, 'Condition successfully added');
	};

	const handleToggleStation = async (data) => {
		const result = await toggleStation(
			data.stationName, 
			data.active, 
			async () => {
				await fetchOrderStartStations();
				// Force re-evaluation of computed properties
				await nextTick();
			}
		);
		
		showNotification(
			result, 
			`Station ${data.stationName} ${data.active ? 'activated' : 'deactivated'}`
		);
	};

	const handleUpdateStationFilterGroup = async (data) => {
		const result = await updateStationFilterGroup(
			data.stationName, 
			data.filterGroupName, 
			async () => {
				await fetchOrderStartStations();
				// Force re-evaluation of computed properties
				await nextTick();
			}
		);
		
		showNotification(
			result, 
			`${data.stationName} filter group changed to ${data.filterGroupName}`
		);
	};

	const handleUpdateGroupName = async (newDesc) => {
		const result = await updateGroupName(newDesc);
		showNotification(result, 'Group name updated');
	};

	const handleUpdateGroupDesc = async (newDesc) => {
		const result = await updateGroupDesc(newDesc);
		showNotification(result, 'Group description updated');
	};

	const handleDeleteGroup = async (groupName) => {
		const confirmResult = await confirm(`Are you sure you want to delete ${groupName}?`, "Confirm Delete");
		if (confirmResult) {
			const result = await deleteGroup(groupName);
			showNotification(result, `${groupName} deleted`);
		}
	};

	const handleCreateGroup = async (data) => {
		const result = await createGroup(data.groupName, data.groupDesc);
		showNotification(result, `Group ${data.groupName} created`);
	};
</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 40px;
	}

	.container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		flex: 1 1 auto;
		overflow-y: auto;
		letter-spacing: 0.3px;
	}

	.row {
		display: flex;
		gap: 40px;
		flex-shrink: 0; 
		flex: auto;
	}

	/* color classes */
	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}
	.bg-border {
		background-color: $base-border-color;
	}

	.disabled {
		pointer-events: none;
		opacity: 0.55; /* Optional: makes it look disabled */
	}

	.notice {
		padding: 5px 15px;
		background: #704040;
		border-radius: 50px
	}

</style>
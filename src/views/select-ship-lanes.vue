<template>
  <div>
    <!-- <h2 class="content-block">Select Ship Lanes</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
          <div>
            <DxButton 
              text="Refresh"
              type="success"
              styling-mode="contained"
              icon="refresh"
              @click="getLaneMappingData"
            />
          </div>
          <div>
            <DxButton 
              text="Add New Shipping Code"
              type="default"
              styling-mode="contained"
              @click="addNewShippingCode"
            />
          </div>
        </div>
        <div>
          <DxDataGrid
            :data-source="mainGridData"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            :column-auto-width="true"
            style="margin-top: 15px;"
            @row-prepared="hospitalLaneBGColor"
            @row-removed="DeleteShipCodeRow"
          >
            <!-- :column-hiding-enabled="hideColumnsOrNot" -->
            <DxPaging :enabled="false"/>
            <DxScrolling mode="standard"/>
            <DxColumnFixing :enabled="true" />
            <DxEditing     
              :allow-deleting="true"
              mode="row"
            />
              <DxColumn 
                data-field="divert_code"
                caption="Ship Code"
                width="100"
                :fixed="true" 
                alignment="center"
                fixed-position="left"
                name="showDivert"
              />
              <!-- <DxColumn 
                data-field="kvk_lane_name"
                caption="Conveyor Lane"
                width="100"
                :fixed="true" 
                fixed-position="left"
              /> -->
              <DxColumn 
                data-field="kvk_lane_name"
                caption="Destination"
                cell-template="destCodeTemplate"
                name="selectDivert"
              />
                <template #destCodeTemplate="{ data }">
                  <div>
                    <DxLookup 
                      :data-source="mainGridLookupdata"
                      v-model:value="data.data.kvk_lane_name"
                      value-expr="kvk_lane_name"
                      display-expr="kvk_lane_name"
                      height="30px"
                      :disabled="data.data.hospital_lane == true"
                      @value-changed="newLaneSelected(data)"
                    />
                  </div>
                </template>
              <DxColumn 
                data-field="altered_datetime"
                caption="Updated"
                data-type="datetime"
                name="selectCarrierAltered"
              />
              <DxColumn 
                data-field="altered_datetime"
                caption="Seconds.Milliseconds"
                v-model:visible="isProgrammerLevel"
                name="selectCarrierAlteredSecond"
                cell-template="isProAltered"
              />
                <template #isProAltered="{data}">
                  <div>{{ selectCarrierSecsAndMilli(data.text) }}</div>
                </template>
              <DxColumn 
                data-field="created_datetime"
                caption="Created"
                data-type="datetime"
                name="selectCarrier"
              />
              <DxColumn 
                data-field="created_datetime"
                caption="Seconds.Milliseconds"
                v-model:visible="isProgrammerLevel"
                name="selectCarrierSecond"
                cell-template="isProX"
              />
                <template #isProX="{data}">
                  <div>{{ selectCarrierSecsAndMilli(data.text) }}</div>
                </template>
          </DxDataGrid>
        </div>
        <!-- Add Popup -->
        <DxPopup 
          v-model:visible="showAddShippingCodePopup"
          :drag-enabled="false"
          :hide-on-outside-click="false"
          :show-close-button="true"
          :show-title="true"
          height="30%"
          width="35%"
          title="Add A New Shipping Code:"
          @hidden="closeAddShipPopup"
        >
          <!-- <DxScrollView
            show-scrollbar="always"
            :scroll-by-content="true"
            :scroll-by-thumb="true"
            width="100%"
            height="90%" 
          > -->
            <div style="height: 100%; position: relative">
              <label>
                Enter Shipping Code:
              </label>
              <div>
                <DxTextBox 
                  value-change-event="keyup"
                  v-model:value="newShipCode"
                  width="100"
                  styling-mode="underlined"
                  style="margin-bottom: 10px;"
                />
              </div>
              <div><strong>**Shipping Code MUST BE SPELLED CORRECTLY - CASE SENSITIVE**</strong></div>
              <div style="position: absolute; bottom: 0; width: 100%;">
                <!-- <div> -->
                <div style="display: flex; justify-content: space-evenly;">
                  <div>
                    <DxButton 
                      text="Confirm"
                      type="success"
                      styling-mode="contained"
                      :disabled="newShipCode == ''"
                      @click="clickToAddNewShipCode"
                    />
                  </div>
                  <div>
                    <DxButton 
                      text="Exit Without Adding"
                      type="default"
                      styling-mode="contained"
                      @click="closeAddShipPopup"
                    />
                  </div>
                </div>
              </div>
            </div> 
          <!-- </DxScrollView> -->
        </DxPopup>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import auth from '../auth';
import { useRouter } from 'vue-router';
import databaseName from '../myFunctions/databaseName';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import { confirm } from 'devextreme/ui/dialog';
import notify from 'devextreme/ui/notify';
import { DxLookup } from 'devextreme-vue/lookup';
import { DxPopup } from 'devextreme-vue/popup';
// import { DxScrollView } from 'devextreme-vue/scroll-view';
import DxTextBox from 'devextreme-vue/text-box';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxScrolling,
  DxColumnFixing,
  DxEditing
} from 'devextreme-vue/data-grid';

const loadingVisible = ref(false);
databaseName.checkWebsiteVersion();
const router = useRouter();

const userToken = ref('');
const userName = ref('');
const userSecurity = ref('');
const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  loadingVisible.value = true;
  //console.log(user.data);
  if(user.data.userSecurity === 'administrator' || user.data.userSecurity === 'programmer' || user.data.userSecurity === 'editor'){
    userToken.value = user.data.userJwtToken;
    userSecurity.value = user.data.userSecurity;
    userName.value = user.data.userIdName;
    loadingVisible.value = false;
  } else {
    alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");
    loadingVisible.value = false;
    router.push('/login-form?redirect=/select-ship-lanes');
  }
  if (user.data.userSecurity === 'programmer') {
    isProgrammerLevel.value = true;
  } else {
    isProgrammerLevel.value = false;
  }
}).catch(error=>{
  loadingVisible.value = false;
  //console.log(error.response);
  if(error.response){
    alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else if(error.request){
    alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else{
    alert('Unknown error\nMake sure that you and the server have not lost connection');
    return;
  }
});

const mainGridData = ref([]);
const mainGridLookupdata = ref([]);

const getLaneMappingData = () => {
  mainGridData.value = [];
  mainGridLookupdata.value = [];
  loadingVisible.value = true;

  axios.get('api/Sorter/Mapping?Scanner=SCAN02').then((resp) => {
    console.log('data', resp.data.data);
    let localMainData = [];
    if (resp.status === 200) {
      for (let d = 0; d < resp.data.data.DivertCodes.length; d++) {
        localMainData.push({ divert_code: resp.data.data.DivertCodes[d].divert_code, kvk_lane_name: null, created_datetime: null, altered_datetime: null, hospital_lane: false });
      }
      // localMainData.push({ divert_code: 'HOSPITAL', kvk_lane_name: 'HOSPITAL', created_datetime: null, hospital_lane: true });
      
      for (let i = 0; i < resp.data.data.LaneMap.length; i++) {
        if (resp.data.data.LaneMap[i].kvk_lane_name.toUpperCase() !== 'HOSPITAL') {
          if (mainGridLookupdata.value.findIndex(l => l.kvk_lane_name == resp.data.data.LaneMap[i].kvk_lane_name) == -1) {
            mainGridLookupdata.value.push({ disp_lane_name: `${resp.data.data.LaneMap[i].wms_lane_name}: ${resp.data.data.LaneMap[i].kvk_lane_name}`, wms_lane_name: resp.data.data.LaneMap[i].wms_lane_name, kvk_lane_name: resp.data.data.LaneMap[i].kvk_lane_name });
          }
          let tempIndex = localMainData.findIndex(f => f.divert_code == resp.data.data.LaneMap[i].divert_code);
          if (tempIndex != -1) {
            localMainData[tempIndex].kvk_lane_name = resp.data.data.LaneMap[i].kvk_lane_name;
            localMainData[tempIndex].created_datetime = resp.data.data.LaneMap[i].created_datetime;
            localMainData[tempIndex].altered_datetime = resp.data.data.LaneMap[i].altered_datetime;
          }
        }
      }
      mainGridLookupdata.value.push({ disp_lane_name: 'Unassign', wms_lane_name: 'Unassign', kvk_lane_name: 'Unassign' });
      loadingVisible.value = false;
      mainGridData.value = localMainData;
    } else {
      mainGridData.value = [];
      mainGridLookupdata.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    // console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
getLaneMappingData();

const newLaneSelected = (laneData) => {
  console.log('select', laneData);
  if (laneData.data.kvk_lane_name.toUpperCase() == 'UNASSIGN') {
    deleteLaneBinding(laneData);
  } else if (laneData.value == null) {
    addLaneBinding(laneData);
  } else {
    laneBindingChanged(laneData);
  }
};

const laneBindingChanged = (laneData) => {
  let newDestObj = {
    kvk_lane_name: laneData.data.kvk_lane_name,
    divert_code: laneData.data.divert_code,
    overwrite_divert_code: laneData.data.divert_code,
  }
  let newLaneConfirm = confirm(`Are you Sure You Want To Change <strong>${laneData.value}</strong> To <strong>${laneData.data.kvk_lane_name}</strong>?`, 'Confirm Change');
  newLaneConfirm.then((dialogResult) => {
    if (dialogResult) {
      loadingVisible.value = true;
      axios({
        method: 'PATCH',
        url: 'api/Sorter/Mapping',
        data: newDestObj,
        headers:{
          'Authorization': `Bearer ${userToken.value}`,
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        if (resp.status === 200) {
          getLaneMappingData();
          notify(resp.data.message, 'success', 4000);
          loadingVisible.value = false;
        } else {
          notify(resp.data.message, 'error', 10000);
          getLaneMappingData();
          loadingVisible.value = false;
        }
      }).catch(error=>{
        loadingVisible.value = false;
        if (error.response.status === 500) {
          notify(error.response.data.message, 'error', 10000);
          getLaneMappingData();
          return;
        } else {
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the server have not lost connection');
            return;
          }
        }
      })
    } else {
      notify('Lane NOT Changed', 'info', 5000);
      getLaneMappingData();
    }
  })
};
const deleteLaneBinding = (laneData) => {
  let deleteDestObj = {
    divert_code: laneData.data.divert_code,
    kvk_lane_name: laneData.value
  };
  let deleteLaneConfirm = confirm(`Are You Sure You Want To Unassign <strong>${laneData.value}</strong>?`, 'Confirm Change');
  deleteLaneConfirm.then((dialogResult) => {
    if (dialogResult) {
      loadingVisible.value = true;
      axios({
        method: 'DELETE',
        url: 'api/Sorter/Mapping',
        data: deleteDestObj,
        headers:{
          'Authorization': `Bearer ${userToken.value}`,
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        if (resp.status === 200) {
          getLaneMappingData();
          notify(resp.data.message, 'success', 4000);
          loadingVisible.value = false;
        } else {
          notify(resp.data.message, 'error', 10000);
          getLaneMappingData();
          loadingVisible.value = false;
        }
      }).catch(error=>{
        loadingVisible.value = false;
        if (error.response.status === 500) {
          notify(error.response.data.message, 'error', 10000);
          getLaneMappingData();
          return;
        } else {
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the server have not lost connection');
            return;
          }
        }
      })
    } else {
      notify('Lane NOT Unassigned', 'info', 5000);
      getLaneMappingData();
    }
  })
};

const addLaneBinding = (laneData) => {
  let addDestObj = {
    divert_code: laneData.data.divert_code,
    kvk_lane_name: laneData.data.kvk_lane_name
  };
  let addLaneConfirm = confirm(`Are You Sure You Want To Assign <strong>${laneData.data.kvk_lane_name}</strong>?`, 'Confirm Change');
  addLaneConfirm.then((dialogResult) => {
    if (dialogResult) {
      loadingVisible.value = true;
      axios({
        method: 'PUT',
        url: 'api/Sorter/Mapping',
        data: addDestObj,
        headers:{
          'Authorization': `Bearer ${userToken.value}`,
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        if (resp.status === 200) {
          getLaneMappingData();
          notify(resp.data.message, 'success', 4000);
          loadingVisible.value = false;
        } else {
          notify(resp.data.message, 'error', 10000);
          getLaneMappingData();
          loadingVisible.value = false;
        }
      }).catch(error=>{
        loadingVisible.value = false;
        if (error.response.status === 500) {
          notify(error.response.data.message, 'error', 10000);
          getLaneMappingData();
          return;
        } else {
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the server have not lost connection');
            return;
          }
        }
      })
    } else {
      notify('Lane NOT Assigned', 'info', 5000);
      getLaneMappingData();
    }
  })
};


// function arrClone(source){
//   return JSON.parse(JSON.stringify(source));
// }
const DeleteShipCodeRow = (e) => {
  console.log(e);
  let deleteShipCodeObj = {
    divertCode: e.data.divert_code
  }
  loadingVisible.value = true;
  axios({
    method: 'DELETE',
    url: 'api/Sorter/DeleteShipCodeAcushnet',
    params: deleteShipCodeObj,
    headers:{
      'Authorization': `Bearer ${userToken.value}`,
      'Content-Type': 'application/json'
    }
  }).then((resp) => {
    if (resp.status === 200) {
      getLaneMappingData();
      notify(resp.data.message, 'success', 4000);
      loadingVisible.value = false;
    } else {
      notify(resp.data.message, 'error', 10000);
      getLaneMappingData();
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      getLaneMappingData();
      return;
    } else {
      if(error.response){
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the server have not lost connection');
        return;
      }
    }
  })
};

const clickToAddNewShipCode = () => {
  let userAddedShipCodeObj = {
    Scanner: 'SCAN02',
    divertCode: newShipCode.value,
  };

  let addSortCodeConfirm = confirm(`Are You Sure You Want To Add Ship Sorter Code: <strong>${newShipCode.value}</strong>?`, 'Confirm Add Ship Sorter Code');
  addSortCodeConfirm.then((dialogResult) => {
    if (dialogResult) {
      loadingVisible.value = true;
      axios({
        method: 'POST',
        url: 'api/Sorter/AddMapping',
        params: userAddedShipCodeObj,
        headers:{
          'Authorization': `Bearer ${userToken.value}`,
          'Content-Type': 'application/json'
        }
      }).then((resp) => {
        if (resp.status === 200) {
          getLaneMappingData();
          notify(resp.data.message, 'success', 4000);
          loadingVisible.value = false;
        } else {
          notify(resp.data.message, 'error', 10000);
          getLaneMappingData();
          loadingVisible.value = false;
        }
        showAddShippingCodePopup.value = false;
      }).catch(error=>{
        loadingVisible.value = false;
        showAddShippingCodePopup.value = false;
        if (error.response.status === 500) {
          notify(error.response.data.message, 'error', 10000);
          getLaneMappingData();
          return;
        } else {
          if(error.response){
            alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else if(error.request){
            alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
            return;
          }
          else{
            alert('Unknown error\nMake sure that you and the server have not lost connection');
            return;
          }
        }
      })
    } else {
      notify('Ship Sorter Code NOT Added', 'info', 5000);
      getLaneMappingData();
      showAddShippingCodePopup.value = false;
    }
  })
};

const showAddShippingCodePopup = ref(false);
const addNewShippingCode = () => {
  showAddShippingCodePopup.value = !showAddShippingCodePopup.value;
};

const newShipCode = ref('');
const closeAddShipPopup = () => {
  newShipCode.value = '';
  showAddShippingCodePopup.value = false;
};

const selectCarrierSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};
const hospitalLaneBGColor = (e) => {
  if (e.rowType == 'data' && e.data.hospital_lane == true) {
    // e.rowElement.style.backgroundColor = '#4a0101';
    e.rowElement.classList.add('my-class');
  }
};
</script>

<style lang="scss">
.my-class > td {
  background-color: #4a0101 !important;
}
</style>

<template>
  <div>
    <!-- <h2 class="content-block">Weight Tolerance</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div style="margin-bottom: 15px;">
          <DxButton 
            text="Refresh"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="GetData"
          />
        </div>
        <div>
          <DxDataGrid
            v-model:data-source="dataset"
            :row-alternation-enabled="true"
            :show-borders="true"
            :word-wrap-enabled="true"
            :column-hiding-enabled="false"
            :column-auto-width="true"
            style="margin-top: 15px;"
            ref="addRowToDataGridRefWeightTol"
            @row-inserted="AddNewWeightTol"
            @row-updated="UpdateRow"
            @row-removed="DeleteRow"
          >
            <DxPaging :enabled="false"/>
            <DxScrolling mode="standard"/>
            <DxFilterRow
              :visible="true"
            />
            <DxEditing     
              :allow-updating="true"
              :allow-deleting="true"
              :allow-adding="true"
              mode="row"
            />
            <DxColumn 
              data-field="min_weight"
              caption="Min Weight"
              alignment="left"
              data-type="number"
            />
            <DxColumn 
              data-field="note"
              caption="Logic"
              alignment="left"
              :allow-editing="false"
            />
            <DxColumn 
              data-field="lower_tol"
              caption="Lower Tolerance %"
              alignment="left"
              data-type="number"
            />
            <DxColumn 
              data-field="upper_tol"
              caption="Upper Tolerance %"
              alignment="left"
              data-type="number"
            />
            <DxToolbar>
              <DxItem
                location="after"
                template="addRowTemplateNewWeightTol"
              />
            </DxToolbar>
              <template #addRowTemplateNewWeightTol>
                <DxButton
                  text="Add New Weight Tolerance"
                  type="default"
                  styling-mode="contained"
                  icon="add"
                  @click="customAddRowBtnClickNewWeightTol"
                />
              </template>
          </DxDataGrid>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import auth from '../auth';
import { useRouter, useRoute } from 'vue-router';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxButton from 'devextreme-vue/button';
import axios from 'axios';
import {
  DxDataGrid,
  DxColumn,
  DxPaging,
  DxScrolling,
  DxEditing,
  DxFilterRow,
  DxToolbar,
  DxItem,
} from 'devextreme-vue/data-grid';
//import { DxLoadIndicator } from 'devextreme-vue/load-indicator';
import notify from 'devextreme/ui/notify';

databaseName.checkWebsiteVersion();
const loadingVisible = ref(false);

const router = useRouter();
const route = useRoute();
const dataset = ref([]);

const userToken = ref('');
const userName = ref('');
const userSecurity = ref('');
// const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  loadingVisible.value = true;
  //console.log(user.data);
  if(user.data.userSecurity === 'administrator' || user.data.userSecurity === 'programmer' || user.data.userSecurity === 'editor'){
    userToken.value = user.data.userJwtToken;
    userSecurity.value = user.data.userSecurity;
    userName.value = user.data.userIdName;
    loadingVisible.value = false;
  } else {
    alert("Access Denied for this page.\nMake sure that you are logged in and that you meet the security level.");
    loadingVisible.value = false;
    router.push({  path: "/login-form",  query: { returnUrl: route.fullPath } });
  }
  // if (user.data.userSecurity === 'programmer') {
  //   isProgrammerLevel.value = true;
  // } else {
  //   isProgrammerLevel.value = false;
  // }
}).catch(error=>{
  loadingVisible.value = false;
  //console.log(error.response);
  if(error.response){
    alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else if(error.request){
    alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
    return;
  }
  else{
    alert('Unknown error\nMake sure that you and the server have not lost connection');
    return;
  }
});

const GetData = () =>{
  loadingVisible.value = true;
  axios.get('api/Admin/WeightToleranceData').then((resp)=>{
    //console.log('HI',resp.data);
    let tmpData = resp.data.data.WeightTolData;

    for(let c = 0; c < tmpData.length; c++)
    {
      if(tmpData.length - 1 == c)
      {
        tmpData[c].note = `Greater Than ${tmpData[c].min_weight} LBS`;
      }
      else
      {
        tmpData[c].note = `Greater Than ${tmpData[c].min_weight} LBS AND Less Than Or Equal To ${tmpData[c + 1].min_weight} LBS`;
      }
    }

    dataset.value = tmpData;
  }).catch(error=>{
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  }).finally(()=>{
    loadingVisible.value = false;
  })
}
GetData();

const DeleteRow = (e) =>{
  if(e.key.min_weight == 0)
  {
    notify('You Cannot Delete The Lower Limit', 'error', 10000);
    GetData();
    return;
  }

  let returnJSON = arrClone(e.key);
  loadingVisible.value = true;
  axios({
    method: 'DELETE',
    url: 'api/Admin/WeightTol',
    data: returnJSON,
    headers:{
      'Authorization': `Bearer ${userToken.value}`,
      'Content-Type': 'application/json'
    }
  }).then((resp)=>{
    notify(resp.data.message, 'success', 6000);
  }).catch(error=>{
    if(error.response){
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the serve have not lost connection');
      return;
    }
  }).finally(()=>{
    loadingVisible.value = false;
    GetData();
  });

}

//Add New Grid Row Button
const addRowToDataGridRefWeightTol = ref(null);
const customAddRowBtnClickNewWeightTol = () => {
  const dataGrid = addRowToDataGridRefWeightTol.value.instance;
  dataGrid.addRow();
};

const AddNewWeightTol = (e) =>{
  //console.log(e);
  if(typeof e.key.min_weight === 'undefined')
  {
    notify('You Must Specify A Min Weight For Now Tolerance', 'error', 10000);
    GetData();
    return;
  }
  else if(typeof e.key.lower_tol === 'undefined')
  {
    notify('You Must Specify A Lower Tolerance Percentage For Now Tolerance', 'error', 10000);
    GetData();
    return;
  }
  else if(typeof e.key.upper_tol === 'undefined')
  {
    notify('You Must Specify An Upper Tolerance Percentage For Now Tolerance', 'error', 10000);
    GetData();
    return;
  }

  if(e.key.lower_tol <= 0 || e.key.lower_tol > 100)
  {
    notify('The Lower Tolerance Percent Must Be Between 1 and 100', 'error', 10000);
    GetData();
    return;
  }
  else if(e.key.upper_tol <= 0 || e.key.upper_tol > 100)
  {
    notify('The Upper Tolerance Percent Must Be Between 1 and 100', 'error', 10000);
    GetData();
    return;
  }

  for(let c = 0; c < dataset.value.length; c++)
  {
    //console.log(dataset.value[c]);
    if(e.key.min_weight == dataset.value[c].min_weight && typeof dataset.value[c].id_kvkweighttol !== 'undefined')
    {
      notify('Weight Tolerance For Specified Min Weight Already Exists', 'error', 10000);
      GetData();
      return;
    }
  }

  let returnJSON = arrClone(e.key);
  loadingVisible.value = true;
  axios({
    method: 'PUT',
    url: 'api/Admin/WeightTol',
    data: returnJSON,
    headers:{
      'Authorization': `Bearer ${userToken.value}`,
      'Content-Type': 'application/json'
    }
  }).then((resp)=>{
    notify(resp.data.message, 'success', 6000);
  }).catch(error=>{
    if(error.response){
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the serve have not lost connection');
      return;
    }
  }).finally(()=>{
    loadingVisible.value = false;
    GetData();
  });
    

}

const UpdateRow = (e) =>{
  console.log(e);

  if(e.key.lower_limit_flag && e.key.min_weight != 0)
  {
    notify('You Cannot Change The Min Weight Of The Lower Limit, It Must Remain 0', 'error', 10000);
    GetData();
    return;
  }

  if(e.key.lower_tol <= 0 || e.key.lower_tol > 100)
  {
    notify('The Lower Tolerance Percent Must Be Between 1 and 100', 'error', 10000);
    GetData();
    return;
  }
  else if(e.key.upper_tol <= 0 || e.key.upper_tol > 100)
  {
    notify('The Upper Tolerance Percent Must Be Between 1 and 100', 'error', 10000);
    GetData();
    return;
  }

  let ds_index = dataset.value.findIndex(p => p.id_kvkweighttol == e.key.id_kvkweighttol);
  if(ds_index == -1)
  {
    notify('Error KOZ Unable To Find Index Of Altered Record In Data Set', 'error', 10000);
    GetData();
    return;
  }
  else
  {
    if(dataset.value.length - 1 != ds_index)
    {
      if(e.key.min_weight >= dataset.value[ds_index + 1].min_weight)
      {
        notify('You Cannot Change The Min Weight To Be Higher Than The Next Tolerances Min Weight', 'error', 10000);
        GetData();
        return;
      }
    }
    else if(ds_index != 0)
    {
      if(e.key.min_weight <= dataset.value[ds_index - 1].min_weight)
      {
        notify('You Cannot Change The Min Weight To Be Lower Than The Next Tolerances Min Weight', 'error', 10000);
        GetData();
        return;
      }
    }

    let returnJSON = arrClone(e.key);
    loadingVisible.value = true;
    axios({
      method: 'PATCH',
      url: 'api/Admin/WeightTol',
      data: returnJSON,
      headers:{
        'Authorization': `Bearer ${userToken.value}`,
        'Content-Type': 'application/json'
      }
    }).then((resp)=>{
      notify(resp.data.message, 'success', 6000);
    }).catch(error=>{
      if(error.response){
        notify(error.response.data.message, 'error', 10000);
        return;
      }
      else if(error.request){
        alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return;
      }
      else{
        alert('Unknown error\nMake sure that you and the serve have not lost connection');
        return;
      }
    }).finally(()=>{
      loadingVisible.value = false;
      GetData();
    });
    
  }

  //loadingVisible.value = true;
}

function arrClone(source){
  return JSON.parse(JSON.stringify(source));
}


</script>

<style lang="scss">
</style>


<template>
  <div>
    <!-- <h2 class="content-block">{{ mainTitleTxt }}</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div :class="showMobileViewOrNot.topTwoBoxes">
          <div :class="showMobileViewOrNot.topSearchBox">
            <div :class="showMobileViewOrNot.topSearchAndRadio">
              <div>
                <DxButton 
                  text="Refresh Page"
                  type="success"
                  styling-mode="contained"
                  icon="refresh"
                  style="margin-bottom: 5px;"
                  @click="getMostRecentLPNs"
                />
              </div>
              <div>
                <DxButton 
                  :text="isReportingTxt"
                  type="default"
                  styling-mode="contained"
                  style="margin-bottom: 10px;"
                  @click="toggleReporting"
                />
              </div>
              <div style="width: 75%;">
                <DxTextBox 
                  placeholder="Enter Value..."
                  width="100%"
                  height="3.0rem"
                  styling-mode="filled"
                  style="margin-bottom: 10px;"
                  v-model:value="textBoxsearchedValues.SearchValue"
                  @enter-key="searchBtnClick(searchBy)"
                />
                <DxRadioGroup 
                  :items="textBoxSearchRadioBtnArr"
                  v-model:value="searchBy"
                  display-expr="disp"
                  value-expr="val"
                  style="margin: 0px 15px;"
                />
                <DxButton 
                  text="Search"
                  type="default"
                  styling-mode="contained"
                  style="margin-top: 15px;"
                  @click="searchBtnClick(searchBy)"
                />
              </div>
            </div>
            <div :class="showMobileViewOrNot.topRecentGrid">
              <h6 style="text-align: left; margin: 5px 0px;"><b>View By:</b></h6>
              <DxRadioGroup 
                  :items="dataGridDisplayFilter"
                  v-model:value="dispBy"
                  display-expr="disp"
                  value-expr="disp"
                  layout="horizontal"
                  style="margin: 0px 15px;"
                />
              <h6 style="text-align: center; margin: 5px 0px;"><b>{{ recentGridTitle }}</b></h6>
              <div v-if="dispBy=='LPN'">
                <DxDataGrid
                  :data-source="recentGridDataSource"
                  :row-alternation-enabled="true"
                  :show-borders="true"
                  :selection="{mode: 'single'}"
                  :column-auto-width="true"
                  :word-wrap-enabled="true"
                  :column-hiding-enabled="false"
                  height="225"
                >
                  <DxScrolling 
                    mode="virtual"
                    show-scrollbar="onHover"
                  />
                  <DxColumn
                    data-field="lpn_barcode"
                    caption="LPN"
                    alignment="center"
                    header-cell-template="captionHeader"
                    cell-template="lpnLinkSearch"
                  />
                    <template #captionHeader="{data}">
                      <div style="color: white;">{{ data.column.caption }}</div>
                    </template>
                    <template #lpnLinkSearch="{data}">
                      <div class="gridLink" @click="searchLPNRecentGridLink(data.data.lpn_barcode)">{{data.data.lpn_barcode}}</div>
                    </template>
                </DxDataGrid>
              </div>
              <div v-else>
                <DxDataGrid
                  :data-source="recentGridDataSource"
                  :row-alternation-enabled="true"
                  :show-borders="true"
                  :selection="{mode: 'single'}"
                  :column-auto-width="true"
                  :word-wrap-enabled="true"
                  :column-hiding-enabled="false"
                  height="225"
                >
                  <DxScrolling 
                    mode="virtual"
                    show-scrollbar="onHover"
                  />
                  <DxColumn
                    data-field="wms_order_number"
                    caption="Order Number"
                    alignment="center"
                    header-cell-template="captionHeader"
                    cell-template="lpnLinkSearch"
                  />
                    <template #captionHeader="{data}">
                      <div style="color: white;">{{ data.column.caption }}</div>
                    </template>
                    <template #lpnLinkSearch="{data}">
                      <div class="gridLink" @click="searchLPNRecentGridLink(data.data.lpn_barcode)">{{data.data.wms_order_number}}</div>
                    </template>
                </DxDataGrid>
              </div>
              

            </div>
          </div>
          <div :class="showMobileViewOrNot.topInfoBox">
            <div style="float: right;">
              <DxButton 
                text="Refresh LPN"
                type="success"
                styling-mode="contained"
                icon="refresh"
                @click="getSingleLPNData"
                :disabled="selectedLPN == 'No LPN Selected'"
              />
            </div>
            <div>
              <div v-if="isReporting">
                <div v-if="singleLPNFound == false" style="color: red; font-size: 16px;"><strong>No ID Selected</strong></div>
                <div v-else>

                  <div style="color: red;"><strong>Continer Barcode: </strong> {{ selectedLPNObject.lpn_barcode }}</div>
                  <div style="color: red;"><strong>Order Number: </strong> {{ selectedLPNObject.wms_order_number }}</div>
                  <div style="color: red;"><strong>Carrier: </strong> {{ selectedLPNObject.shipping_carrier }}</div>
                  <div style="color: red;"><strong>Class Of Service: </strong> {{ selectedLPNObject.class_of_service }} </div>
                  <div style="color: red;"><strong>Zip Code: </strong> {{ selectedLPNObject.ship_to_zip }} </div>
                  <div style="color: red;"><strong>Container Type: </strong> {{ selectedLPNObject.container_type }} </div>
                  <div style="color: red;"><strong>Starting Zone: </strong> {{ selectedLPNObject.starting_zone }} </div>
                  <div style="color: red;"><strong>Order Start: </strong> {{ selectedLPNObject.order_start_station_name }}  {{ selectedLPNObject.order_started_verified_datetime }} </div>
                  <div style="color: red;"><strong>Packing Station: </strong> {{ selectedLPNObject.pack_station_name }} {{ selectedLPNObject.pack_complete_datetime }} </div>
                  <!-- <div style="color: red;"><strong>Tracking Number:</strong> {{ selectedLPNObject.lpn_tracking_number }}</div>
                  <div style="color: red;"><strong>WMS Estimated Weight:</strong> {{ selectedLPNObject.container_calc_weight == null ? '' : selectedLPNObject.container_calc_weight }}</div> -->
                  <!-- <div style="color: red;"><strong>Scale Weight:</strong> {{ selectedLPNObject.container_actual_weight == null ? '' : selectedLPNObject.container_actual_weight }}</div> -->
                  <!-- <div style="color: red;"><strong>Import Date:</strong> {{ selectedLPNObject.created_datetime.replace('T', ' ') }}</div>
                  <div style="color: red;"><strong>Ship Via:</strong> {{ selectedLPNObject.ship_via }}</div> -->
                </div>
              </div>
              <div v-else>
                <div v-if="singleLPNFound == false" style="font-size: 16px;"><strong>No ID Selected</strong></div>
                <div v-else>
                  <div v-if="selectedLPNObject.binding_active" style="background-color: #8bc34a; width: 30%;"><strong>TOTE:</strong> {{ selectedLPNObject.tote_barcode }}</div>
                  <div v-else><strong>TOTE: </strong> {{ selectedLPNObject.tote_barcode }}</div>
                  <div><strong>Continer Barcode: </strong> {{ selectedLPNObject.lpn_barcode }}</div>
                  <div><strong>Order Number: </strong> {{ selectedLPNObject.wms_order_number }}</div>
                  <div><strong>Carrier: </strong> {{ selectedLPNObject.shipping_carrier }}</div>
                  <div><strong>Class Of Service: </strong> {{ selectedLPNObject.class_of_service }} </div>
                  <div><strong>Zip Code: </strong> {{ selectedLPNObject.ship_to_zip }} </div>
                  <div><strong>Container Type: </strong> {{ selectedLPNObject.container_type }} </div>
                  <div><strong>Starting Zone: </strong> {{ selectedLPNObject.starting_zone }} </div>
                  <div><strong>Order Start: </strong> {{ selectedLPNObject.order_start_station_name }}  {{ selectedLPNObject.order_started_verified_datetime }} </div>
                  <div><strong>Packing Station: </strong> {{ selectedLPNObject.pack_station_name }} {{ selectedLPNObject.pack_complete_datetime }} </div>
                  <!-- <div><strong>Tracking Number:</strong> {{ selectedLPNObject.lpn_tracking_number }}</div>
                  <div><strong>WMS Estimated Weight:</strong> {{ selectedLPNObject.container_calc_weight == null ? '' : selectedLPNObject.container_calc_weight }}</div> -->
                  <!-- <div><strong>Scale Weight:</strong> {{ selectedLPNObject.container_actual_weight == null ? '' : selectedLPNObject.container_actual_weight }}</div> -->
                  <!-- <div><strong>Import Date:</strong> {{ selectedLPNObject.created_datetime.replace('T', ' ') }}</div>
                  <div><strong>Ship Via:</strong> {{ selectedLPNObject.ship_via }}</div> -->
                </div>
              </div>
              <hr />
              <div style="display: flex; justify-content: space-evenly; flex-wrap: wrap; margin-top: 25px;">
                <div v-for="(LPN) in lpnScaleCancelCode" :key="LPN.id">
                  <div :class="LPN.color" @click="clickToSeeScaleErrorOrCode">
                    <div><strong>{{ LPN.disp }}</strong></div>
                    <div v-if="LPN.id == 1">{{ LPN.additionalInfo }}</div>
                    <div v-if="LPN.id == 3">{{ LPN.additionalInfo }}</div>
                    <div v-if="scalePassShowWeight && LPN.id == 1">Scale Weight: {{ selectedLPNObject.container_actual_weight == null ? '' : selectedLPNObject.container_actual_weight }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div v-if="showMainOrAnnexInfo">
          <div style="display: flex; margin: 25px 0px 15px 0px;">
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyNotNeeded"></div>
              Zone Not Required
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyPending"></div>
              Zone Pending
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyComplete"></div>
              Scanned Into Zone
            </div>
          </div>
          <div style="display: flex; justify-content: space-evenly; flex-wrap: wrap;">
            <div v-for="(zone) in pickZonesObjs" :key="zone.id">
              <div :class="zone.color" @click="clickToOpenPickZoneGreen(zone.disp)">{{ zone.disp }}</div>
            </div>
          </div>
        </div> -->
        <div>
          <div style="display: flex; margin: 25px 0px 15px 0px;">
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyNotNeeded"></div>
              No Picks In Zone
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyPending"></div>
              Pending Pick/s In Zone
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyComplete"></div>
              Picks In Zone Complete
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyAlert"></div>
              Missing Pick/s From Zone
            </div>
          </div>
          <div class="shipLanesGridStyle">
            <div v-for="(lane) in pickZonesObjs" :key="lane.id">
              <!-- <div :class="lane.color" @click="clickToOpenShipLaneGreen(lane.kvk_lane_name)">{{ lane.disp }}</div> -->
              <div :class="lane.color">{{ lane.disp }}</div>
            </div>
          </div>
        </div>
        <DxPopup 
          v-model:visible="pickZonesPopup"
          :drag-enabled="false"
          :hide-on-outside-click="true"
          :show-close-button="true"
          :show-title="true"
          height="40%"
          width="40%"
        >
          <!-- @hidden="pickZonesCompletePopupClosed" -->
          <div>
            <strong>Completed Date Time:</strong> {{ pickZonesPopupMsgToShow.replace('T', ' ') }}
          </div> 
        </DxPopup>
        <DxPopup 
          v-model:visible="scaleErrorPopup"
          :drag-enabled="false"
          :hide-on-outside-click="true"
          :show-close-button="true"
          :show-title="true"
          height="30%"
          width="40%"
          :title="scaleOrCodeTitle"
        >
          <div>
            {{ scaleErrorMsg }}
          </div>
        </DxPopup>
        <!-- <DxPopup 
          v-model:visible="shipZonePopup"
          :drag-enabled="false"
          :hide-on-outside-click="true"
          :show-close-button="true"
          :show-title="true"
          height="40%"
          width="40%"
        > -->
          <!-- @hidden="pickZonesCompletePopupClosed" -->
          <!-- <div>
            <strong>Completed Date Time:</strong> {{ shipLaneCompletedTimeToShow.replace('T', ' ') }}
          </div>   -->
        <!-- </DxPopup> -->
      </div>
    </div>

    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <div style="display: flex;">
          <div class="sideButtons">
            <DxScrollView
              show-scrollbar="always"
              :scroll-by-content="true"
              :scroll-by-thumb="true"
              width="100%"
              height="90%" 
            >
              <div class="sideBtnStyle" v-for="(button) in buttonOptions" :key="button.id">
                <div :class="button.colorClass" @click="sideButtonClick(button)">{{ button.disp }}</div>
              </div>
            </DxScrollView>
          </div>
          <div id="mainContent">
            <div v-if="showTransLogDiv">
              <h4><strong>LPN: {{ selectedLPNObject.lpn_barcode }} Transaction Log</strong></h4>
              <DxDataGrid
                :data-source="transLogGridDataSource"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
                width="100%" 
                height="550"
                :column-auto-width="true"
              >
                <DxScrolling mode="virtual" />
                <DxFilterRow
                  :visible="true"
                />
                  <DxColumn 
                    data-field="transaction_datetime"
                    caption="Date"
                    data-type="datetime"
                    alignment="left"
                    name="transLogFirst"
                  />
                  <DxColumn 
                    data-field="transaction_datetime"
                    caption="Seconds.Milliseconds"
                    v-model:visible="isProgrammerLevel"
                    name="transLogSecond"
                    cell-template="isPro"
                  />
                    <template #isPro="{data}">
                      <div>{{ transLogSecsAndMilli(data.text) }}</div>
                    </template>
                  <DxColumn 
                    data-field="transaction_code"
                    caption="Code"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="scannerpoint"
                    caption="Scanner"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="plc"
                    caption="PLC"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="transaction_message"
                    caption="Message"
                    alignment="left"
                  />
              </DxDataGrid>
            </div>
            <div v-else-if="showRejectDiv">
              <h4><b>Hospital Lane</b></h4>
              <div :class="showMobileViewOrNot.hospitalSearchLayout">
                <div>
                  <strong>Select Date</strong>
                  <!-- :min="nineDaysAgo" -->
                  <DxDateBox
                    v-model:value="now"
                    type="date"
                    styling-mode="underlined"
                    width="95%"
                  />
                </div>
                <!-- <div>
                  <strong>End Date (Previous)</strong> -->
                  <!-- :min="tenDaysAgo" -->
                  <!-- <DxDateBox
                    v-model:value="previousDateTime"
                    
                    :max="previousTimeMax"
                    type="datetime"
                    styling-mode="underlined"
                    width="95%"
                  />
                </div> -->
                <div>
                  <DxButton 
                    text="Search"
                    type="default"
                    styling-mode="contained"
                    @click="getHospitalData"
                  />
                </div>
              </div>
              <!-- <div v-if="showHospFoundAndGrid"> -->
              <div>
                <h5 style="margin-top: 0px;"><strong>{{ numberInHospitalLane }}</strong></h5>
                <div class="errorMsgListContainer">
                  <div v-for="(errMsg) in errorTypeArray" :key="errMsg.messageType">
                    <p class="errorMsgListStyle"><strong>{{ errMsg.messageType }}:</strong> {{ errMsg.count }}</p>
                  </div>
                </div>
                <!-- <div style="margin-top: 25px;">
                  <strong>Filter by Cleared or Not Cleared</strong>
                  <DxSelectBox 
                    :data-source="hospitalClearedSelectData"
                    v-model:value="clearedFilterValue"
                    display-expr="disp"
                    value-expr="val"
                    styling-mode="underlined"
                    width="150"
                    @value-changed="getHospitalGridInstance"
                  />
                </div> -->
                <div>
                  <DxDataGrid
                    :data-source="hospitalDataSource"
                    ref="hospGridRef"
                    :column-hiding-enabled="false"
                    :row-alternation-enabled="true"
                    :show-borders="true"
                    :show-column-headers="true"
                    :word-wrap-enabled="true"
                    width="100%" 
                    height="525"
                    :column-auto-width="true"
                    @editor-preparing="modifySearchPanelStyleezHospital"
                    >
                    <DxFilterRow
                      :visible="true"
                    />
                    <DxScrolling mode="virtual" />
                    <DxSearchPanel
                      :visible="true"
                      :width="170"
                      placeholder="Filter Results..."
                    />
                      <DxColumn 
                        data-field="lpn_barcode"
                        caption="LPN"
                        alignment="left"
                        cell-template="hospSearchLPN"
                      />
                        <template #hospSearchLPN="{ data }">
                          <div class="gridLink" @click="searchLPNRecentGridLink(data.text)">{{data.text}}</div>
                        </template>
                      <DxColumn 
                        data-field="error_title"
                        caption="Error"
                        alignment="left"
                      />
                      <DxColumn 
                        data-field="error_details"
                        caption="Error Message"
                        alignment="left"
                      />
                        <!-- cell-template="errorMsgLink" -->
                        <!-- <template #errorMsgLink="{ data }">
                          <div class="gridLink" @click="seeFullErrorMsg(data)">{{ parseErrorMessage(data.text) }}</div>
                        </template> -->
                      <DxColumn 
                        data-field="error_datetime"
                        caption="Error Date"
                        data-type="datetime"
                        alignment="left"
                        name="hospFirst"
                      />
                      <!-- <DxColumn 
                        data-field="created_timestamp"
                        caption="Seconds.Milliseconds"
                        v-model:visible="isProgrammerLevel"
                        name="hospSecond"
                        cell-template="isPro"
                      /> -->
                        <template #isPro="{data}">
                          <div>{{ transLogSecsAndMilli(data.text) }}</div>
                        </template>
                      <!-- <DxColumn 
                        data-field="completed_timestamp"
                        caption="Error Completed Date"
                        data-type="datetime"
                        alignment="left"
                        name="hospCompFirst"
                      /> -->
                      <!-- <DxColumn 
                        data-field="completed_timestamp"
                        caption="Seconds.Milliseconds"
                        v-model:visible="isProgrammerLevel"
                        name="hospCompSecond"
                        cell-template="isProComp"
                      />
                        <template #isProComp="{data}">
                          <div>{{ transLogSecsAndMilli(data.text) }}</div>
                        </template> -->
                      <DxColumn 
                        data-field="cleared"
                        caption="Cleared"
                        alignment="center"
                        
                        data-type="boolean" 
                      /><!-- 
                        <template #cleared-status="{ data }">
                          <div :class="clearedStatusColor(data)">{{ getClearedStatus(data) }}</div>
                        </template> -->
                  </DxDataGrid>
                </div>
              </div>
            </div>
            <div v-else-if="showPickZonesDiv">
              <h4><strong>LPN: {{ selectedLPNObject.lpn_barcode }} Pick Zones</strong></h4>
              <DxDataGrid
                :data-source="pickZonesForLPNDataSource"
                :column-hiding-enabled="false"
                :row-alternation-enabled="true"
                :show-borders="true"
                :word-wrap-enabled="true"
                width="100%" 
                height="550"
                :column-auto-width="true"
              >
                <DxScrolling mode="virtual" />
                <DxFilterRow
                  :visible="true"
                />
                  <DxColumn 
                    data-field="item_number"
                    caption="Item"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="bin_location"
                    caption="PTL Location"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="pick_zone_added_datetime"
                    caption="Pick Added Date Time"
                    data-type="datetime"
                    alignment="left"
                    name="pickAddFirst"
                  />
                  <DxColumn 
                    data-field="quantity"
                    caption="Expected QTY"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="completed_quantity"
                    caption="Picked QTY"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="completed_code"
                    caption="PTL Status"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="completed_user"
                    caption="Picked By"
                    alignment="left"
                  />
                  <DxColumn 
                    data-field="completed_datetime"
                    caption="Complete Date Time"
                    data-type="datetime"
                    alignment="left"
                    name="pickCompleteFirst"
                  />
                  <DxColumn 
                    data-field="description"
                    caption="Description"
                    alignment="left"
                  />
              </DxDataGrid>
            </div>

          </div>

        </div>

      </div>
    </div>

  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import auth from '../auth';
import DxLoadPanel from 'devextreme-vue/load-panel';
import notify from 'devextreme/ui/notify';
import { alert } from "devextreme/ui/dialog";
import DxTextBox from 'devextreme-vue/text-box';
import DxButton from 'devextreme-vue/button';
import DxRadioGroup from 'devextreme-vue/radio-group';
import { DxScrollView } from 'devextreme-vue/scroll-view';
import { DxDateBox } from 'devextreme-vue/date-box';
// import { DxSelectBox } from 'devextreme-vue/select-box';
import { DxPopup } from 'devextreme-vue/popup';
import {
  DxDataGrid,
  DxColumn,
  DxSearchPanel,
  // DxPaging,
  DxFilterRow,
  // DxPager,
  DxScrolling,
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

const loadingVisible = ref(false);
databaseName.checkWebsiteVersion();

const isMobile = databaseName.getScreenSizeSmall();
const showMobileViewOrNotFunction = () => {
  let topTwoBoxes = '';
  let topSearchBox = '';
  let topSearchAndRadio = '';
  let topRecentGrid = '';
  let topInfoBox = '';
  // let shippingLanesLayout = '';
  let hospitalSearchLayout = '';
  if (isMobile) {
    topTwoBoxes = '';
    topSearchBox = 'topSearchBoxMobile';
    topSearchAndRadio = 'topSearchAndRadioMobile';
    topRecentGrid = 'topRecentGridMobile';
    topInfoBox = 'topInfoBoxMobile';
    // shippingLanesLayout = 'shippingLanesContainerMobile';
    hospitalSearchLayout = 'hospitalSearchLayoutMobile';
  } else {
    topTwoBoxes = 'topBoxContainer';
    topSearchBox = 'topSearchBoxNotMobile';
    topSearchAndRadio = 'topSearchAndRadioNotMobile';
    topRecentGrid = 'topRecentGridNotMobile';
    topInfoBox = 'topInfoBoxNotMobile';
    // shippingLanesLayout = 'shippingLanesContainer';
    hospitalSearchLayout = 'hospitalSearchLayoutNotMobile';
  }
  return {
    topTwoBoxes,
    topSearchBox,
    topSearchAndRadio,
    topRecentGrid,
    topInfoBox,
    // shippingLanesLayout,
    hospitalSearchLayout,
  }
};
const showMobileViewOrNot = showMobileViewOrNotFunction();
// const displayMainOrAnnex = ref('annex');
// const annexOrMainRadioBtnGroup = [
//   { disp: 'Main', val: 'main' },
//   { disp: 'Annex', val: 'annex' },
// ];

const textBoxSearchRadioBtnArr = [
  { disp: 'Search By LPN', val: 'LPN' },
  { disp: 'Search By TOTE', val: 'TOTE' },
  { disp: 'Search By ORDER', val: 'ORDER' },
];
const dataGridDisplayFilter = [
  {disp: 'ORDER'},
  {disp: "LPN"}
];
const dispBy = ref('ORDER');
const searchBy = ref('LPN');
const textBoxsearchedValues = ref({
  SearchMethod: 'LPN',
  SearchValue: '',
  isReporting: '0'
});

const isReportingTxt = ref('View Archive Data > 10');
const isReporting = ref(false);
const mainTitleTxt = ref('EZ View (Active Data < 10)');
const toggleReporting = () => {
  if(isReporting.value){
    // this.activityCountsDisabled = false;
    isReporting.value = false;
    isReportingTxt.value = "View Archive Data < 10";
    mainTitleTxt.value = 'EZ View (Active Data < 10)';
  } else {
    // this.activityCountsDisabled = true;
    isReporting.value = true;
    isReportingTxt.value = 'View Active Data > 10';
    mainTitleTxt.value = 'EZ View (Archive Data > 10)';
  }
  // this.orderStartDisabled = true;
  // this.lineItemsDisabled = true;
  // this.importDisabled = true;
  
  // loadingVisible.value = true;

  // this.auditLogDisabled = true;
  // this.selectedBarcodeObject = {};
  // this.selectedBarcode = "No HU Selected";
  // this.resetScannerDateTimeObj();
  // this.resetStatusButtonOption();
  // getMostRecentLPNs();
};

const pickZonesObjs = ref([
  { id: 1, disp: 'Bay 02', value: '02', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 2, disp: 'Bay 03', value: '03', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 3, disp: 'Bay 04', value: '04', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 4, disp: 'Bay 05', value: '05', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 5, disp: 'Bay 08', value: '08', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 6, disp: 'Bay 09', value: '09', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 7, disp: 'Bay 10', value: '10', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 8, disp: 'Bay 11', value: '11', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 9, disp: 'Bay 12', value: '12', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 10, disp: 'Bay 14', value: '14', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 11, disp: 'Bay 15', value: '15', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 12, disp: 'Bay 16', value: '16', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 13, disp: 'Bay 17', value: '17', completedDatetime: '', color: 'pickZoneDefault' }
]);

const lpnScaleCancelCode = ref([
  //{ id: 1, disp: 'PANDA', additionalInfo: '', color: 'lpnScaleDefault' },
  // { id: 2, disp: 'Cancel', additionalInfo: '', color: 'lpnScaleDefault' },
  // { id: 3, disp: 'Condition Code', additionalInfo: '', color: 'lpnScaleDefault' },
]);

const scalePassShowWeight = ref(false);
const scaleOrCodeTitle = ref('');
const scaleErrorMsg = ref('');

const shipLanes = ref([
  { id: 1, disp: 'Lane01', kvk_lane_name: 'LANE01', wms_lane_name: 'lane01', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 2, disp: 'Lane02', kvk_lane_name: 'LANE02', wms_lane_name: 'lane02', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 3, disp: 'Lane03', kvk_lane_name: 'LANE03', wms_lane_name: 'lane03', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 4, disp: 'Lane04', kvk_lane_name: 'LANE04', wms_lane_name: 'lane04', completedDatetime: '', color: 'pickZoneDefault' },
  { id: 5, disp: 'Hospital', kvk_lane_name: 'HOSPITAL', wms_lane_name: 'lane05', completedDatetime: '', color: 'pickZoneDefault' },
]);

// const showMainOrAnnexInfo = ref(false);

const resetPickZoneButtons = () => {
  pickZonesObjs.value.forEach((s) => {
    s.color = 'pickZoneDefault';
  });
  shipLanes.value.forEach((a) => {
    a.color = 'pickZoneDefault';
  });
  lpnScaleCancelCode.value.forEach((l) => {
    l.color = 'lpnScaleDefault',
    l.additionalInfo = '';
  });
  scalePassShowWeight.value = false;
  scaleErrorMsg.value = '';
};

const buttonOptions = ref([
  { id: 'transaction', disp: 'Transaction Log', colorClass: 'disabledSideToolBarButton' },
  { id: 'pick', disp: 'Pick Zones', colorClass: 'disabledSideToolBarButton' },
  //{ id: 'hospital', disp: 'Hospital Lane', colorClass: 'normalButton' },
]);

const selectedLPN = ref('No LPN Selected');
const selectedLPNObject = ref({});

const showTransLogDiv = ref(false);
const showPickZonesDiv = ref(false);
const showRejectDiv = ref(false);

const singleLPNFound = ref(false);
const recentGridTitle = ref('200 Most Recent Cartons')
const recentLPNsSearchParams = ref({
  Limit: '200',
  isReporting: '0'
});
const recentGridDataSource = ref([]);
const getMostRecentLPNs = () => {
  loadingVisible.value = true;
  singleLPNFound.value = false;
  recentGridTitle.value = '200 Most Recent Cartons';
  selectedLPN.value = 'No LPN Selected';
  //TYLER
  selectedLPNObject.value = {};

  showTransLogDiv.value = false;
  showPickZonesDiv.value = false;
  showRejectDiv.value = false;

  if(isReporting.value) {
    textBoxsearchedValues.value.isReporting = '1';
  } else {
    textBoxsearchedValues.value.isReporting = '0';
  }

  resetPickZoneButtons();

  buttonOptions.value.forEach((btn) => {
    switch (btn.id) {
      case 'transaction':
      case 'pick':
        btn.colorClass = 'disabledSideToolBarButton';
        break;
      case 'hostpital':
        btn.colorClass = 'normalButton';
        break;
    }
  });

  axios.get('api/Order/EZTrace', { params: recentLPNsSearchParams.value }).then((resp) => {
    if (resp.status === 200) {
      console.log(resp.data.data);
      recentGridDataSource.value = resp.data.data;
      loadingVisible.value = false;
    } else {
      recentGridDataSource.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
    
  });
};
getMostRecentLPNs();

const pickZonesForLPNDataSource = ref([]);
const searchBtnClick = (searchFilter) => {  
  singleLPNFound.value = false;
  showTransLogDiv.value = false;
  showPickZonesDiv.value = false;
  showRejectDiv.value = false;
  pickZonesForLPNDataSource.value = [];

  buttonOptions.value.forEach((btn) => {
    switch (btn.id) {
      case 'transaction':
      case 'pick':
        btn.colorClass = 'disabledSideToolBarButton';
        break;
      case 'hospital':
        btn.colorClass = 'normalButton';
        break;
    }
  });

  resetPickZoneButtons();

  textBoxsearchedValues.value.SearchMethod = searchFilter;

  loadingVisible.value = true;
  if(isReporting.value) {
    textBoxsearchedValues.value.isReporting = '1';
  } else {
    textBoxsearchedValues.value.isReporting = '0';
  }

  axios.get('api/Order/OrderDetails', { params: textBoxsearchedValues.value }).then((resp) => {
    console.log('search', resp.data);
    if (resp.status === 200) {
      if (resp.data.data.length == 0) {
        notify("There Was No Match or Nothing Contained the Searched Value. Make Sure you are using the correct Search By", "warning", 7000);
        loadingVisible.value = false;
        return;
      }
      recentGridDataSource.value = resp.data.data;
      if (resp.data.data.length == 1) {
        selectedLPNObject.value = recentGridDataSource.value[0];
        selectedLPNObject.value.pack_complete_datetime = selectedLPNObject.value.pack_complete_datetime !== null ? selectedLPNObject.value.pack_complete_datetime.replace('T', ' ') : selectedLPNObject.value.pack_complete_datetime;
        selectedLPNObject.value.order_started_verified_datetime = selectedLPNObject.value.order_started_verified_datetime !== null ? selectedLPNObject.value.order_started_verified_datetime.replace('T', ' ') : selectedLPNObject.value.order_started_verified_datetime;
        selectedLPN.value = recentGridDataSource.value[0].lpn_barcode;
        recentGridTitle.value = `${selectedLPN.value}`;
        loadingVisible.value = false;
        // notify(resp.data.message, 'success', 4000);
        singleLPNSearchParams.value.LPNBarcode = selectedLPN.value;
        textBoxsearchedValues.value.SearchValue = '';
        //getSingleLPNData();
        getPickZonesInfo();
        // if (!showMainOrAnnexInfo.value) {
        //getShipLaneInfo(recentGridDataSource.value);
        // }
      } else {
        if (searchBy.value == 'LPN') {
          recentGridTitle.value = `LPNs Containing ${textBoxsearchedValues.value.SearchValue}`;
        }
        else if(searchBy.value == 'ORDER')
        {
          recentGridTitle.value = `Orders Containing ${textBoxsearchedValues.value.SearchValue}`;
        } 
        else {
          recentGridTitle.value = `Bound To TOTE ${textBoxsearchedValues.value.SearchValue}`;
        }
        loadingVisible.value = false;
        textBoxsearchedValues.value.SearchValue = '';
      }
    } else {
      loadingVisible.value = false;
      getMostRecentLPNs();
      notify(resp.data.message, 'error', 10000);
      textBoxsearchedValues.value.SearchValue = '';
    }
  }).catch(error=>{
    loadingVisible.value = false;
    getMostRecentLPNs();
    // console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};

const searchLPNRecentGridLink = (data) => {
  textBoxsearchedValues.value.SearchValue = data;
  textBoxsearchedValues.value.SearchMethod = 'LPN';
  searchBtnClick(textBoxsearchedValues.value.SearchMethod);
};

const singleLPNSearchParams = ref({
  LPNBarcode: '',
  isReporting: '0'
});
const transLogGridDataSource = ref([]);
const getSingleLPNData = () => {
  scalePassShowWeight.value = false;
  scaleErrorMsg.value = '';
  shipLaneCompletedTimeToShow.value = '';
  loadingVisible.value = true;
  if(isReporting.value) {
    singleLPNSearchParams.value.isReporting = '1';
  } else {
    singleLPNSearchParams.value.isReporting = '0';
  }

  axios.get('api/Order/LPNTransLog', { params: singleLPNSearchParams.value }).then((resp) => {
    console.log('transLog', resp.data);
    if (resp.status === 200) {
      transLogGridDataSource.value = resp.data.data;
      singleLPNFound.value = true;
      loadingVisible.value = false;
      buttonOptions.value.find(b => b.id == 'transaction').colorClass = 'selectedButton';
      buttonOptions.value.find(b => b.id == 'pick').colorClass = 'normalButton';
      showTransLogDiv.value = true;
      // getScanAndValInfo(transLogGridDataSource.value);
      for (let i = resp.data.data.length - 1; i >= 0; i--) {
      //SHIP LANES
      // if (resp.data.data[i].transaction_code.toUpperCase().includes('CONFIRMED')) {
      //   if (resp.data.data[i].transaction_code.toUpperCase().includes('LANE01')) {
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE01').color = 'pickZoneGood';

      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE02').color = 'pickZoneDefault';
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE03').color = 'pickZoneDefault';
      //   } else if (resp.data.data[i].transaction_code.toUpperCase().includes('LANE02')) {
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE02').color = 'pickZoneGood';

      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE01').color = 'pickZoneDefault';
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE03').color = 'pickZoneDefault';
      //   } else if (resp.data.data[i].transaction_code.toUpperCase().includes('LANE03')) {
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE03').color = 'pickZoneGood';

      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE01').color = 'pickZoneDefault';
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE02').color = 'pickZoneDefault';
      //   } else if (resp.data.data[i].transaction_code.toUpperCase().includes('HOSPITAL')) {
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE03').color = 'pickZoneBad';

      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE01').color = 'pickZoneDefault';
      //     shipLanes.value.find(l => l.kvk_lane_name == 'LANE02').color = 'pickZoneDefault';
      //   }
      // }

        //SCALE WEIGHT
        // if (resp.data.data[i].transaction_code.toUpperCase() == 'SCALE-ERROR' || resp.data.data[i].transaction_code.toUpperCase() == 'WEIGHT-FAIL') {
        //   lpnScaleCancelCode.value.find(s => s.id == 1).color = 'lpnScaleBad';
        //   lpnScaleCancelCode.value.find(s => s.id == 1).additionalInfo = 'Click To See Error';
        //   scaleErrorMsg.value = resp.data.data[i].transaction_message;
        //   if (resp.data.data[i].transaction_code.toUpperCase() == 'SCALE-ERROR') {
        //     scaleOrCodeTitle.value = 'Scale Error';
        //   } else {
        //     scaleOrCodeTitle.value = 'Weight Fail';
        //   }
        //   scalePassShowWeight.value = false;
        // } else if (resp.data.data[i].transaction_code.toUpperCase() == 'WEIGHT-PASS') {
        //   lpnScaleCancelCode.value.find(s => s.id == 1).color = 'lpnScaleGood';
        //   lpnScaleCancelCode.value.find(s => s.id == 1).additionalInfo = '';
        //   scaleErrorMsg.value = '';
        //   scalePassShowWeight.value = true;
        // }

        //CANCELED LPN
        // if (resp.data.data[i].transaction_code.toUpperCase() == 'CANCELOLPN') {
        //   lpnScaleCancelCode.value.find(c => c.id == 2).color = 'lpnCancelAlert';
        // } 

        //CONDITION CODE
        // if (resp.data.data[i].transaction_code.toUpperCase() == 'APPLYOLPNCONDITIONCODE') {
        //   lpnScaleCancelCode.value.find(d => d.id == 3).color = 'lpnScaleBad';
        //   lpnScaleCancelCode.value.find(d => d.id == 3).additionalInfo = 'Click To See Code';
        //   scaleErrorMsg.value = resp.data.data[i].transaction_message;
        //   scaleOrCodeTitle.value = 'Condition Code';
        // } else if (resp.data.data[i].transaction_code.toUpperCase() == 'REMOVEOLPNCONDITIONCODE') {
        //   lpnScaleCancelCode.value.find(d => d.id == 3).color = 'lpnScaleAlert';
        //   lpnScaleCancelCode.value.find(d => d.id == 3).additionalInfo = 'A Code Was Removed';
        //   scaleOrCodeTitle.value = '';
        // }
      }
    } else {
      transLogGridDataSource.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};

const sideButtonClick = (buttonInfo) => {
  buttonOptions.value.forEach((btn) => {
    switch (btn.id) {
      case 'transaction':
      case 'pick':
        if (selectedLPN.value !== 'No LPN Selected') {
          btn.colorClass = 'normalButton';
        }
        break;
      case 'hospital':
        btn.colorClass = 'normalButton';
        break;
    }
  });

  switch (buttonInfo.id) {
    case 'transaction':
      getSingleLPNData();
      buttonInfo.colorClass = 'selectedButton';
      break;
    case 'pick':
      getPickZonesInfo('sideClick');
      buttonInfo.colorClass = 'selectedButton';
      break;
    case 'hospital':
      clickToShowHospitalDiv();
      buttonInfo.colorClass = 'selectedButton';
      break;
  }
};

const pickZonesPopupMsgToShow = ref('');
const getPickZonesInfo = (click) => {
  loadingVisible.value = true;
  pickZonesForLPNDataSource.value = [];
  pickZonesPopupMsgToShow.value = '';

  let isReportingForZones = '';
  if(isReporting.value) {
    isReportingForZones = '1';
  } else {
    isReportingForZones = '0';
  }

  let lpnParams = {
    LPNBarcode: selectedLPN.value,
    isReporting: isReportingForZones
  }

  axios.get('api/Order/EZTraceMasterDetails', { params: lpnParams }).then((resp) => {
    console.log('pick', resp.data);
    if (resp.status === 200) {
      pickZonesForLPNDataSource.value = resp.data.data.PickZones;
      transLogGridDataSource.value = resp.data.data.TransLog;

      singleLPNFound.value = true;
      loadingVisible.value = false;
      buttonOptions.value.find(b => b.id == 'transaction').colorClass = 'selectedButton';
      buttonOptions.value.find(b => b.id == 'pick').colorClass = 'normalButton';
      showTransLogDiv.value = true;
/*
<div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyNotNeeded"></div>
              No Picks In Zone
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyPending"></div>
              Pending Pick/s In Zone
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyComplete"></div>
              Picks In Zone Complete
            </div>
            <div class="keyIndividualContainer">
              <div class="EZViewPickStatusKeyAlert"></div>
              Missing Pick/s From Zone
            </div>
 */
      let tmpHolder = [];
      for (let p = 0; p < pickZonesForLPNDataSource.value.length; p++) {
        let zone = pickZonesForLPNDataSource.value[p].bin_location.substring(0, 2);
        /*
        if (pickZonesForLPNDataSource.value[p].pick_zone_complete_time != null) {
          pickZonesObjs.value.find(z => z.value == pickZonesForLPNDataSource.value[p].pick_zone).color = 'pickZoneGood';
          pickZonesObjs.value.find(z => z.value == pickZonesForLPNDataSource.value[p].pick_zone).completedDatetime = pickZonesForLPNDataSource.value[p].pick_zone_complete_time;
        } else {
          pickZonesObjs.value.find(z => z.value == pickZonesForLPNDataSource.value[p].pick_zone).color = 'pickZoneAlert';
        }
        */
       /*
       {
         zone: '',
         complete: false,
         inProgress: false
       }
       */
       
        let zoneIndex = tmpHolder.findIndex(p => p.zone == zone);
        if(zoneIndex == -1)
        {
          console.log(typeof(pickZonesForLPNDataSource.value[p].completed_code));
          if(pickZonesForLPNDataSource.value[p].completed_code === null)
          {
            tmpHolder.push({zone: zone, complete: false, inProgress: false});
          }
          else // we think its completed based on this one
          {
            tmpHolder.push({zone: zone, complete: true, inProgress: false});
          }
        }
        else
        {
          //if its marked as complete and this bin is not mark it as in progress
          if(pickZonesForLPNDataSource.value[p].completed_code === null && tmpHolder[zoneIndex].complete)
          {
            tmpHolder[zoneIndex].complete = false;
            tmpHolder[zoneIndex].inProgress = true;
          }
          else if(pickZonesForLPNDataSource.value[p].completed_code !== null && !tmpHolder[zoneIndex].inProgress && !tmpHolder[zoneIndex].complete)
          {
            tmpHolder[zoneIndex].inProgress = true;
          }
        }
       
      }
      console.log(tmpHolder);
      for(let c = 0; c < tmpHolder.length; c++)
      {
        let pickIndex = pickZonesObjs.value.findIndex(p => p.value == tmpHolder[c].zone);
        console.log(pickIndex);
        if(pickIndex != -1)
        {
          //console.log(pickIndex);
          console.log(pickZonesObjs.value[pickIndex]);
          if(tmpHolder[c].complete)
          {
            pickZonesObjs.value[pickIndex].color = 'pickZoneGood';
          }
          else if(tmpHolder[c].inProgress)
          {
            pickZonesObjs.value[pickIndex].color = 'pickZoneBad';
          }
          else
          {
            pickZonesObjs.value[pickIndex].color = 'pickZoneAlert';
          }

        }
      }
      
      if (click == 'sideClick') {
        showPickZonesDiv.value = true;

        showTransLogDiv.value = false;
        showRejectDiv.value = false;
      }
      loadingVisible.value = false;
    } else {
      pickZonesForLPNDataSource.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};

const shipLaneCompletedTimeToShow = ref(''); 
// const getShipLaneInfo = (olpnData) => {
//   //LOOK AT TRANS LOG INFO - MOST RECENT DIVERT CONFIRM (I.E. LANE02 == LANE02 IS GREEN)

//   console.log('shipLanes', olpnData);
//   shipLaneCompletedTimeToShow.value = '';
//   for (let a = 0; a < shipLanes.value.length; a++) {
//     if ((shipLanes.value[a].wms_lane_name == olpnData[0].destination_location) && (olpnData[0].completed_datetime != null)) {
//       shipLanes.value[a].color = 'pickZoneGood';
//       shipLanes.value[a].completedDatetime = olpnData[0].completed_datetime;
//     } 
//     //RED = HOSP DIVERT

//     // else if (shipLanes.value[a].wms_lane_name == olpnData[0].destination_location) {
//     //   shipLanes.value[a].color = 'pickZoneAlert';
//     // }
//   }
// };

const scaleErrorPopup = ref(false);
const clickToSeeScaleErrorOrCode = () => {
  scaleErrorPopup.value = !scaleErrorPopup.value;
};

const pickZonesPopup = ref(false);
// const clickToOpenPickZoneGreen = (whichBtn) => {
//   pickZonesPopupMsgToShow.value = '';
//   pickZonesPopupMsgToShow.value = pickZonesObjs.value.find(z => z.disp == whichBtn).completedDatetime;
//   if (pickZonesPopupMsgToShow.value !== '') {
//     pickZonesPopup.value = !pickZonesPopup.value;
//   }
// };
// const shipZonePopup = ref(false);
// const clickToOpenShipLaneGreen = (whichBtn) => {
//   console.log(whichBtn);
//   //shipLaneCompletedTimeToShow.value = '';
//   shipLaneCompletedTimeToShow.value = shipLanes.value.find(a => a.kvk_lane_name == whichBtn).completedDatetime;
//   // if (shipLaneCompletedTimeToShow.value !== '') {
//     shipZonePopup.value = !shipZonePopup.value;
//   // }
// };
// const pickZonesCompletePopupClosed = () => {
//   pickZonesObjs.value.forEach((obj) => {
//     if (obj.completedDatetime != '') {
//       obj.completedDatetime = '';
//     }
//   })
// };
const convertDateMySQL = (date, which) => {
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let year = date.getFullYear();

  let monthString = month.toString();
  if(monthString.length == 1){
    monthString = '0' + monthString;
  }

  let dayString = day.toString();
  if(dayString.length == 1){
    dayString = '0' + dayString;
  }

  let hour = date.getHours();

  let hourString = hour.toString();
  if(hourString.length == 1){
    hourString = '0' + hourString;
  }
  let minutes = date.getMinutes();
  let minutesString = minutes.toString();
  if(minutesString.length == 1){
    minutesString = '0' + minutesString;
  }
  let seconds = date.getSeconds();
  let secondsString = seconds.toString();
  if(secondsString.length == 1){
    secondsString = '0' + secondsString;
  }
  let realDate = year+"-"+monthString+"-"+dayString+" "+hourString+":"+minutesString+":"+secondsString;
  // let realDate = year+"-"+monthString+"-"+dayString;
  // return realDate;
  if (which == 'start') {
    now.value = realDate;
  } else {
    previousDateTime.value = realDate;
  }
};

const now = ref(new Date());
const previousDateTime = ref(new Date());
previousDateTime.value.setHours(previousDateTime.value.getHours() - 2);
const previousTimeMax = ref(new Date());
previousTimeMax.value.setHours(previousTimeMax.value.getHours() - 1);
convertDateMySQL(now.value, 'start');
convertDateMySQL(previousDateTime.value, 'end');
const nineDaysAgo = ref(new Date());
nineDaysAgo.value.setDate(nineDaysAgo.value.getDate() - 9);
const tenDaysAgo = ref(new Date());
tenDaysAgo.value.setDate(tenDaysAgo.value.getDate() - 10);

// const hospitalClearedSelectData = ref([
//   { disp: "Cleared", val: "clear" },
//   { disp: "Not Cleared", val: "notClear" },
//   { disp: "Show All", val: "all" }
// ]);

const clearedFilterValue = ref('all');
const clickToShowHospitalDiv = () => {
  showRejectDiv.value = true;
  getHospitalData();

  showTransLogDiv.value = false;
  showPickZonesDiv.value = false;
};

// const showHospFoundAndGrid = ref(false);
const hospitalDataSource = ref([]);
const getHospitalData = () => {
  loadingVisible.value = true;
  let hospitalSearchParams = {
    date: now.value,
  }

  axios.get('api/Log/ShippingErrors', { params: hospitalSearchParams }).then((resp) => {
    console.log(resp.data.data);
    if (resp.status === 200) {
      hospitalDataSource.value = resp.data.data;
      // showHospFoundAndGrid.value = true;
      loadingVisible.value = false;
      getListOfErrorMessages();
    } else {
      hospitalDataSource.value = [];
      loadingVisible.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};

const hospGridRef = ref(null);
// const getHospitalGridInstance = () => {
//   const dataGrid = hospGridRef.value.instance;
//   if (clearedFilterValue.value == 'clear') {
//     dataGrid.filter([
//       ['cleared', '=', true]
//     ])
//   } else if (clearedFilterValue.value == 'notClear') {
//     dataGrid.filter([
//       ['cleared', '=', false]
//     ])
//   } else {
//     dataGrid.clearFilter();
//   }
//   getListOfErrorMessages();
// };

const errorTypeArray = ref([]);
const numberInHospitalLane = ref('');
const getListOfErrorMessages = () => {
  errorTypeArray.value = [];
  let counter = 0;
  hospitalDataSource.value.forEach((msg) => {
    if (errorTypeArray.value.length == 0) {
      switch (clearedFilterValue.value) {
        case 'clear':
          if (msg.cleared == true) {
            errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
            counter++;
          }
          break;
        case 'notClear':
          if (msg.cleared == false) {
            errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
            counter++;
          }
          break;
        case 'all':
          errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
          counter++;
      }
    } else {
      let h = errorTypeArray.value.findIndex(m => m.messageType == msg.error_title);
      if (h == -1) {
        switch (clearedFilterValue.value) {
          case 'clear':
            if (msg.cleared == true) {
              errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
              counter++;
            }
            break;
          case 'notClear':
            if (msg.cleared == false) {
              errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
              counter++;
            }
            break;
          case 'all':
            errorTypeArray.value.push({ messageType: msg.error_title, count: 1 });
            counter++;
        }
      } else {
        switch(clearedFilterValue.value) {
          case 'clear':
            if (msg.cleared == true) {
              errorTypeArray.value[h].count++;
              counter++;
            } 
            break;
          case 'notClear':
            if (msg.cleared == false) {
              errorTypeArray.value[h].count++;
              counter++;
              }
            break;
          case 'all':
            errorTypeArray.value[h].count++;
            counter++;
            break;
        }
      }
    }
  })
  numberInHospitalLane.value = `${counter} Found`;
};

// const parseErrorMessage = (errorMsg) => {
//   let e = errorMsg.split('<h3>')[1];
//   e = e.split('</h3>')[0];
//   return e;
// };
// const seeFullErrorMsg = (errorData) => {
//   alert(errorData.data.error_message, 'ERROR');
  // let alertResult = alert(errorData.data.ErrorMessage, 'ERROR');
  // alertResult.then(()=>{
  //   console.log('completed');
  // });
// };
// const getClearedStatus = (status) => {
//   if (status.data.cleared == true) {
//     return 'Cleared';
//   } else {
//     return 'Not Cleared';
//   }
// };
// const clearedStatusColor = (status) => {
//  if (status.data.cleared == true) {
//   return 'hospClearedColor';
//  } else {
//   return 'hospNOTClearedColor';
//  }
// };

const transLogSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const modifySearchPanelStyleezHospital = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};
</script>

<style lang="scss">
.topBoxContainer {
  display: flex;
}
.topSearchBoxNotMobile {
  display: flex;
  border: 1px solid black;
  width: 55%;
  padding: 5px;
}
.topSearchBoxMobile {
  border: 1px solid black;
  width: 98%;
  padding: 5px;
}
.topInfoBoxNotMobile {
  border: 1px solid black;
  width: 45%;
  padding: 5px;
}
.topInfoBoxMobile {
  border: 1px solid black;
  width: 98%;
  padding: 5px;
  margin-top: 5px;
}
.topRecentGridNotMobile {
  width: 40%;
}
.topRecentGridMobile {
  width: 95%;
}
.topSearchAndRadioNotMobile {
  width: 60%;
}
.topSearchAndRadioMobile {
  width: 95%;
}
.gridLink {
  cursor: pointer;
  color: white;
  text-decoration: underline;
}
.shipLanesGridStyle {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
}
.pickZoneDefault {
  color: #898787;
  background-color: #3e3d3d;
  border: 1px solid grey; 
  cursor: default;
  pointer-events: none;
  padding: 10px;
  text-align: center;
}
.pickZoneGood {
  background-color: #8bc34a;
  border: 1px solid grey; 
  pointer-events: none;
  cursor: default;
  padding: 10px;
  text-align: center;
}
.pickZoneAlert {
  background-color: #FFC107;
  color: black;
  border: 1px solid grey; 
  pointer-events: none;
  cursor: default;
  padding: 10px;
  text-align: center;
}
.pickZoneBad {
  background-color: #f44336;
  //color: black;
  border: 1px solid grey; 
  pointer-events: none;
  cursor: default;
  padding: 10px;
  text-align: center;
}
.lpnScaleDefault {
  color: #898787;
  background-color: #3e3d3d;
  border: 1px solid grey; 
  width: 150px;
  height: 55px;
  cursor: default;
  pointer-events: none;
  padding: 5px;
  text-align: center;
}
.lpnScaleGood {
  //color: #898787;
  background-color: #8bc34a;
  border: 1px solid grey; 
  width: 150px;
  height: 55px;
  cursor: default;
  pointer-events: none;
  padding: 5px;
  text-align: center;
}
.lpnScaleBad {
  //color: #898787;
  background-color: #f44336;
  border: 1px solid grey; 
  width: 150px;
  height: 55px;
  cursor: pointer;
  //pointer-events: none;
  padding: 5px;
  text-align: center;
}
.lpnScaleAlert {
  //color: #898787;
  background-color: #FFC107;
  border: 1px solid grey; 
  width: 150px;
  height: 55px;
  cursor: default;
  pointer-events: none;
  padding: 5px;
  text-align: center;
}
.lpnCancelAlert {
  //color: #898787;
  background-color: #f44336;
  border: 1px solid grey; 
  width: 150px;
  height: 55px;
  cursor: default;
  pointer-events: none;
  padding: 5px;
  text-align: center;
}
.keyIndividualContainer {
  display: flex;
  align-items: center;
}
.EZViewPickStatusKeyComplete {
  background-color: #8bc34a;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 5px;
  margin-left: 10px;
}
.EZViewPickStatusKeyPending {
  background-color: #FFC107;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 5px;
  margin-left: 10px;
}
.EZViewPickStatusKeyAlert {
  background-color: #f44336;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 5px;
  margin-left: 10px;
}
.EZViewPickStatusKeyNotNeeded {
  background-color: #898787;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  margin-right: 5px;
}
#mainContent {
  margin-left: 10px;
  padding: 10px 16px;
  // height: 100%;
  width: 78%;
  border: 1px solid grey;
}
.sideButtons {
  width: 20%;
  height: 100%;
}
.sideBtnStyle {  
  border: 1px solid black;
  color: #ffff;
  font-size: 18px;
  padding: 4px 0px 4px 0px;
  text-align: center;
  overflow:visible;
  // cursor: pointer;
}
.disabledSideToolBarButton {
  pointer-events: none;
  cursor: default;
  color: #ffff;
  background-color: grey;
}
.normalButton {
  background-color: #3aafa9;
}
.normalButton:hover {
  cursor: pointer;
  background-color: #2f8e8a;
}
.selectedButton {
  background-color: #1b5350;
}
.hospitalSearchLayoutNotMobile {
  display: flex;
  align-items: flex-end;
  margin-bottom: 25px;
}
.hospitalSearchLayoutMobile {
  margin-bottom: 25px;
}
.errorMsgListContainer {
  margin-top: -15px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.errorMsgListStyle {
  margin: 0px 5px 0px 5px;
  padding-right: 5px;
  border-right: 0.5px solid grey;
}
.hospClearedColor {
  background-color: #8bc34a;
}
.hospNOTClearedColor {
  background-color: #f44336;
}
</style>

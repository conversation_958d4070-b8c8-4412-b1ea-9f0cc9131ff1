<template>
  <div>
    <!-- <h2 class="content-block">Audit Log</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div id="pick-menu-cont-audit">
          <div class="pick-audit-select">
            Data Limit:
          <DxSelectBox
            :data-source="selectBoxDS"
            :value="selectBoxDS[0].num"
            display-expr="title"
            value-expr="num"
            styling-mode="underlined"
            @value-changed="handleDataAmountChange"
          />
          </div>
          <div class="alert-audit-select-filter">
          Filter:
          <DxSelectBox
            :data-source="selectBoxFilterDS"
            :value="selectBoxFilterDS[0].value"
            display-expr="title"
            value-expr="value"
            styling-mode="underlined"
            @value-changed="handleDataFilterChange"
          />
          </div>
          <DxButton
            class="pick-refresh-btn"
            :width="120"
            text="Refresh"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="getData"
          />
        </div>
        <div v-if="showHuTextBox">
          <DxTextBox
            id="HUStyle"  
            placeholder="Enter Exact Barcode"
            v-model:value="HUSearchNumber"
            :width="225"
            styling-mode="underlined"
          />
          <DxButton
            id="HUButtonStyle" 
            text="Search with Barcode"
            type="default"
            styling-mode="contained"
            @click="handleDataFilterChangeforHU"
          />
          </div>
        <div id="auditRadioGroup">
        Data Options:
          <DxRadioGroup
            :items="radioBtnArr"
            :value="isReporting"
            display-expr="disp"
            value-expr="Reporting"
            layout="horizontal"
            @value-changed="handleRadioBtnChange"
          />
        </div>
        <DxDataGrid
          :data-source="dataSource"
          :column-hiding-enabled="true"
          :column-auto-width="true"
          :row-alternation-enabled="true"
          :show-borders="true"
          :word-wrap-enabled="true"
          @editor-preparing="modifySearchPanelStyleAuditLog"
          @exporting="onExporting"
        >
        <DxPaging :page-size="10"/>
        <DxPager
          :show-page-size-selector="true"
          :allowed-page-sizes="[10 , 20, 40]"
          :show-info="true" 
        />
        <DxExport   
          :enabled="true"
        />
        <DxFilterRow
          :visible="true"
        />
        <DxSearchPanel
          :visible="true"
          :width="170"
          placeholder="Filter Results..."
        />
          <DxColumn
            data-field="audit_source"
            caption="Source"
          />
          <DxColumn
            data-field="audit_category"
            caption="Category"
            width="170px"
          />
          <DxColumn
            data-field="audit_filter"
            caption="Filter"
          />
          <DxColumn
            data-field="audit_datetime"
            data-type="datetime"
            caption="Date"
            name="auditFirst"
          />
          <DxColumn 
            data-field="audit_datetime"
            caption="Seconds.Milliseconds"
            v-model:visible="isProgrammerLevel"
            name="auditSecond"
            cell-template="isPro"
          />
            <template #isPro="{data}">
              <div>{{ auditSecsAndMilli(data.text) }}</div>
            </template>
          <DxColumn
            data-field="audit_subject"
            caption="Subject"
          />
          <DxColumn
            data-field="audit_message"
            caption="Audit Message"
          />
        </DxDataGrid>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import DxSelectBox from 'devextreme-vue/select-box';
import DxTextBox from 'devextreme-vue/text-box';
import notify from 'devextreme/ui/notify';
import DxRadioGroup from 'devextreme-vue/radio-group';
import axios from 'axios';
import auth from '../auth';
import DxButton from 'devextreme-vue/button';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import databaseName from '../myFunctions/databaseName';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow,
  DxExport,
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

const BasicDataClass = ref({
  Limit: "1000",
  Filter: "Scanner Message Center",
  isReporting: "0"
});
// const databaseInfo = ref(Object);
const dataSource = ref([]);
// const pageSizes = ref([10,20,40]);
const loadingVisible = ref(false);
const radioBtnArr = ref(['Active Data < 10 days', 'Archived Data > 10 days']);
const isReporting = ref("0");
const limit = ref(1000);
const filter = ref("Scanner Message Center");
const selectBoxDS = ref([{title: "1,000", num: 1000},
              {title: "2,000", num: 2000},
              {title: "5,000", num: 5000},
              {title: "10,000", num: 10000},
              {title: "20,000", num: 20000},
              {title: "Unlimited", num: -1}]);
const selectBoxFilterDS = ref([
  {title: 'Scanner', value: 'Scanner Message Center'},
  {title: 'PLC', value: 'PLC Message Center'},
  {title: 'WebSocket', value: 'WS Message Center'},
  {title: 'TCP/IP', value: 'Socket Message Center'},
  {title: 'EDI', value: 'EDI Message Center'},
  {title: 'ALL', value: '-1'}
]);
const HUSearchNumber = ref('');
const showHuTextBox = ref(false);

// databaseInfo.value = databaseName.getPaths();
databaseName.checkWebsiteVersion();
radioBtnArr.value = databaseName.getNumberOfDays();
const getData = () => {
  loadingVisible.value = true;
  axios({
    method: 'GET',
    url: 'api/Log/AuditLogs',
    params: BasicDataClass.value
  })
  .then(resp=>{
    if(resp.status === 200){
      dataSource.value = resp.data.data;
      loadingVisible.value = false;
      // notify(resp.data.message, 'success', 4000);
    }
    else{
      dataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
}
getData();

const modifySearchPanelStyleAuditLog = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};
const handleDataAmountChange = (data) => {
  BasicDataClass.value.Limit = data.value.toString();
  limit.value = data.value;
  
  getData();
};
const handleRadioBtnChange = (data) => {
  if(data.value == 0){
    BasicDataClass.value.isReporting = "0";
    isReporting.value = "0";
    getData();
  }
  else{
    BasicDataClass.value.isReporting = "1";
    isReporting.value = "1";
    getData();
  }
};
const handleDataFilterChange = (data) => {
  BasicDataClass.value.Filter = data.value.toString();
  filter.value = data.value;
  if(filter.value == 'HUSearchNumber') {
    HUSearchNumber.value = '';
    showHuTextBox.value = true;
  } else {
    showHuTextBox.value = false;
  }
  getData();
};
const handleDataFilterChangeforHU = () => {
  // this.BasicDataClass.Filter = data.value.toString();
  filter.value = HUSearchNumber.value;

  BasicDataClass.value.Filter = `HU;${HUSearchNumber.value}`;

  getData();

  // Add different data call here
};

const auditSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Audit Log ${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};
</script>

<style lang="scss">
#pick-menu-cont-audit{
  //width: 600px;
  margin-bottom: 40px;
}
.pick-audit-select{
  float: left;
  width: 100px;
  margin-right: 20px;
  margin-bottom: 20px;
}
.alert-audit-select-filter{
  float: left;
  width: 120px;
  margin-right: 20px;
  margin-bottom: 20px;
}
.pick-refresh-btn{
  float: left;
  margin-right: 20px;
  margin-bottom: 20px;
  margin-top: 15px;
}
#auditRadioGroup{
  margin-top: 70px;
}
#HUStyle {
  margin-bottom: 10px; 
}
#HUButtonStyle {
  margin-bottom: -30px;
}
</style>

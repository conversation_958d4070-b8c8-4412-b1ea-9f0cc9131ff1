<template>
  <div>
    <!-- <h2 class="content-block">Alarm Log</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div style="margin-bottom: 20px;;">
          <DxButton
            text="Refresh"
            type="success"
            styling-mode="contained"
            icon="refresh"
            @click="getData"
          />
        </div>
        <div>
          <DxRadioGroup
            :items="limitOrDateBtnArr"
            :value="limitOrDateBtnArr[0]"
            layout="horizontal"
            @value-changed="radioBtnChangedGridOrder"
          />
        </div>
        <div v-if="showDateSelectBoxes" style="display: flex; margin-top: 25px; align-items: flex-end;">
          <div>
            <strong>Start Date</strong>
            <DxDateBox
              v-model:value="yesterday"
              type="datetime"
              styling-mode="underlined"
              width="95%"
            />
          </div>
          <div>
            <strong>End Date</strong>
            <DxDateBox
              v-model:value="getToday"
              type="datetime"
              styling-mode="underlined"
              width="95%"
            />
          </div>
          <div>
            <DxButton
              text="Search"
              type="default"
              styling-mode="contained"
              icon="refresh"
              @click="searchByDatesSelected"
            />
          </div>
        </div>
        <div>
          <DxDataGrid
            :data-source="dataSource"
            :column-hiding-enabled="true"
            :row-alternation-enabled="true"
            :show-borders="true"
            :column-auto-width="true"
            :word-wrap-enabled="true"
            @editor-preparing="modifySearchPanelStyleAlarmLog"
            @exporting="onExporting"
          >
          <DxPaging :page-size="10"/>
          <DxPager
            :show-page-size-selector="true"
            :allowed-page-sizes="[10, 20, 40]"
            :show-info="true" 
          />
          <DxExport   
            :enabled="true"
          />
          <DxFilterRow
            :visible="true"
          />
          <DxSearchPanel
            :visible="true"
            :width="170"
            placeholder="Filter Results..."
          />
            <DxColumn
              data-field="kvk_optix_alarm_created_datetime"
              data-type="datetime"
              caption="Date"
              alignment="left"
              name="alarmFirst"
            />
            <DxColumn 
              data-field="kvk_optix_alarm_created_datetime"
              caption="Seconds.Milliseconds"
              v-model:visible="isProgrammerLevel"
              name="alarmSecond"
              cell-template="isPro"
            />
              <template #isPro="{data}">
                <div>{{ alarmSecsAndMilli(data.text) }}</div>
              </template>
            <DxColumn
              data-field="kvk_optix_alarm_tag_bit_pos"
              caption="Alarm Code"
              alignment="left"
            />
            <DxColumn
              data-field="kvk_optix_alarm_elapsed"
              caption="Alarm Cleared"
              cell-template="alarmedElapsedTemplate"
            />
              <template #alarmedElapsedTemplate="{ data }">
                <div :class="getAlarmElapsedColor(data)">{{ getClearedOrNot(data) }}</div>
              </template>
            <DxColumn
              data-field="kvk_optix_alarm_message"
              caption="Message"
              alignment="left"
            />
          </DxDataGrid>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import axios from 'axios';
import auth from '../auth';
import databaseName from '../myFunctions/databaseName';
import notify from 'devextreme/ui/notify';
import DxRadioGroup from 'devextreme-vue/radio-group';
import DxButton from 'devextreme-vue/button';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import { DxDateBox } from 'devextreme-vue/date-box';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow,
  DxExport,
} from 'devextreme-vue/data-grid';

const isProgrammerLevel = ref(true);
auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

// const databaseInfo = ref(Object);
const dataSource= ref([]);
// const pageSizes = ref([10,20,40]);
const loadingVisible = ref(false);
const limitOrDateBtnArr = ref(['Last 500 Alarms', 'Search Alarms By Day']);
// const isReporting = ref("0");
// const limit = ref(1000);
// this.databaseInfo = databaseName.getPaths();
databaseName.checkWebsiteVersion();
// radioBtnArr.value = databaseName.getNumberOfDays();

Date.prototype.FormatDatetime = function() {
  let date = this.getFullYear()+'-'+((this.getMonth()+1).toString().padStart(2, '0'))+'-'+this.getDate().toString().padStart(2, '0');
  let time = this.getHours().toString().padStart(2, '0') + ":" + this.getMinutes().toString().padStart(2, '0') + ':' + this.getSeconds().toString().padStart(2, '0');
  return date + ' ' + time;
}

const yesterday = ref(new Date());
yesterday.value.setDate(yesterday.value.getDate() - 1);
const getToday = ref(new Date());

const showDateSelectBoxes = ref(false);
const alarmDataGetParams = ref({
  Limit: 500
});
const radioBtnChangedGridOrder = (data) => {
  if (data.value == 'Last 500 Alarms') {
    showDateSelectBoxes.value = !showDateSelectBoxes.value
    alarmDataGetParams.value = {
      Limit: 500
    }
    getData();
  } else {
    showDateSelectBoxes.value = !showDateSelectBoxes.value
  }
};
const searchByDatesSelected = () => {
  alarmDataGetParams.value = {
    StartDate: yesterday.value.FormatDatetime(),
    EndDate: getToday.value.FormatDatetime()
  }
  getData();
};


const getData = () => {
  loadingVisible.value = true;
  const tmpData = alarmDataGetParams.value;
  // console.log(tmpData);
  axios({
    method: 'GET',
    url: 'api/Log/OptixAlarms',
    params: tmpData
  })
  .then(resp=>{
    // console.log(resp.data);
    if(resp.status === 200){
      dataSource.value = resp.data.data;
      loadingVisible.value = false;
      // notify(resp.data.message, 'success', 4000);
    }
    else{
      dataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if (error.response.status === 500) {
      notify(error.response.data.message, 'error', 10000);
      return;
    }
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  })
}
getData();

const getAlarmElapsedColor = (data) => {
  if (data.data.kvk_optix_alarm_elapsed) {
    return 'alarm-elapsed-true';
  } else {
    return 'alarm-elapsed-false';
  }
};
const getClearedOrNot = (data) => {
  if (data.value) {
    return 'Not Cleared';
  } else {
    return 'Cleared';
  }
};

const modifySearchPanelStyleAlarmLog = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};

const alarmSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Alarm Log ${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};
</script>

<style lang="scss">
.alarm-elapsed-false {
  background-color: #90EE90;
  color: black;
}
.alarm-elapsed-true {
  background-color: #FF0000;
  color: black;
}
</style>


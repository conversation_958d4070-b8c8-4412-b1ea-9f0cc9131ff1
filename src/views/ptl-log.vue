<template>
  <div>
    <!-- <h2 class="content-block">Matthews Lightning Pick Data</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <DxLoadPanel
          :height="500"
          :width="700"
          :visible="loadingVisible"
          :show-indicator="true"
          :show-pane="true"
          :shading="true"
          shading-color="rgba(0,0,0,0.4)"
        />
        <div>
          <h4 style="margin-top: 0px;"><strong>All Messages</strong></h4>
          <div style="display: flex;">
            <div>
            Data Limit:
            <DxSelectBox
              :data-source="selectBoxDS"
              :value="selectBoxDS[0].num"
              display-expr="title"
              value-expr="num"
              styling-mode="underlined"
              style="margin-right: 15px;"
              @value-changed="handleDataAmountChange"
            />
            </div>
            <div>
              <DxButton
                class="mlp-refresh-btn"
                :width="120"
                text="Refresh"
                type="success"
                styling-mode="contained"
                icon="refresh"
                @click="getMLPData"
              />
            </div>
          </div>
          <div v-if="showHuTextBox">
            <DxTextBox
              id="HUStyle"  
              placeholder="Enter Exact Barcode"
              v-model:value="HUSearchNumber"
              :width="225"
              styling-mode="underlined"
            />
            <DxButton
              id="HUButtonStyle" 
              text="Search with Barcode"
              type="default"
              styling-mode="contained"
              @click="handleDataFilterChangeforHU"
            />
          </div>
          <div>
            <DxDataGrid
              id="MLP Data From Audit Log"
              :data-source="mlpAuditLogDataSource"
              :column-hiding-enabled="true"
              :column-auto-width="true"
              :row-alternation-enabled="true"
              :show-borders="true"
              :word-wrap-enabled="true"
              @editor-preparing="modifySearchPanelStyleMatthewData"
              @exporting="onExporting"
            >
              <DxPaging :page-size="10"/>
              <DxPager
                :show-page-size-selector="true"
                :allowed-page-sizes="[10, 20, 40]"
                :show-info="true" 
              />
              <DxExport   
                :enabled="true"
              />
              <DxFilterRow
                :visible="true"
              />
              <DxSearchPanel
                :visible="true"
                :width="170"
                placeholder="Filter Results..."
              />
              <DxColumn
                data-field="audit_source"
                caption="Source"
              />
              <DxColumn
                data-field="audit_category"
                caption="Category"
              />
              <DxColumn
                data-field="audit_filter"
                caption="Filter"
              />
              <DxColumn
                data-field="audit_datetime"
                data-type="datetime"
                caption="Date"
                name="auditFirst"
              />
              <DxColumn 
                data-field="audit_datetime"
                caption="Seconds.Milliseconds"
                v-model:visible="isProgrammerLevel"
                name="auditSecond"
                cell-template="isPro"
              />
                <template #isPro="{data}">
                  <div>{{ mlpSecsAndMilli(data.text) }}</div>
                </template>
              <DxColumn
                data-field="audit_subject"
                caption="Subject"
              />
              <DxColumn
                data-field="audit_message"
                caption="Audit Message"
              />
            </DxDataGrid>
          </div>
        </div>
        <div>
          <h4><strong>Messages By Day</strong></h4>
          <div style="display: flex; align-items: flex-end;">
            <div>
              <strong>Select Date</strong>
              <DxDateBox
                v-model:value="getToday"
                :min="tenDaysAgo"
                type="date"
                styling-mode="underlined"
                width="75%"
              />
            </div>
            <div>
              <DxButton
                text="Submit"
                type="default"
                styling-mode="contained"
                @click="getMLPData"
              />
            </div>
          </div>
          <div style="display: flex;">
            <h5 style="margin-right: 15px;"><strong>Success</strong>: {{ mlpSuccessNum }}</h5>
            <h5><strong>Unsuccessful</strong>: {{ mlpFailNum }}</h5>
          </div>
          <div>
            <DxTabPanel
              :height="400"
              :loop="true"
              :animation-enabled="false"
              :swipe-enabled="true"
            >
            <!-- :key="selectionChangedKey" -->
              <!-- v-model:selected-index="succesOrFailIndex"
              @selection-changed="mlpOnTabChanged" 
              :data-source="tabPanelDataSource"
              :item-template="successOrFailTabSelected" -->
              <DxItem title="Success">
                <template #default>
                  <DxDataGrid
                    id="MLP Success Grid"
                    :data-source="mlpSuccesDataSource"
                    :column-hiding-enabled="true"
                    :column-auto-width="true"
                    :row-alternation-enabled="true"
                    :show-borders="true"
                    :word-wrap-enabled="true"
                    @editor-preparing="modifySearchPanelStyleMatthewData"
                    @exporting="onExporting"
                  >
                    <DxPaging :page-size="10"/>
                    <DxPager
                      :show-page-size-selector="true"
                      :allowed-page-sizes="[10, 20, 40]"
                      :show-info="true" 
                    />
                    <DxExport   
                      :enabled="true"
                    />
                    <DxFilterRow
                      :visible="true"
                    />
                    <DxSearchPanel
                      :visible="true"
                      :width="170"
                      placeholder="Filter Results..."
                    />
                      <DxColumn
                        data-field="lpn_barcode"
                        caption="Carton Number"
                        alignment="left"
                      />
                      <DxColumn
                        data-field="scannerpoint"
                        caption="Scanner"
                      />
                      <DxColumn
                        data-field="transaction_datetime"
                        data-type="datetime"
                        caption="Date"
                        name="tranFirst"
                      />
                      <DxColumn 
                        data-field="transaction_datetime"
                        caption="Seconds.Milliseconds"
                        v-model:visible="isProgrammerLevel"
                        name="tranSecond"
                        cell-template="isPro"
                      />
                        <template #isPro="{data}">
                          <div>{{ mlpSecsAndMilli(data.text) }}</div>
                        </template>
                      <DxColumn
                        data-field="transaction_code"
                        caption="Code"
                      />
                      <DxColumn
                        data-field="transaction_message"
                        caption="Message"
                      />
                  </DxDataGrid>
                </template>
              </DxItem>
              <DxItem title="Unsuccessful">
                <template #default>
                  <DxDataGrid
                    id="MLP Unsuccessful Grid"
                    :data-source="mlpFailureDataSource"
                    :column-hiding-enabled="true"
                    :column-auto-width="true"
                    :row-alternation-enabled="true"
                    :show-borders="true"
                    :word-wrap-enabled="true"
                    @editor-preparing="modifySearchPanelStyleMatthewData"
                    @exporting="onExporting"
                  >
                    <DxPaging :page-size="10"/>
                    <DxPager
                      :show-page-size-selector="true"
                      :allowed-page-sizes="[10, 20, 40]"
                      :show-info="true" 
                    />
                    <DxExport   
                      :enabled="true"
                    />
                    <DxFilterRow
                      :visible="true"
                    />
                    <DxSearchPanel
                      :visible="true"
                      :width="170"
                      placeholder="Filter Results..."
                    />
                      <DxColumn
                        data-field="lpn_barcode"
                        caption="Carton Number"
                        alignment="left"
                      />
                      <DxColumn
                        data-field="scannerpoint"
                        caption="Scanner"
                      />
                      <DxColumn
                        data-field="transaction_datetime"
                        data-type="datetime"
                        caption="Date"
                        name="tranFirst"
                      />
                      <DxColumn 
                        data-field="transaction_datetime"
                        caption="Seconds.Milliseconds"
                        v-model:visible="isProgrammerLevel"
                        name="tranSecond"
                        cell-template="isPro"
                      />
                        <template #isPro="{data}">
                          <div>{{ mlpSecsAndMilli(data.text) }}</div>
                        </template>
                      <DxColumn
                        data-field="transaction_code"
                        caption="Code"
                      />
                      <DxColumn
                        data-field="transaction_message"
                        caption="Message"
                      />
                  </DxDataGrid>
                </template>
              </DxItem>
            </DxTabPanel>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import databaseName from '../myFunctions/databaseName';
import axios from 'axios';
import DxSelectBox from 'devextreme-vue/select-box';
import DxTextBox from 'devextreme-vue/text-box';
import notify from 'devextreme/ui/notify';
import { DxDateBox } from 'devextreme-vue/date-box';
import DxButton from 'devextreme-vue/button';
import { DxLoadPanel } from 'devextreme-vue/load-panel';
import DxTabPanel, { DxItem } from 'devextreme-vue/tab-panel';
import { exportDataGrid } from 'devextreme/excel_exporter';
import { Workbook } from 'exceljs';
import saveAs from 'file-saver';
import {
  DxDataGrid,
  DxColumn,
  DxPager,
  DxPaging,
  DxSearchPanel,
  DxFilterRow,
  DxExport,
} from 'devextreme-vue/data-grid';

const router = useRouter();
// const isProgrammerLevel = ref(true);
// databaseName.isSomeoneLoggedIn().then(user => {
//   if (!databaseName.getDoesPageNeedLogin()) {
//     if (user.data.userFirst == 'View' || user.data.userLast == 'Only') {
//       notify('Please Log In To Use The Demo Site', 'warning', 5000);
//       router.push('/login-form');
//     }
//   }
//   if(user.data.userSecurity === 'programmer'){
//       isProgrammerLevel.value = true;
//     } else {
//       isProgrammerLevel.value = false;
//     }
// });

// const pageSizes = ref([10,20,40]);
const loadingVisible = ref(false);
//const radioBtnArr = ref(['Active Data < 10 days', 'Archived Data > 10 days']);
//const isReporting = ref("0");
const limit = ref(1000);
const filter = ref("PTL Message Center");
const selectBoxDS = ref([{title: "1,000", num: 1000},
              {title: "2,000", num: 2000},
              {title: "5,000", num: 5000},
              {title: "10,000", num: 10000},
              {title: "20,000", num: 20000},
              {title: "Unlimited", num: -1}]);

const HUSearchNumber = ref('');
const showHuTextBox = ref(false);

// databaseInfo.value = databaseName.getPaths();
databaseName.checkWebsiteVersion();
//radioBtnArr.value = databaseName.getNumberOfDays();
const getToday = ref(new Date());
const tenDaysAgo = ref(new Date());
tenDaysAgo.value.setDate(tenDaysAgo.value.getDate() - 10);

Date.prototype.FormatDatetime = function() {
  let date = this.getFullYear()+'-'+((this.getMonth()+1).toString().padStart(2, '0'))+'-'+this.getDate().toString().padStart(2, '0');
  //let time = this.getHours().toString().padStart(2, '0') + ":" + this.getMinutes().toString().padStart(2, '0') + ':' + this.getSeconds().toString().padStart(2, '0');
  return date;
  //return date + ' ' + time;
}
const dateParam = ref({
  ImportDate: getToday.value.FormatDatetime(),
});
const mlpAuditSearchParams = ref({
  Limit: "1000",
  Filter: "PTL Message Center",
  isReporting: "0"
});
const mlpAuditLogDataSource = ref([]);
const getMLPAuditData = () => {
  loadingVisible.value = true;
  axios.get('api/Log/AuditLogs', { params: mlpAuditSearchParams.value }).then((resp) => {
    //console.log(resp.data);
    if (resp.status === 200) {
      mlpAuditLogDataSource.value = resp.data.data;
      loadingVisible.value = false;
    } else {
      mlpAuditLogDataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};
getMLPAuditData();

const mlpSuccesDataSource = ref([]);
const mlpFailureDataSource = ref([]);
const mlpSuccessNum = ref(Number);
const mlpFailNum = ref(Number);
const getMLPData = () => {
  dateParam.value.ImportDate = getToday.value.FormatDatetime(),
  loadingVisible.value = true;
  axios({
    method: 'GET',
    url: 'api/Order/PTLOrderEntryMessages',
    params: dateParam.value
  })
  .then(resp=>{
    //console.log(resp.data.data);
    if (resp.status === 200){
      mlpSuccesDataSource.value = resp.data.data.OrderEntrySuccess;
      mlpFailureDataSource.value = resp.data.data.OrderEntryFailure;
      mlpSuccessNum.value = resp.data.data.OrderEntrySuccess.length;
      mlpFailNum.value = resp.data.data.OrderEntryFailure.length;
      loadingVisible.value = false;
      //notify(resp.data.message, 'success', 4000);
    } else {
      mlpSuccesDataSource.value = [];
      mlpFailureDataSource.value = [];
      loadingVisible.value = false;
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    loadingVisible.value = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
}
getMLPData();

const modifySearchPanelStyleMatthewData = (e) => {
  if (e.parentType === "searchPanel") {
    e.editorOptions.stylingMode = "underlined";
  }
};
const handleDataAmountChange = (data) => {
  mlpAuditSearchParams.value.Limit = data.value.toString();
  limit.value = data.value;
  
  getMLPAuditData();
};

const handleDataFilterChangeforHU = () => {
  // this.BasicDataClass.Filter = data.value.toString();
  filter.value = HUSearchNumber.value;

 //BasicDataClass.value.Filter = `HU;${HUSearchNumber.value}`;

  getMLPData();

  // Add different data call here
};

const mlpSecsAndMilli = (e) => {
  let tempArray = e.split(':');
  return tempArray[tempArray.length - 1];
};

const onExporting = (e) => {
  let today = new Date();
  let date = today.getFullYear()+'-'+(today.getMonth()+1)+'-'+today.getDate();
  let time = today.getHours() + ':' + today.getMinutes() + ':' + today.getSeconds();
  let dateTime = date+' '+time;
  const workbook = new Workbook();
  const worksheet = workbook.addWorksheet('Main sheet');
  exportDataGrid({
    component: e.component,
    worksheet: worksheet,
    customizeCell: function(options) {
      const excelCell = options;
      excelCell.font = { name: 'Arial', size: 12 };
      excelCell.alignment = { horizontal: 'left' };
    } 
  }).then(function() {
      workbook.xlsx.writeBuffer()
        .then(function(buffer) {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `${e.element.id}_${dateTime}.xlsx`);
        });
  });
  e.cancel = true;
};
</script>

<style lang="scss">
// .alert-audit-select-filter{
//   float: left;
//   width: 120px;
//   margin-right: 20px;
//   margin-bottom: 20px;
// }
.mlp-refresh-btn{
  float: left;
  margin-right: 20px;
  margin-bottom: 20px;
  margin-top: 15px;
}
// #auditRadioGroup{
//   margin-top: 70px;
// }
#HUStyle {
  margin-bottom: 10px; 
}
#HUButtonStyle {
  margin-bottom: -30px;
}
</style>
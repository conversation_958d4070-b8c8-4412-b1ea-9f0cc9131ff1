<template>
	<div ref="contentBlockRef" class="content-block dx-card responsive-paddings" :class="{ 'maximized': isMaximized }">
		<!-- Container controls -->
		<div style="display: flex; flex-direction: row-reverse; gap: 10px; position: absolute;top:10px;right:10px;">
			<div v-if="!isMaximizedFromUrl" class="flex">
				<DxButton class="custom-button" :width="22" :height="22" v-if="isMaximized" type='default' styling-mode='outlined' icon='collapse' @click='toggleMaximize' />
				<DxButton class="custom-button" :width="22" :height="22" v-else type='default' styling-mode='outlined' icon='expandform' @click='toggleMaximize' />
			</div>
			<DxButton class="custom-button" :width="22" :height="22" type='default' styling-mode='outlined' icon='plus' @click="zoomContainer.decreaseZoom()" />
			<div style="text-align: center; align-self: center; font-size: x-small;">{{ (100 /currentZoom).toFixed()}}%</div>			
			<DxButton class="custom-button" type='default' :width="22" :height="22" styling-mode='outlined' icon='minus' @click="zoomContainer.increaseZoom()" />
		</div>

		<ZoomableContainer ref="zoomContainer" :hide-controls="true">
			
			<div v-if="!isConnected" style="display: flex; flex: auto; position: relative; justify-content: center; align-items: center;">
				<div class="card" style="z-index: 2; padding: 20px 40px; background-color: #31303A; width: max-content; height: auto; transform: scale(1); font-size: small; box-shadow: 2px 4px 7px 1px rgba(0,0,0,0.25);">
					<div style="border-bottom: 1px solid #515159; margin-bottom: 20px;">Connect to a station</div>

					<div style="margin-left: 3px;">Select station</div>
					<div style="display: flex; gap: 8px;">
						<DxSelectBox 
              style="border-radius: 5px; " 
              styling-mode="filled" 
              display-expr="order_start_station_name" 
							:data-source="orderStartStations.data" 
							v-model="selectedStation">
						</DxSelectBox>

						<DxButton 
              text="Connect" 
              @click="onStationConnect()" 
              style="border-radius: 5px;	height: 32px;" 
              styling-mode="contained" 
              type="default">
            </DxButton>
					</div>
				</div>
			</div>

			<!-- Connected view -->
			<div v-else class="container" style="position: relative;">
				<div class="row">
					<!-- station connect -->
					<div style="display:flex; flex-wrap: wrap; flex:0%;">
						<div style="flex: 1 1 100%; margin-left: 3px;">Station</div>
						<DxTextBox 
              style="border-radius: 5px 0 0 5px;" 
              :disabled="true" 
              styling-mode="filled" 
              :value="selectedStation?.order_start_station_name">
            </DxTextBox>
						<DxButton 
              v-if="isConnected" 
              text="Disconnect" 
              style="border-radius: 0 5px 5px 0;	height: 32px;" 
              styling-mode="contained" 
              type="default" 
							@click="onStationDisconnect()">
						</DxButton>
					</div>
					
					<!-- space fill -->
					<div style="flex: auto;"></div>

					<div style="display: flex; align-items: end; gap: 20px;">
						<div>
							<div>Queue Count</div>
							<span>In Queue: </span><span class="accent">{{ countsObj.containers_in_queue }}</span>
						</div>

						<!-- order stats -->
						<div>
							<div>Completed Orders</div>
							<div>
								<span>Today</span>: <span class="accent">{{countsObj.today_completed_count }}</span> | <span>Last Hour</span>: <span class="accent">{{countsObj.last_hour_completed_count}}</span>
							</div>
						</div>
					</div>
				</div>

				<!-- the queue -->
				<div style="flex: auto;" class="row">
					<div style="display: flex; flex: auto; flex-direction: column; justify-content: center; align-items: center; align-content: center;">
						<div style="display: flex; align-items: center; gap: 25px;">
							<!-- order cards -->
							<TransitionGroup name="card-slide" tag="div" style="display: flex; align-items: center; gap: 25px;">
								<div v-if="!orderQueue.length">No orders in queue with the current filters</div>
								<template v-else>
									<div v-for="order in orderQueue" :key="order?.lpn_barcode || Math.random()">
										<div v-if="order" class="card" :class="getCardStatus(order)"> 
											<div style="font-size: x-large; text-align: center;">{{ order.container_type }}</div> 
											<div style="font-size:x-small; text-align: center">{{ order.lpn_barcode }}</div>
											<!-- steps -->
											<div>
												<div style="text-align: center; margin-top: 10px;">Steps</div>
												<template v-if="order.is_tote">
													<div v-for="s in toteSteps" style="text-align: center;" :key="s">{{ s }}</div>
												</template>
												<template v-else>
													<div v-for="ss in boxSteps" style="text-align: center;" :key="ss">{{ ss }}</div>
												</template>
												
												<div style="text-align: center; font-size: xx-small; margin-top: 10px;" class="accent">{{ order.scanned_barcode? '#'+ order.scanned_barcode: order.scanned_barcode }}</div>
												<!-- `reprint action -->
												<div style="text-align: center; margin-top: 10px;">
													<dxButton 
                            v-if="order.lpn_barcode == currentOrder?.lpn_barcode && order.container_type != 'tote'" 
                            text="Reprint" 
                            styling-mode="contained" 
                            type="default" 
                            @click="onReprint(order)">
                          </dxButton>
												</div>
											</div>
										</div>
									</div>
								</template>
							</TransitionGroup>
						</div>

						<div style="display: flex; flex-direction: column; gap: 10px; margin-top: 20px; align-items: center;">
							<dxTextBox 
								ref="inputBarcode"
                placeholder="Scan or Enter Barcode" 
                styling-mode="filled" 
                style="width: 280px; height: 45px;"
								:onEnterKey="onBarcodeScanned"
								v-model="barcodeInput">
							</dxTextBox>
							<!-- connection status -->
							<div :style="{ display: 'flex', alignItems: 'center', gap: '8px' }">
								<div :style="{ width: '10px', height: '10px', borderRadius: '50%', backgroundColor: isServerConnected ? 'green' : 'red' }"></div>
								<span>Server {{ isServerConnected ? 'Connected' : 'Disconnected' }}</span>
							</div>
						</div>
					</div>
				</div>

				<!-- log / filters / queue table -->
				<div class="row">
					<!-- log -->
					<div style="flex: auto;">
						<div style="padding-left: 3px; padding-bottom: 2px;">Scanned Log</div>
						<div class="table-container">
							<table>
								<thead>
                  <tr>
                    <th>Time</th>
                    <th>Type</th>
                    <th>Container Barcode</th>
                    <th>Tote Barcode</th>
                  </tr>
                </thead>
								<tbody>
									<tr v-for="item in reversedScannedOrdersLog" :key="item.time">
										<td>{{ item.time}}</td>
										<td>{{ item.container }}</td>
										<td>{{ item.lpn_barcode }}</td>
										<td><span v-if="item.container === 'tote'">{{ item.scanned_barcode }}</span></td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>

					<!-- Filters -->
					<div style="margin: 0 20px; align-content: center;">
						<div style="padding-bottom: 2px; ">Active Filters</div>
						<div v-for="item in activeStationFilters" :key="item.condition_name">
							<span style="display: inline-block; width: 10px; height: 10px;border-radius: 50%; margin-right: 5px;" class="bg-accent"></span>
							<span>{{ item.condition_name }}: </span> 
							<span class="accent">{{ item.condition_values.join(", ")}}</span>
						</div>
					</div>
					
					<!-- queue tables-->
					<div style="flex: auto; display: flex; flex-direction: row; gap: 10px;">
						<!--  container queue -->
						<div style="flex: auto;">
							<div style="padding-left: 3px; padding-bottom: 2px;">Container Queue</div>
							<div class="table-container">
								<table>
									<thead>
                    <tr>
                      <th>Type</th>
                      <th>Filtered / All</th>
                    </tr>
                  </thead>
									<tbody>
										<tr v-for="item in countsObj.container_types_counts" :key="item.container_type">
											<td>{{ item.container_type}}</td>
											<td>{{ item.display_value }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>

						<!--  aisle queue -->
						<div style="flex: auto;">
							<div style="padding-left: 3px; padding-bottom: 2px;">Zone Queue</div>
							<div class="table-container">
								<table>
									<thead>
                    <tr>
                      <th>Zone</th>
                      <th>Count</th>
                    </tr>
                  </thead>
									<tbody>
										<tr v-for="item in countsObj.zone_counts || []" :key="item.start_zone">
											<td>{{ item.start_zone }}</td>
											<td>{{ item.display_value }}</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</ZoomableContainer>
	</div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
 	=====================================================================*/
  // Vue core
	import { ref, onMounted, computed, nextTick, onUnmounted, watch } from 'vue';
	
  // UI components & utilities
	import { DxSelectBox } from 'devextreme-vue';
	import { DxButton } from 'devextreme-vue/button';
	import { DxTextBox } from 'devextreme-vue/text-box';
	import notify from 'devextreme/ui/notify';
	
  // Child components
	import ZoomableContainer from '../components/zoomable-container.vue'
	import SkeletonLoader from '../components/skeleton-loader.vue'
	
  // Composables
	import { useOrderStart } from '@/composables/useOrderStart'
	import { getQueryParam } from '@/composables/urlUtils';
	import auth from '@/auth';
	import { useRouter, useRoute } from 'vue-router';
  /*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { 
		// WebSocket state
		isConnected,
		connectionStatus,
		messages,
		reconnect,
		disconnect,
		connect,
		// WebSocket commands
		sendProcessOrderCommand,
		sendConfirmContainerCommand,
		sendClientReadyCommand,
		sendEstablishMessage,
		sendConfirmToteCommand,
		// API methods
		fetchOrderStartStations,
		// Shared state
		orderStartStations,
		selectedStation,
		activeOrders,
    countsData,
    filtersData,
    lastCommandResponse,
    stationStatus,
		// Command-specific response tracking
		orderNotFoundResponses,
    lastOrderNotFoundResponse,
    lastRejectedConfirmContainerResponse,
    lastRejectedConfirmToteResponse,
		// Loading and error handling
		isLoading,
		isResetting,
		error,
		clearError
	} = useOrderStart();

	const router = useRouter();
	const route = useRoute();
	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/
	const zoomContainer = ref();
  const contentBlockRef = ref();
	const inputBarcode = ref(null)

  /*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	const isServerConnected = ref(true);
	const barcodeInput = ref('');
	const ogArray = ref([]);
	const isMaximized = ref(false);
	const isMaximizedFromUrl = ref(false);

	const notifyOptions = ref({
    position: { 
      my: "bottom", 
      at: "bottom", 
      of: ".content-block", 
      offset: { x: 0, y: 10 }
    }, 
    width: 'auto',
    animation: { 
      show: { type: "fade", duration: 800, from: 0, to: 1 }, 
      hide: { type: "fade", duration: 800, from: 1, to: 0 } 
    }
  });

	const scannedOrder = ref(null);
	const scannedOrdersLog = ref([]);
	const lastScannedBarcode = ref('');
	const MAX_LOG_SIZE = 30;
	
	const boxSteps = ['1 Make Box', '2 Apply Label', '3 Scan Label'];
	const toteSteps = ['1 Scan Tote'];

	const reversedScannedOrdersLog = computed(() => {
		return [...scannedOrdersLog.value].reverse();
	});


  /*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	onMounted(async() => {
		//Get all possible stations - call api /OrderStart/OrderStartStations
		await fetchOrderStartStations()

		// get station from local storage if available and set it as default
		const station = localStorage.getItem('Order-Station');

		const isValidJSON = str => { try { JSON.parse(str); return true; } catch { return false; }}

		if (station){
			if (isValidJSON(station)) {
				selectedStation.value = orderStartStations.value.data.find(s => s.id_kvkorderstartstation === JSON.parse(station).id_kvkorderstartstation);
			}
		}

		// override the station if it's passed as a query parameter
		const orderStartParam = getQueryParam('orderStart');
		if (orderStartParam) {
			console.log(orderStartStations.value.data)
			selectedStation.value = orderStartStations.value.data.find(s => s.order_start_station_name === orderStartParam) || selectedStation.value;
		}
		
		// Get URL query parameters
		isMaximizedFromUrl.value = getQueryParam('max') === 'true';
		
		// Set isMaximized based on URL parameter if it exists
		if (isMaximizedFromUrl.value) {
			isMaximized.value = true;
		}

		// Start the focus timer
		startFocusTimer();
		
		// Start the midnight disconnect timer
		startMidnightTimer();
	});

	onUnmounted(() => {
		// Clear all intervals when component is unmounted
		const interval_id = window.setInterval(function(){}, Number.MAX_SAFE_INTEGER);
		for (let i = 1; i < interval_id; i++) {
			window.clearInterval(i);
		}
	});


  /*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	const countsObj = computed(() => {
		return countsData.value || { zone_counts: [] };
	});
  
	const currentOrder = computed(() => {
		// Find the first order that hasn't been scanned yet from active orders
		return activeOrders.value?.[0] || null;
	});
  
	const orderQueue = computed(() => {
		const active = activeOrders.value || [];
		const scanned = scannedOrder.value ? [scannedOrder.value] : [];
		return [...scanned, ...active].filter(order => order && order.lpn_barcode);
	});
  
	const activeStationFilters = computed(() => {
		return filtersData.value.condition_filters;
	});

	const lastHourOrders = computed(() => {
		return 0;
	});

	const currentZoom = computed(() => {
		return zoomContainer.value?.zoomLevel || 1;
	});

	/*=====================================================================
    WATCHERS 
  =====================================================================*/
	// Monitor connection status changes
	watch(connectionStatus, (newStatus, oldStatus) => {
		// console.log(`WebSocket status changed from ${oldStatus} to ${newStatus}`);
		
		if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED') {
			notify({ ...notifyOptions.value, message: 'Connected to station ' + selectedStation.value.order_start_station_name, displayTime: 3000  });

			// Automatically send ESTABLISH and CLIENT-READY when connected
			sendEstablishMessage();
			sendClientReadyCommand();

			focusTextBox()
		} else if (newStatus === 'ERROR' && oldStatus !== 'ERROR') {
			notify({ 
        ...notifyOptions.value, 
        message: `Error! We're having trouble connecting to the live service.`, 
        type: 'error', 
        displayTime: 5000 
      });
		}
	});
	
	// Active Orders
	watch(activeOrders, (newOrders, oldOrders) => {
		if (isResetting.value) return;
		
		// console.log('Active orders changed', oldOrders);
		// Only process if we have orders and they've changed
		if (!oldOrders?.length) return;
		
		// Find the first order that was in oldOrders but not in newOrders
		const completedOrder = oldOrders.find(oldOrder => 
			oldOrder && oldOrder.lpn_barcode && 
			!newOrders?.some(newOrder => newOrder && newOrder.lpn_barcode === oldOrder.lpn_barcode)
		);

		if (completedOrder) {
			// Update scanned order first
			scannedOrder.value = {...completedOrder, scanned_barcode: lastScannedBarcode.value};

			// Then add to log
			const logEntry = { 
				time: new Date().toLocaleTimeString(),
				container: completedOrder.container_type, 
				lpn_barcode: completedOrder.lpn_barcode,
				scanned_barcode: lastScannedBarcode.value 
			};
			// console.log('Adding log entry:', logEntry);
			scannedOrdersLog.value.push(logEntry);
			
			// Keep only the last MAX_LOG_SIZE entries
			if (scannedOrdersLog.value.length > MAX_LOG_SIZE) {
				scannedOrdersLog.value = scannedOrdersLog.value.slice(-MAX_LOG_SIZE);
			}

			// Clear scanned order only after we've logged it
			if (!newOrders?.length) {
				scannedOrder.value = null;
			}
			
			// Clear the last scanned barcode after we're done with it
			lastScannedBarcode.value = '';
		}
	}, { deep: true });

	// Current Order
	watch(currentOrder, (newOrder, oldOrder) => {
		// Skip processing if we're in the middle of resetting state
		if (isResetting.value) return;
		
		// Process the new current order if it exists and is not a tote
		if (newOrder && !newOrder.is_tote) {
			// If there's no oldOrder (initial load) or the order has changed
			if (!oldOrder || newOrder.lpn_barcode !== oldOrder.lpn_barcode) {
				sendProcessOrderCommand(newOrder.lpn_barcode);
			}
		}
	});

	// Watch for order-not-found responses
	watch(lastOrderNotFoundResponse, (newResponse) => {
		// Skip processing if we're in the middle of resetting state
		if (isResetting.value) return;

		if (newResponse) {
			notify({  
        ...notifyOptions.value, 
        message: `Order not found. Print Failed!! ${newResponse.message || 'Unknown error'}`,  
        type: 'error',  
        displayTime: 8000  
      });
		}
	});

	watch(lastRejectedConfirmToteResponse, (newResponse) => {
		// Skip processing if we're in the middle of resetting state
		if (isResetting.value) return;

		if (newResponse) {
			// set the current order overwrite based on response can_overwrite
			currentOrder.value.overwrite = JSON.parse(newResponse.message).can_overwrite;
			const cmdMessage = JSON.parse(newResponse.message);
			const msg = cmdMessage.can_overwrite ? 
        cmdMessage.reject_message + ' - Overwrite by Scanning Again!' : 
        cmdMessage.reject_message;
        
			notify({  
        ...notifyOptions.value, 
        message: msg,  
        type: 'error',  
        displayTime: 8000
      });
		}
	});

	watch(lastRejectedConfirmContainerResponse, (newResponse) => {
		// Skip processing if we're in the middle of resetting state
		if (isResetting.value) return;

		if (newResponse) {
			const cmdMessage = JSON.parse(newResponse.message);
			notify({  
        ...notifyOptions.value, 
        message: cmdMessage.reject_message,  
        type: 'error',  
        displayTime: 5000  
      });
		}
	});

	// Watch scannedOrdersLog for changes
	watch(scannedOrdersLog, (newLog) => {
		console.log('Scanned orders log updated:', newLog);
	}, { deep: true });

  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
	const onStationConnect = async() => {
		if (!selectedStation.value) {
			notify({...notifyOptions.value, message: 'Please select a station!', type: 'error', displayTime: 1000 });
			return;
		}
    
		// Set the station in local storage for use as default
		localStorage.setItem('Order-Station', JSON.stringify(selectedStation.value));

		// connect websocket
		await connect();
	};

	const onStationDisconnect = () => {
		//disconnect socket and station
		disconnect();

		if (isMaximizedFromUrl.value){
			auth.logOut();
			router.push({
				path: "/login-form",
				query: { returnUrl: route.path + '?max=true&orderStart=' + selectedStation.value.order_start_station_name}
			});
		}
		// clear the station from local storage	
		//toast
		notify({...notifyOptions.value, message: 'Disconnected from station ' + selectedStation.value.order_start_station_name, type: 'success', displayTime: 2000 });
	};

	const getCardStatus = (order) => {
		if (!order) return {};
		
		return {
			'card-active': currentOrder.value && order.lpn_barcode === currentOrder.value.lpn_barcode,
			'card-previous': order.scanned_barcode != null,
			'card-next': order.scanned_barcode == null && 
				(currentOrder.value ? order.lpn_barcode !== currentOrder.value.lpn_barcode : true)
		};
	};

	const onReprint = (order) => {

		// Send reprint command
		sendProcessOrderCommand(order.lpn_barcode);

		notify({ 
      ...notifyOptions.value, 
      message: 'Reprinting label for order ' + order.lpn_barcode, 
      type: 'success', 
      displayTime: 2000 
    });
	};

	const onBarcodeScanned = async (e) => {
		if (e.event.key != 'Enter') return;
		if (!barcodeInput.value) return;

		const currentOrderValue = currentOrder.value;
		if (!currentOrderValue) return;

		// Store the barcode value
		const scannedBarcode = barcodeInput.value;
		lastScannedBarcode.value = scannedBarcode;
		
		try {
			// Send the appropriate command based on order type and wait for response
			if (currentOrderValue.is_tote) {
				await sendConfirmToteCommand({ 
					lpn_barcode: currentOrderValue.lpn_barcode, 
					tote_barcode: scannedBarcode, 
					overwrite: currentOrderValue.overwrite ?? false 
				});
			} else {
				await sendConfirmContainerCommand({ 
					lpn_barcode: currentOrderValue.lpn_barcode, 
					scanned_barcode: scannedBarcode 
				});
			}

			// Only clear input and refocus if command was successful
			barcodeInput.value = '';
			focusTextBox();
		} catch (error) {
			// Handle error - maybe show notification to user
			notify({ 
				...notifyOptions.value, 
				message: 'Failed to process barcode. Please try again.', 
				type: 'error', 
				displayTime: 3000 
			});
		}
	};

	const toggleMaximize = () => {
		isMaximized.value = !isMaximized.value;

		if (!isMaximized.value) {
			notifyOptions.value.position = { 
        my: "bottom", 
        at: "bottom", 
        of: ".content-block", 
        offset: { x: 0, y: 10 }
      };
		} else {
			notifyOptions.value.position = { 
        my: "bottom", 
        at: "bottom", 
        of: window, 
        offset: { x: 0, y: -10 }
      };
		}
	};

	const focusTextBox = async () => {
		// Wait for the next DOM update cycle
		await nextTick();
		// Check if the reference exists before trying to access instance
		if (inputBarcode.value && inputBarcode.value.instance) {
			inputBarcode.value.instance.reset()
			inputBarcode.value.instance.focus();
		}
	};

	const startFocusTimer = () => {
		setInterval(() => {
			focusTextBox();
		}, 3000); // 3000 ms = 3 seconds
	};

	const startMidnightTimer = () => {
		const checkMidnight = () => {
			const now = new Date();
			if (now.getHours() === 0 && now.getMinutes() === 0) {
				if (isConnected.value) {
					onStationDisconnect();
				}
			}
		};

		// Check every minute
		setInterval(checkMidnight, 60000); // 60000 ms = 1 minute
	};
</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		padding: 25px!important;
		&.maximized {
			position: fixed;
			top: 0;
			left: 0;
			width: 100vw;
			height: 100vh;
			z-index: 950;
			margin: 0;
		}
	}

	.container {
		display: flex;
		flex-direction: column;
		gap: 20px;
		flex: 1 1 auto;
		overflow-y: auto;
		letter-spacing: 0.3px;
	}

	.row {
		display: flex;
		gap: 20px;
		flex-shrink: 0; 
	}

	/* color classes */
	.accent {
		color: $base-accent;
	}
  
	.bg-accent {
		background-color: $base-accent;
	}

	/* cards */
	.card {
		position: relative;
		height: 280px;
		width: 220px;
		padding: 10px 20px;
		border-radius: 8px;
		align-content: center;
		background-color: rgba($base-bg-dark, 0.5);
		font-size: medium;
		letter-spacing: 2px;
		transition: transform .3s cubic-bezier(0.4, 0, 0.2, 1);

		&.card-active {
			border: 1px solid rgba($base-accent,0.6);
			transform: scale(1);
			background-color: $base-bg-dark;
			box-shadow: 0px 0px 21px -4px rgba(60,175,169,0.18);
			color: $base-text-color;
      
			&:before {
				content: 'Current';
				display: block;
				position: absolute;
				top: -30px;
				text-align: center;
				width: 100%;
				left: 0;
				font-size: smaller;
			}
		}

		&.card-previous {
			box-shadow: -2px 3px 7px 1px rgba(0,0,0,0.25);
			font-size: small;
			transform: scale(.8);
      
			&:before {
				content: 'Previous';
				display: block;
				position: absolute;
				top: -30px;
				text-align: center;
				width: 100%;
				left: 0;
			}
		}

		&.card-next {
			box-shadow: 2px 4px 7px 1px rgba(0,0,0,0.25);
			font-size: small;
			transform: scale(.8);
      
			&:before {
				content: 'Next';
				display: block;
				position: absolute;
				top: -30px;
				text-align: center;
				width: 100%;
				left: 0;
			}
		}
	}

	/* card transition classes */
	.card-slide-move {
		transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.card-slide-enter-active {
		transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
	}

	.card-slide-leave-active {
		transition: all .3s cubic-bezier(0.4, 0, 0.2, 1);
		position: absolute;
		pointer-events: none;  /* Add this to prevent interaction during exit */
	}

	.card-slide-enter-from {
		opacity: 0;
		transform: translateX(100px) scale(0.1);
	}

	.card-slide-leave-to {
		opacity: 0;
		transform: translateX(-30px) scale(0.1);
	}

	/* Table style */
	.table-container {
		height: 150px;
		overflow-y: auto;
		border-radius: 8px;
		background-color: rgba($base-bg-dark, 0.3);
	}

	table {
		border-collapse: collapse;
		width: 100%;
		position: sticky;
		top: 0;
		z-index: 1;
	}

	th, td {
		border: none;
		padding: 1px 12px;
		text-align: left;
	}

	th {
		background-color: $base-bg-dark;
	}

	th:first-of-type {
		border-radius: 8px 0 0 8px;
	}

	th:last-of-type {
		border-radius: 0 8px 8px 0;
	}

	/* DX Controls Override  */
	.dx-texteditor {
		border: 0px;
		min-width: 120px;
		border-radius: 5px;
		background-color: $base-bg-dark;
	}
  
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: $base-bg-dark;
		opacity: 1;
	}
  
	.dx-texteditor::before {
		content: none;
	}
  
	.dx-texteditor::after {
		content: none;
	}
	
	::v-deep(.dx-button) {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color;
	}

	.custom-button.dx-button-has-icon {
		min-width: unset !important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		width: unset;
		height: unset;
	}
</style>
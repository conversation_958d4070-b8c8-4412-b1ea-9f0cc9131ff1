<template>
  <div>
    <!-- <h2 class="content-block">Home</h2> -->
    <div class="content-block">
      <div class="dx-card responsive-paddings">
        <h2 style="text-align: center;">Welcome To SIM Software - KOZ</h2>
        <div style="text-align: center;"><img id="company-image" src='../assets/logo.png' alt="Company Logo"></div>
        <div id="kvk-container">
          <h3>
            <a href="https://www.systemsinmotioninc.com/" target="_blank">SIM Software</a> - Alsip, IL, USA
          </h3>
          <p id="kvk-container">Version {{ websiteVersion }}</p>
        </div>
        <!-- <div id="home-hidden-title">
          <div class="hide-btn">
            <DxButton
              class="home-refresh"
              :width="150"
              text="Refresh"
              type="success"
              icon="refresh"
              styling-mode="contained"
              @click="getData"
            />
          </div>
          <p class="home-time">
            {{convertDate(now)}}
          </p>
        </div> -->
        <DxButton
          class="home-device-btn"
          :width="150"
          :text="buttonTxt"
          type="default"
          styling-mode="contained"
          style="margin: 15px 0px 30px 0px;"
          @click="unhide"
        />
        <div v-if="hearbeatVis == true" id="hiddenDiv">
          <div id="home-hidden-title">
          <div class="hide-btn">
            <DxButton
              class="home-refresh"
              :width="150"
              text="Refresh"
              type="success"
              icon="refresh"
              styling-mode="contained"
              @click="getData"
            />
          </div>
          <p class="home-time">
            {{convertDate(now)}}
          </p>
        </div>
          <DxDataGrid
            class="home-grid"
            :data-source="dataSource"
            :show-borders="true"
            :show-column-headers="false"
            :column-auto-width="true"
            height="300"
          >
          <DxScrolling mode="virtual" />
          <DxGrouping :auto-expand-all="true" :allow-collapsing="false" />
            <DxColumn
              data-field="displayName1"
              cell-template="colorful1"
            />
            <DxColumn
              data-field="displayName2"
              cell-template="colorful2"
            />
            <DxColumn
              data-field="displayName3"
              cell-template="colorful3"
            />
            <DxColumn
              data-field="group"
              :group-index="0"
            />
            <template #colorful1="{data}">
              <div class="groupContainer">
                <div :class="getColor1({data})"></div>
                <div>{{data.text}}</div>
              </div>
            </template>
            <template #colorful2="{data}">
              <div class="groupContainer">
                <div :class="getColor2({data})"></div>
                <div>{{data.text}}</div>
              </div>
            </template>
            <template #colorful3="{data}">
              <div class="groupContainer">
                <div :class="getColor3({data})"></div>
                <div>{{data.text}}</div>
              </div>
            </template>
          </DxDataGrid>
          <!-- <div style="text-align: center;">Alarms</div>
          <DxDataGrid
            class="home-grid"
            :data-source="alarmDS"
            :show-borders="true"
            :show-column-headers="false"
            :column-auto-width="true"
            :column-hiding-enabled="true"
            width="90%"
          >
            <DxColumn
              data-field="alarmdate"
              data-type="datetime"
              :hiding-priority="0"
              name="homeAlarmFirst"
            />
            <DxColumn 
              data-field="alarmdate"
              caption="Seconds.Milliseconds"
              v-model:visible="isProgrammerLevel"
              name="homeAlarmSecond"
              :hiding-priority="1"
              cell-template="isPro"
            />
              <template #isPro="{data}">
                <div>{{ homeAlarmSecsAndMilli(data.text) }}</div>
              </template>
            <DxColumn
              data-field="kvkalarmmessage"
              :hiding-priority="2"
              />
              COMMENT OUT CELL-TEMPLATE AND TEMPLATE BELOW IF ALARM GRID VIEWABLE
              cell-template="alarm-status"
              <template #alarm-status="{data}">
                <div class="groupContainer">
                  <div :class="getColorAlarm({data})"></div>
                  <div>{{data.text}}</div>
                </div>
              </template>
          </DxDataGrid> -->
          <!-- <div style="display: flex; justify-content: center;">
            <div style="display: flex; align-items: center; margin-right: 10px;"><div class="homeGreenAlarm"></div>Green = Alarm Condition End</div>
            <div style="display: flex; align-items: center;"><div class="homeRedAlarm"></div> Red = Alarm Condition Begin</div>
          </div> -->
        </div>
      </div>
      <Footer></Footer>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import databaseName from '../myFunctions/databaseName';
import scannerParser from '../myFunctions/scannerParser';
import DxButton from 'devextreme-vue/button';
import axios from 'axios';
import auth from '../auth';
import notify from 'devextreme/ui/notify';
import Footer from '../components/app-footer.vue';
import { 
  DxDataGrid,
  DxColumn,
  DxScrolling,
  DxGrouping } from 'devextreme-vue/data-grid';

  const isProgrammerLevel = ref(true);

auth.getUser().then(user=>{
  // console.log(user);
    if(user.data.userSecurity === 'programmer'){
      isProgrammerLevel.value = true;
    } else {
      isProgrammerLevel.value = false;
    }
  }).catch(error=>{
    // loadingVisible.value = false;
    //console.log(error.response);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    } else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
});

const isMobile = ref(false);
const websiteVersion = ref("loading...");
// const databaseInfo = ref(Object);
const buttonTxt = ref("Show Devices");
const hearbeatVis = ref(false);
const now = ref(new Date());
const dataSource = ref([]);
const alarmDS = ref([]);

const getData = () => {
  now.value = new Date();
  axios({
    method: 'GET',
    url: 'api/Admin/HomeScreen'
  }).then(resp=>{
    if(resp.status === 200){
      // console.log(resp);
      dataSource.value = scannerParser.formatHeartbeats(resp.data.data.heartbeatData, isMobile.value);
      alarmDS.value = resp.data.data.alarmData;
      //notify(resp.data.message, 'success', 4000);
    }
    else{
      dataSource.value = [];
      alarmDS.value = [];
      notify(resp.data.message, 'error', 10000);
    }
  }).catch(error=>{
    // this.loadingVisible = false;
    console.log(error);
    if(error.response){
      alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else if(error.request){
      alert('Request failed with status code: ' + error.response.status + '\nMake sure that you and the server have not lost connection');
      return;
    }
    else{
      alert('Unknown error\nMake sure that you and the server have not lost connection');
      return;
    }
  });
};

websiteVersion.value = databaseName.getWebsiteVersion();
// databaseInfo.value = databaseName.getPaths();
databaseName.checkWebsiteVersion();
isMobile.value = databaseName.getScreenSizeSmall();
getData();

const getColor1 = (data) => {
  if(data.data.data.active1 === 'inactive'){return 'disabled-home'}
  if(data.data.data.status1 === "off"){
    return 'off-home';
  }else if(data.data.data.status1 === "on"){
    return 'on-home';
  } else {
    return;
  }
};
const getColor2 = (data) => {
  if(data.data.data.active2 === 'inactive'){return 'disabled-home'}
  if(data.data.data.status2 === "off"){
    return 'off-home';
  }else if(data.data.data.status2 === "on"){
    return 'on-home';
  } else {
    return;
  }
};
const getColor3 = (data) => {
  if(data.data.data.active3 === 'inactive'){return 'disabled-home'}
  if(data.data.data.status3 === "off"){
    return 'off-home';
  }else if(data.data.data.status3 === "on"){
    return 'on-home';
  } else {
    return;
  }
};
const unhide = () => {
  if(buttonTxt.value === 'Show Devices'){
    buttonTxt.value = 'Hide Devices';
    hearbeatVis.value = true;
  }
  else{
    buttonTxt.value = 'Show Devices';
    hearbeatVis.value = false;
  }
};
// const getColorAlarm = (data) => {
//   let temp = data.data.data;
//   if(temp.alarmdevicenote === 'START' || temp.alarmdevicenote === 'power off'){
//     return 'off-home';
//   }
//   else{
//     return 'on-home';
//   }
// };

// UNCOMMENT SECS AND MILLI FUNCTION IF ALARM GRID USED!!!!
// const homeAlarmSecsAndMilli = (e) => {
//   let tempArray = e.split(':');
//   return tempArray[tempArray.length - 1];
// };
// const convertDateShort = (date) => {
//   let month = date.getMonth() + 1;
//   let day = date.getDate();
//   let year = date.getFullYear();

//   let monthString = month.toString();

//   if(monthString.length == 1){
//       monthString = "0" + monthString;
//   }

//   let dayString = day.toString();

//   if(dayString.length == 1){
//       dayString = "0" + dayString;
//   }

//   return year+"-"+monthString+"-"+dayString;
// };
const convertDate = (date) => {
  let dd = 'AM';
  let month = date.getMonth() + 1;
  let day = date.getDate();
  let year = date.getFullYear();

  let monthString = month.toString();

  let dayString = day.toString();

  let hour = date.getHours();
  if(hour == 12){
    dd = 'PM';
  }
  else if(hour >= 12){
    hour = hour - 12;
    dd = 'PM';
  }
  let hourString = hour.toString();
  if(hourString.length == 1){
      hourString = '0' + hourString;
    }

  let minutes = date.getMinutes();
  let minutesString = minutes.toString();
  if(minutesString.length == 1){
      minutesString = '0' + minutesString;
    }

  let seconds = date.getSeconds();
  let secondsString = seconds.toString();
  if(secondsString.length == 1){
      secondsString = '0' + secondsString;
    }

  let realDate = monthString+"/"+dayString+"/"+year+" "+hourString+":"+minutesString+":"+secondsString+" "+dd;
  return realDate;
};
</script>

<style lang="scss">
#kvk-container{
  text-align: center;
}
#company-image{
  width: 40%;
  margin-bottom: 0px;
  padding-top: 3%;
  padding-bottom: 3%;
}
#hiddenDiv{
  position: relative;
  margin: auto;
  width: 100%;
}
.home-device-btn{
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 20px;
}
.home-grid{
  position: relative;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 20px;
}
.home-refresh{
  left: 0;
  right: 0;
  margin: auto;
  margin-bottom: 20px;
  float: left;
  margin-right: 20px;
}
.hide-btn{
  width: 100%;
}
#home-hidden-title{
  width: 320px;
  padding-bottom: 50px;
  margin: auto;
  right: 0;
  left: 0;
}
.home-time{
  float: left;
}
.disabled-home{
  background-color: rgb(197, 197, 197);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.on-home{
  background-color: #90EE90;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
.off-home{
  background-color: #FF0000;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 10px;
}
#hiddenDiv{
  position: relative;
  margin: auto;
  width: 100%;
}
.groupContainer{
  display: flex;
  align-items: center;
}
// .homeGreenAlarm {
//   height: 15px;
//   width: 15px;
//   background-color: #90EE90;
//   margin-right: 5px;
// }
// .homeRedAlarm {
//   height: 15px;
//   width: 15px;
//   background-color: #FF0000;
//   margin-right: 5px;
// }
</style>
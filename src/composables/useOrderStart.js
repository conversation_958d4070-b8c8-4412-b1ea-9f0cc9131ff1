import { computed, ref, watch, nextTick } from 'vue';
import { useWebSocket } from './useWebSocket';
import { useRestAPI } from './useRestAPI';
import databaseName from '../myFunctions/databaseName';
// import { useAuth } from './useAuth';
import auth from '../auth'

export function useOrderStart(options = {}) {
  
  // Message base for WebSocket
	const msgBase = databaseName.getWebsocketMessageBase();

  // Initialize the base WebSocket functionality
  const websocket = useWebSocket(options)
  const { 
    isConnected, 
    connectionStatus, 
    messages, 
    sendMessage, 
    reconnect,
    disconnect,
    clearMessages
  } = websocket

  // Initialize the REST API functionality
  const api = useRestAPI()

  // =========================================
  // Loading & Error Handling
  // =========================================
  
  // Single loading state for all API operations
  const isLoading = ref(false)
  
  // Single error state for all API operations
  const error = ref(null)
  
  // Clear any existing error
  const clearError = () => {
    error.value = null
  }
  
  // Helper to wrap API calls with loading and error handling
  const loadingAndErrorWrapper = async (operationName, callback) => {
    // Clear previous error
    clearError()
    // Set loading state
    isLoading.value = true 
    try {
      // Execute the callback
      const result = await callback()
      return result
    }catch (err) {
      // Capture and store error with the operation name
      error.value = { operation: operationName,message: err.message || `Error during ${operationName}`,timestamp: new Date() }
      console.error(`Error during ${operationName}:`, err)
      throw err
    } finally {
      // Clear loading state
      isLoading.value = false
    }
  }
  
  // =========================================
  // Shared State
  // =========================================
  const activeOrders = ref([]);
  const countsData = ref(null);
  const filtersData = ref([]);
  const stationStatus = ref([]);
  const orderStartStations = ref([])
  const selectedStation = ref({})
  const commandResponses = ref([]);
  const lastCommandResponse = ref(null);

  // To stop watchers from reacting to the reset
  const isResetting = ref(false);

  // Command-specific response tracking
  const orderNotFoundResponses = ref([]);
  const rejectedConfirmContainerResponses = ref([]);
  const rejectedConfirmToteResponses = ref([]);
  
  // Maximum number of responses to keep
  const MAX_RESPONSES = 50;
  
  // Helper function to limit array size
  const limitArraySize = (array, maxSize) => {
    if (array.length > maxSize) {
      return array.slice(-maxSize);
    }
    return array;
  };
  
  // Add lookup for the last response of each type
  const lastOrderNotFoundResponse = computed(() => {
    return orderNotFoundResponses.value.length > 0 ? orderNotFoundResponses.value[orderNotFoundResponses.value.length - 1] : null;
  });
  
  const lastRejectedConfirmContainerResponse = computed(() => {
    return rejectedConfirmContainerResponses.value.length > 0 ? rejectedConfirmContainerResponses.value[rejectedConfirmContainerResponses.value.length - 1] : null;
  });
  
  const lastRejectedConfirmToteResponse = computed(() => {
    return rejectedConfirmToteResponses.value.length > 0 ? rejectedConfirmToteResponses.value[rejectedConfirmToteResponses.value.length - 1] : null;
  });

  // Store the disconnect function for cleanup
  let disconnectFn = null

  // Function to reset all state
  const resetState = () => {
    isResetting.value = true;
    activeOrders.value = [];
    countsData.value = null;
    filtersData.value = [];
    stationStatus.value = { isActive: false, details: null };
    commandResponses.value = [];
    lastCommandResponse.value = null;
    // Reset command-specific response arrays
    orderNotFoundResponses.value = [];
    rejectedConfirmContainerResponses.value = [];
    rejectedConfirmToteResponses.value = [];

    // Use nextTick to ensure Vue has processed the state changes
    nextTick(() => {
      isResetting.value = false;
    });
    
  };
  
  // Watch for connection status changes to trigger cleanup
  watch(connectionStatus, (newStatus, oldStatus) => {
    // If going from any connected state to DISCONNECTED
    if (newStatus === 'DISCONNECTED' && 
        (oldStatus === 'CONNECTED' || oldStatus === 'RECONNECTING')) {
      // console.log('Connection lost, cleaning up state');
      resetState();
    }
  });

  // Also reset on explicit disconnect
  const originalDisconnect = websocket.disconnect;
  const enhancedDisconnect = () => {
    resetState(); // Reset state first
    return originalDisconnect(); 
  };
  
  // Helper function to safely parse potentially double-encoded JSON
  const safeJsonParse = (jsonString) => {
    try {
      // console.log('jsonString:',jsonString)
      // First parse attempt
      const parsed = JSON.parse(jsonString);
      
      // If the result is a string and looks like JSON, parse it again
      if (typeof parsed === 'string' && 
        (parsed.startsWith('[') || parsed.startsWith('{'))) {
        try {
          return JSON.parse(parsed);
        } catch (innerError) {
          // console.warn('Second JSON parse failed, using first result', innerError);
          return parsed;
        }
      }
      
      // If it's not a string or doesn't look like JSON, return the first parse
      return parsed;
    } catch (error) {
      // console.error('JSON parsing failed:', error);
      return false // Re-throw to let caller handle it
    }
  };

  // =========================================
  // WebSocket message commands
  // =========================================
   
  // Send ESTABLISH message
  const sendEstablishMessage = () => {
    const user =  auth._user
    const msgObj = {
      msgType: "ESTABLISH",
      msgFromService: selectedStation.value.order_start_station_name,
      msgFromUser: "",
      EstablishConnObj: {
        messageGroup: selectedStation.value.order_start_station_name,
        userIdName: user.userIdName,
        userSecurity: user.userSecurity,
        message: ""
      }
    }
   // console.log('SENDING ESTABLISH MESSAGE', msgObj)
    return sendMessage({...msgBase.value,...msgObj})
  }

  // Send CLIENT-READY command
  const sendClientReadyCommand = () => {
    //console.log('SENDING CLIENT-READY COMMAND')
    const msgObj = {
      msgType: "COMMAND",
      msgFromService: selectedStation.value.order_start_station_name,
      msgFromUser: "",
      CommandObj: {
        cmdName: "CLIENT-READY",
        cmdMessage: ""
      }
    }
    
		return sendMessage({...msgBase.value,...msgObj})
  }

  // Send PROCESS-ORDER command
  const sendProcessOrderCommand = (lpn_barcode) => {
    const msgObj = {
      msgType: "COMMAND",
      msgFromService: selectedStation.value.order_start_station_name,
      msgToUser: "",
      msgFromUser: "",
      CommandObj: {
        cmdName: "PROCESS-ORDER",
        cmdMessage: lpn_barcode
      }
    }
    
		return sendMessage({...msgBase.value,...msgObj})
  }

    // Send PROCESS-ORDER command
    const sendConfirmContainerCommand = (obj) => {
      const msgObj = {
        msgType: "COMMAND",
        msgFromService: selectedStation.value.order_start_station_name,
        msgToUser: "",
        msgFromUser: "",
        CommandObj: {
          cmdName: "CONFIRM-CONTAINER",
          cmdMessage: JSON.stringify(obj)
        }
      }
      // console.log('SENDING CONFIRM-CONTAINER COMMAND', msgObj)
      
      return sendMessage({...msgBase.value,...msgObj})
    }

    // Send PROCESS-ORDER command
    const sendConfirmToteCommand = (obj) => {
      const msgObj = {
        msgType: "COMMAND",
        msgFromService: selectedStation.value.order_start_station_name,
        msgToUser: "",
        msgFromUser: "",
        CommandObj: {
          cmdName: "CONFIRM-TOTE",
          cmdMessage: JSON.stringify(obj)
        }
      }
      //console.log('SENDING CONFIRM-TOTE COMMAND', msgObj)
      
      return sendMessage({...msgBase.value,...msgObj})
    }

    // connect and send establish message immediately
    const initialize = () => {
      if (isConnected.value) {
        sendEstablishMessage()
      } else {
        reconnect()
      }
    }

  // // Helper computed properties for specific message types
  // const clientReadyMessages = computed(() => 
  //   messages.filter(msg => 
  //     msg.msgType === 'COMMAND' && 
  //     msg.CommandObj?.cmdName === 'CLIENT-READY'
  //   )
  // )

  // const processOrderMessages = computed(() => 
  //   messages.filter(msg => 
  //     msg.msgType === 'COMMAND' && 
  //     msg.CommandObj?.cmdName === 'PROCESS-ORDER'
  //   )
  // )

  // =========================================
  // REST API functions
  // =========================================
  /**
   * Fetch Order Start Stations from the Rest API
   * 
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} - The list of stations
   */
  const fetchOrderStartStations = async (params = {}) => {
    return loadingAndErrorWrapper('Fetch Stations', async () => {
      const data = await api.get('/OrderStart/OrderStartStations', params)
      orderStartStations.value = data
      return data
    })
  }

 

  // =========================================
  // WEB-SOCKET: PROCESS MESSAGES
  // =========================================
  watch(() => websocket.messages, (messages) => {
    if (messages.length === 0) return;
    
    const msg = messages[messages.length - 1];
    //console.log('New message received:', msg);
    
    // Handle different command types
    if (msg.CommandObj) {
      const cmdName = msg.CommandObj.cmdName;
      const cmdMessage = msg.CommandObj.cmdMessage;
      
      // Create a base response object
      const response = {
        command: cmdName,
        message: cmdMessage,
        timestamp: new Date(),
        success: true,
        raw: msg
      };
      
      // Process specific commands
      switch(cmdName) {
        case "ACTIVE-ORDERS":
          try {
            
            const ordersData = safeJsonParse(cmdMessage);
            activeOrders.value = ordersData;
            response.data = ordersData;
          } catch (error) {
            console.error('Error parsing orders data:', error);
            response.success = false;
            response.error = error.message;
          }
          break;
          
        case "COUNTS-UPDATE":
          try {
            const countsInfo = safeJsonParse(cmdMessage);

            countsData.value = countsInfo;
            response.data = countsInfo;
          } catch (error) {
            console.error('Error parsing counts data:', error);
            response.success = false;
            response.error = error.message;
          }
          break;
          
        case "FILTERS-UPDATE":
          try {
            const filtersInfo = safeJsonParse(cmdMessage);
            filtersData.value = filtersInfo;
            response.data = filtersInfo;
          } catch (error) {
            console.error('Error parsing filters data:', error);
            response.success = false;
            response.error = error.message;
          }
          break;
          
        case "STATION-ACTIVE":
          // Simply mark the station as active - no need to parse any data
          stationStatus.value = {
            isActive: true,
            timestamp: new Date()
          };
          response.data = { active: true };
          break;

        case "ORDER-NOT-FOUND":
          try {
            if (cmdMessage) {
              response.data = safeJsonParse(cmdMessage);
            }
            orderNotFoundResponses.value.push(response);
          } catch (error) {
            console.error('Error parsing ORDER-NOT-FOUND data:', error);
            response.success = false;
            response.error = error.message;
            orderNotFoundResponses.value.push(response);
          }
          break;
            
        case "REJECTED-CONFIRM-CONTAINER":
          try {
            if (cmdMessage) {
              response.data = safeJsonParse(cmdMessage);
            }
            rejectedConfirmContainerResponses.value.push(response);
          } catch (error) {
            console.error('Error parsing REJECTED-CONFIRM-CONTAINER data:', error);
            response.success = false;
            response.error = error.message;
            rejectedConfirmContainerResponses.value.push(response);
          }
          break;
          
        case "REJECTED-CONFIRM-TOTE":
          try {
            if (cmdMessage) {
              response.data = safeJsonParse(cmdMessage);
            }
            rejectedConfirmToteResponses.value.push(response);
          } catch (error) {
            console.error('Error parsing REJECTED-CONFIRM-TOTE data:', error);
            response.success = false;
            response.error = error.message;
            rejectedConfirmToteResponses.value.push(response);
          }
          break;  
        case "STATION-INACTIVE": 
            
            stationStatus.value = {
            isActive: true,
            timestamp: new Date()
          };
          response.data = { active: true };
          break;

        default:
         // For any unhandled command, try parsing the message if it exists
         if (cmdMessage) {
          try {
            response.data = safeJsonParse(cmdMessage);
          } catch (error) {
            console.warn(`Unhandled command ${cmdName} with unparseable message`, error);
          }
        }
      }
      
      // Store the command response
      lastCommandResponse.value = response;
      commandResponses.value = limitArraySize([...commandResponses.value, response], MAX_RESPONSES);
      
      // Limit specific response arrays
      if (cmdName === "ORDER-NOT-FOUND") {
        orderNotFoundResponses.value = limitArraySize(orderNotFoundResponses.value, MAX_RESPONSES);
      } else if (cmdName === "REJECTED-CONFIRM-CONTAINER") {
        rejectedConfirmContainerResponses.value = limitArraySize(rejectedConfirmContainerResponses.value, MAX_RESPONSES);
      } else if (cmdName === "REJECTED-CONFIRM-TOTE") {
        rejectedConfirmToteResponses.value = limitArraySize(rejectedConfirmToteResponses.value, MAX_RESPONSES);
      }
    }
  }, { deep: true });
  

  return {
    // Re-export base WebSocket functionality
    ...websocket,
    
    //Send commands
    sendEstablishMessage,
    sendClientReadyCommand,
    sendProcessOrderCommand,
    sendConfirmContainerCommand,
    sendConfirmToteCommand,
    
    // Filtered messages
    // clientReadyMessages,
    // processOrderMessages,

    // REST API methods
    fetchOrderStartStations,

    // Shared state
    orderStartStations,
    selectedStation,
    activeOrders,
    countsData,
    filtersData,
    lastCommandResponse,
    stationStatus,

    // Command-specific response tracking
    orderNotFoundResponses,
    rejectedConfirmContainerResponses,
    rejectedConfirmToteResponses,
    lastOrderNotFoundResponse,
    lastRejectedConfirmContainerResponse,
    lastRejectedConfirmToteResponse,

    // loading and error states
    isLoading,
    isResetting,
    error,
    clearError,
    
    // Initialization helper
    initialize
  }
}
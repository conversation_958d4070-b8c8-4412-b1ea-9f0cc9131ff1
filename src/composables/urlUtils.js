// utils/urlUtils.js

/**
 * Get URL search parameters, handling both hash and non-hash routes
 * Works for both history mode and hash mode routing
 * @returns {URLSearchParams}
 */
export const getUrlSearchParams = () => {
  // For hash-based routing (#/route?param=value)
  if (window.location.hash && window.location.hash.includes('?')) {
    const hashParts = window.location.hash.split('?');
    if (hashParts.length > 1) {
      return new URLSearchParams(hashParts[1]);
    }
  }
  
  // For history mode routing (/route?param=value)
  return new URLSearchParams(window.location.search);
};

/**
 * Get a specific query parameter value from the URL
 * 
 * @param {string} paramName - The name of the parameter to retrieve
 * @param {string} [defaultValue=null] - The default value to return if parameter doesn't exist
 * @return {string|null} The parameter value or defaultValue if not found
 */
export const getQueryParam = (paramName, defaultValue = null) => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return defaultValue;
  }
  
  // Get the current URL's query string, handling hash-based routing
  const urlParams = getUrlSearchParams();
  
  // Return the parameter value if it exists, otherwise return the default value
  return urlParams.has(paramName) ? urlParams.get(paramName) : defaultValue;
};

/**
 * Get all query parameters as an object
 * 
 * @return {Object} An object containing all query parameters
 */
export const getAllQueryParams = () => {
  // Check if we're in a browser environment
  if (typeof window === 'undefined') {
    return {};
  }
  
  const urlParams = getUrlSearchParams();
  const params = {};
  
  // Convert URLSearchParams to a plain object
  urlParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
};


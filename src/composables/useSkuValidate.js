import { computed, ref, watch, nextTick } from 'vue'
import { useWebSocket } from './useWebSocket'
import { useRestAPI } from './useRestAPI'
import databaseName from '../myFunctions/databaseName'
import auth from '../auth'

export function useSkuValidate(options = {}) {
  // State
	const lastAPIError = ref([]);
	const lastAPIErrorData = ref([])
	const lastAPISuccessData = ref([]);
	const selectedStation = ref({})
	const stations = ref(['PackStation01','PackStation02','PackStation03','PackStation04','PackStation05','PackStation06', 'PackStation07', 'PackStation08', 'PackStation09', 'PackStation10', 'PackStation11', 'PackStation12', 'PackStation13', 'PackStationMobile01','PackStationMobile02','PackStationMobile03','PackStationMobile04'])
	const isAdminOverride = ref(false)
	const order = ref({})
	const textBoxBarcode = ref('');

	// Message base for WebSocket
	const msgBase = databaseName.getWebsocketMessageBase();

	// Initialize the base WebSocket functionality
	const websocket = useWebSocket(options)
	const { 
		connect,
		isConnected, 
		connectionStatus, 
		messages, 
		sendMessage, 
		reconnect,
		disconnect,
		clearMessages
	} = websocket

	// Initialize the REST API functionality
	const api = useRestAPI()

	// =========================================
	// Loading & Error Handling
	// =========================================
	
	// Single loading state for all API operations
	const isLoading = ref(false)
	
	// Single error state for all API operations
	const error = ref(null)
	
	// Clear any existing error
	const clearError = () => {
		error.value = null
	}
	

	// Watch for connection status changes to trigger cleanup
	watch(connectionStatus, (newStatus, oldStatus) => {
		// If going from any connected state to DISCONNECTED
		if (newStatus === 'DISCONNECTED' && 
				(oldStatus === 'CONNECTED' || oldStatus === 'RECONNECTING')) {
			console.log('Connection lost, cleaning up state');
			// resetState();
		}
	});


	// Helper function to safely parse potentially double-encoded JSON
  const safeJsonParse = (jsonString) => {
    try {
      // First parse attempt
      const parsed = JSON.parse(jsonString);
      
      // If the result is a string and looks like JSON, parse it again
      if (typeof parsed === 'string' && 
        (parsed.startsWith('[') || parsed.startsWith('{'))) {
        try {
          return JSON.parse(parsed);
        } catch (innerError) {
          console.warn('Second JSON parse failed, using first result', innerError);
          return parsed;
        }
      }
      
      // If it's not a string or doesn't look like JSON, return the first parse
      return parsed;
    } catch (error) {
      console.error('JSON parsing failed:', error);
      throw error; // Re-throw to let caller handle it
    }
  };


	// =========================================
	// WebSocket message commands
	// =========================================
	// Send ESTABLISH message
	const sendEstablishMessage = () => {
		const user =  auth._user
		const msgObj = {
			msgType: "ESTABLISH",
			msgFromService: selectedStation.value,
			msgFromUser: "",
			EstablishConnObj: {
				messageGroup: selectedStation.value,
				userIdName: user.userIdName,
				userSecurity: user.userSecurity,
				message: ""
			}
		}
		// console.log('SENDING ESTABLISH MESSAGE', msgObj)
		return sendMessage({...msgBase.value,...msgObj})
	}

	// connect and send establish message immediately
	const initialize = () => {
		if (isConnected.value) {
			sendEstablishMessage()
		} else {
			// We can connect manually and handle the establish message in a watcher
			reconnect()
		}
	}


	// =========================================
	// REST API functions
	// =========================================

	// API response handler - standardizes error handling
	const handleApiResponse = async (apiCall) => {
		isLoading.value = true;
		try {
			const result = await apiCall();
			return { success: true, message: result.message, data: result.data };
		} catch (error) {
			console.error('API Error:', error);
			lastAPIError.value = error.response?.data?.message || 'Unknown error occurred';
			lastAPIErrorData.value = error.response?.data || []

			console.log('handleApiResponse', error)

			return { success: false, error: lastAPIError.value, message: error.response?.data?.message };
		} finally {
			isLoading.value = false;
		}
	};

	//Fetch order data
	const fetchOrder = async (_barcode) => {
		textBoxBarcode.value = _barcode;
		return handleApiResponse(async () => {
			const response = await api.get(`/PackStation/StartValidation/${selectedStation.value}`, { barcode: _barcode })
			order.value = response.data || {}
			return response
		})
	}

	//TYLER;
	//Send Complete Container
	const completeContainer = async (lpn_barcode) => {
		return handleApiResponse(async () => {
			// i think the root cause stemes from this where maybe something happened with JS order of execution
			//or something else happened that 'cleared' out the value in the text box so when it tried to complete the container it failed
			//BUT still closed the popup
			const response = await api.post(`/PackStation/CompleteContainer?barcode=${lpn_barcode}`)

			return response;
		});
	}

	//TYLER;
	//Send Reprint Request
	const reprintAllLabels = async () => {
		return handleApiResponse(async () => {
			const response = await api.post(`/PackStation/RePrintALL/${selectedStation.value}?barcode=${order.value.lpn_barcode }`)

			return response;
		});
	}

	//Send for validation
	const validateOrder = async () => {

		const body = order.value.lpn_lines.map(item => ({
				line_id: item.line_id,
				validated_qty: item.qty_validated || 0
		}));


		console.log('isAdminOverride:', isAdminOverride.value)
		let adminToken = isAdminOverride.value ? await auth.getAdminToken() : null
		let response = null
		console.log('adminToken:', adminToken)
		return handleApiResponse(async () => {
			if (!adminToken){
				response = await api.post(`/PackStation/SubmitValidation/${selectedStation.value}?barcode=${order.value.lpn_barcode }`, body)
			}else {
				response = await api.withToken(adminToken).post(`/PackStation/SubmitValidation/${selectedStation.value}?barcode=${order.value.lpn_barcode }&admin_overwrite=true`, body)
			}

			// clear the override right away
			if (isAdminOverride.value) {
				clearAdminOverride();
			}

			return response
		})

	}

	// Add cleanup when admin override is done
	const clearAdminOverride = () => {
		isAdminOverride.value = false;
		auth.clearAdminVerification();
	};

	return {
		// Re-export base WebSocket functionality
		...websocket,

		//Send commands
		sendEstablishMessage,

		// REST API methods
		fetchOrder,
		validateOrder,
		completeContainer,
		reprintAllLabels,

		// Shared state
		selectedStation,
		stations,
		isAdminOverride,
		order,

		// loading and error states
		isLoading,
		lastAPIError,
		lastAPIErrorData,
		clearError,

		// Initialization helper
		initialize
	}
}
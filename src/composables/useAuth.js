// useAuth.js
import axios from 'axios';
import databaseName from '../myFunctions/databaseName';
import { ref, reactive, readonly } from 'vue';

export default function useAuth() {
  const defaultUser = {
    userEmail: 'view only',
    userIdNum: 'view only',
    userFirst: 'View',
    userLast: 'Only',
    userPass: 'password',
    userIdName: 'view only',
    userSecurity: "standard",
    userJwtToken: "1",
    avatarUrl: '',
    isAuthenticated: false 
  };

  // Use reactive for the user state
  const _user = reactive({...defaultUser, isAuthenticated: false});
  // Use ref for tempAdmin since it's either null or an object
  const _tempAdmin = ref(null);

  // Create a readonly wrapper to expose the user state without allowing external modifications
  const user = readonly(_user);

  const hasPermission = (userSecurity = []) => {
    // Skip permission check if no roles required
    if (userSecurity.length === 0) return true;
    
    // Check if user has one of the required roles
    return userSecurity.includes(_user.userSecurity);
  };

  const loggedIn = (userSecurity = []) => {
    // First check if user is authenticated at all
    if (!_user.isAuthenticated) return false;
    
    // If no specific roles required, any authenticated user is fine
    if (userSecurity.length === 0) {
      return true;
    }
    
    // Otherwise check if user has one of the required roles
    return hasPermission(userSecurity);
  };

  const defaultLogIn = () => {
    Object.assign(_user, {...defaultUser, userSecurity: "view only", isAuthenticated: true});
    console.log("Default login, setting isAuthenticated to false:", _user);
    return _user;
  };

  const logIn = async (email, password) => {
    try {
      let temp = email.split("@");
      // Determine login method (email or username)
      const loginMethod = typeof temp[1] !== 'undefined' ? "email" : "username";
      
      const loginClass = {
        "Username": email.toString(),
        "Password": databaseName.GeneratePasswordHash(password),
        "loginMethod": loginMethod
      };
      
      const response = await axios({
        method: 'POST',
        url: 'api/Auth/Login',
        data: loginClass,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (typeof response.data.data[0].user_id_num === 'undefined') {
        throw "Incorrect email or password";
      }
      
      // Update user state with response data
      const userData = response.data.data[0];
      Object.assign(_user, {
        userEmail: userData.user_email,
        userIdNum: userData.user_id_num,
        userFirst: userData.user_first_name,
        userLast: userData.user_last_name,
        userPass: password,
        userIdName: userData.user_id_name,
        userSecurity: userData.user_security,
        userJwtToken: userData.user_jwt_token,
        isAuthenticated: true
      });

      return {
        isOk: true,
        data: _user
      };
    }
    catch (error) {
      if (error.response?.status === 500) {
        return {
          isOk: false,
          message: "Incorrect username/email or password"
        };
      }
      else if (error.response) {
        alert('Response failed with status code: ' + error.response.status + '\nMake sure that you and the serve have not lost connection');
        return {
          isOk: false,
          message: "Internal Server Error"
        };
      }
      else if (error.request) {
        alert('Request failed\nMake sure that you and the serve have not lost connection');
        return {
          isOk: false,
          message: "Internal Server Error"
        };
      }
      else {
        return {
          isOk: false,
          message: "Incorrect email or password."
        };
      }
    }
  };

  const logOut = async () => {
    Object.assign(_user, {...defaultUser, isAuthenticated: false});
  };

  const getUser = async () => {
    try {
      return {
        isOk: true,
        data: _user
      };
    }
    catch {
      return {
        isOk: false
      };
    }
  };

  const verifyAdmin = async (username, password) => {
    try {
      const loginClass = {
        "Username": username.toString(),
        "Password": databaseName.GeneratePasswordHash(password),
        "loginMethod": username.includes('@') ? "email" : "username"
      };

      const response = await axios({
        method: 'POST',
        url: 'api/Auth/Login',
        data: loginClass,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.data[0].user_security === 'administrator') {
        // Store admin data temporarily
        _tempAdmin.value = {
          userSecurity: response.data.data[0].user_security,
          userJwtToken: response.data.data[0].user_jwt_token,
          // Add any other needed admin data
        };
        
        return {
          isOk: true,
          data: _tempAdmin.value
        };
      }
      
      return {
        isOk: false,
        message: 'User does not have admin privileges'
      };
    } catch (error) {
      return {
        isOk: false,
        message: error.response?.status === 500 
          ? "Incorrect username/email or password"
          : "Verification failed"
      };
    }
  };

  const clearAdminVerification = () => {
    _tempAdmin.value = null;
  };

  const getAdminToken = () => {
    return _tempAdmin.value?.userJwtToken;
  };

  const hasValidAdminVerification = () => {
    return !!_tempAdmin.value;
  };

  // Return public methods and state
  return {
    user,
    hasPermission,
    loggedIn,
    defaultLogIn,
    logIn,
    logOut,
    getUser,
    verifyAdmin,
    clearAdminVerification,
    getAdminToken,
    hasValidAdminVerification
  };
}
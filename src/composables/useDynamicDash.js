import { computed, ref, watch, nextTick } from 'vue'
import { useRestAPI } from './useRestAPI'


export function useDynamicDash() {
	// State
	const lastAPIError = ref([]);
	const lastAPIErrorData = ref([]);
	const isLoading = ref(false);

	// Initialize the REST API functionality
	const api = useRestAPI()

	// API response handler 
	const handleApiResponse = async (apiCall) => {
		isLoading.value = true;
		try {
			const result = await apiCall();
			return { success: true, message: result.message, data: result.data };
		} catch (error) {
			console.error('API Error:', error);
			lastAPIError.value = error.response?.data?.message || 'Unknown error occurred';
			lastAPIErrorData.value = error.response?.data || []

			console.log('handleApiResponse', error)

			return { success: false, error: lastAPIError.value, message: error.response?.data?.message };
		} finally {
			isLoading.value = false;
		}
	};

	const fetchDashboardConfig = async () => {
		return handleApiResponse(async () => {
			const response = await api.get(`/api/dashboard-config`)
			return response
		})
	};

	const getDemoDashboardConfig = async () => {
		return [
			{
				id: 'systemCounts',
				type: 'grid',
				title: 'System Counts',
				minWidth: 4, // in columns
				rowSpan: 1,
				priority: 1,
				dataSource: 'system_counts', //reflects the object key in the response array
				config: {
					columns: [
						{ field: 'count_name', caption: 'Stat', type: 'string' },
						// { field: 'count_desc', caption: 'Desc', type: 'string' },
						{ field: 'count', caption: 'Count' , type: 'string'},
					],
					pagination: true,
					pageSize: 5,
				}
			},
			{
				id: 'putwallAllocations',
				type: 'chart',
				chartType: 'bar',
				title: 'Put Wall Allocation',
				minWidth: 8, // in columns
				rowSpan: 1,
				priority: 2,
				dataSource: 'put_wall_allocations', //reflects the object key in the response array
				config: {
					series: [
						{ field: 'lane_name', value: 'count' },
					],
					showLegend: false,
				}
			},
			{
				id: 'pickMods',
				type: 'grid',
				title: 'Pick Mods',
				minWidth: 4, // in columns
				rowSpan: 1,
				priority: 3,
				dataSource: 'pick_mod_containers', //reflects the object key in the response array
				config: {
					columns: [
						{ field: 'mod_name', caption: 'Name', type: 'string' },
						{ field: 'count', caption: 'Count' , type: 'string'},
					],
					pagination: true,
					pageSize: 5,
				}
			},
			{
				id: 'singleItemSorter',
				type: 'chart',
				chartType: 'bar',
				title: 'Sorter Lane Distribution',
				minWidth: 8, // in columns
				rowSpan: 1,
				priority: 4,
				dataSource: 'single_items_sorter', //reflects the object key in the response array
				config: {
					series: [
						{ field: 'lane_name', value: 'count' },
					],
					showLegend: false,
				}
			},

			{
				id: 'carouselLines',
				type: 'grid',
				title: 'Carousel Lines',
				minWidth: 4, // in columns
				rowSpan: 1,
				priority: 5,
				dataSource: 'carousel_lines', //reflects the object key in the response array
				config: {
					columns: [
						{ field: 'pod_name', caption: 'Name', type: 'string' },
						{ field: 'open_lines', caption: 'Open' , type: 'string'},
						{ field: 'released_lines', caption: 'Released' , type: 'string'},
						{ field: 'completed_lines', caption: 'Completed' , type: 'string'},
					],
					pagination: true,
					pageSize: 5,
				}
			},
			{
				id: 'scanners',
				type: 'chart',
				chartType: 'fullstackedbar',
				title: 'Scanners',
				minWidth: 8, // in columns
				rowSpan: 1,
				priority: 6,
				dataSource: 'scanners', //reflects the object key in the response array
				config: {
					series: [
						{ field: 'scanner_name', value: 'good_reads', caption: 'Good Reads', color: '#4DAF50' },
						{ field: 'scanner_name', value: 'no_reads', caption: 'No Reads', color: '#FF5252' },
					],
					showLegend: true,
				}
			},
			
		];
	};
	
	const getDemoDashboardConfigNew = async () => {
		return [
			{
				id: 'activity',
				name: 'activity',
				displayName: 'Activity',
				priority: 1,
				userSecurity: ['administrator', 'programmer'], //security roles for the category
				data: [{
						id: 'systemCounts',
						type: 'grid',
						title: 'System Counts',
						minWidth: 4, // in columns
						rowSpan: 1,
						priority: 1,
						userSecurity: ['administrator', 'programmer'], //security roles for the widget
						dataSource: 'system_counts', //reflects the object key in the response array
						config: {
							columns: [{
									field: 'count_name',
									caption: 'Stat',
									type: 'string'
								},
								{
									field: 'count',
									caption: 'Count',
									type: 'string'
								},
							],
							pagination: true,
							pageSize: 5,
						}
					},
					{
						id: 'putwallAllocations',
						type: 'chart',
						chartType: 'bar',
						title: 'Put Wall Allocation',
						minWidth: 8, // in columns
						rowSpan: 1,
						priority: 2,
						userSecurity: ['administrator', 'programmer'],
						dataSource: 'put_wall_allocations', //reflects the object key in the response array
						config: {
							series: [{
								field: 'lane_name',
								value: 'count'
							}, ],
							showLegend: false,
						}
					},
					{
						id: 'pickMods',
						type: 'grid',
						title: 'Pick Mods',
						minWidth: 4, // in columns
						rowSpan: 1,
						priority: 3,
						userSecurity: ['administrator', 'programmer'],
						dataSource: 'pick_mod_containers', //reflects the object key in the response array
						config: {
							columns: [{
									field: 'mod_name',
									caption: 'Name',
									type: 'string'
								},
								{
									field: 'count',
									caption: 'Count',
									type: 'string'
								},
							],
							pagination: true,
							pageSize: 5,
						}
					}
				]
			},
			{
				id: 'kpi',
				name: 'kpi',
				displayName: 'KPIs',
				priority: 2,
				userSecurity: ['administrator', 'programmer'],
				data: [{
						id: 'singleItemSorter',
						type: 'chart',
						chartType: 'bar',
						title: 'Sorter Lane Distribution',
						minWidth: 8, // in columns
						rowSpan: 1,
						priority: 4,
						userSecurity: ['administrator', 'programmer'],
						dataSource: 'single_items_sorter', //reflects the object key in the response array
						config: {
							series: [{
								field: 'lane_name',
								value: 'count'
							}, ],
							showLegend: false,
						}
					},

					{
						id: 'carouselLines',
						type: 'grid',
						title: 'Carousel Lines',
						minWidth: 4, // in columns
						rowSpan: 1,
						priority: 5,
						userSecurity: ['administrator', 'programmer'],
						dataSource: 'carousel_lines', //reflects the object key in the response array
						config: {
							columns: [{
									field: 'pod_name',
									caption: 'Name',
									type: 'string'
								},
								{
									field: 'open_lines',
									caption: 'Open',
									type: 'string'
								},
								{
									field: 'released_lines',
									caption: 'Released',
									type: 'string'
								},
								{
									field: 'completed_lines',
									caption: 'Completed',
									type: 'string'
								}
							],
							pagination: true,
							pageSize: 5,
						}
					},
					{
						id: 'scanners',
						type: 'chart',
						chartType: 'fullstackedbar',
						title: 'Scanners',
						minWidth: 8, // in columns
						rowSpan: 1,
						priority: 6,
						userSecurity: ['administrator', 'programmer'],
						dataSource: 'scanners', //reflects the object key in the response array
						config: {
							series: [{
									field: 'scanner_name',
									value: 'good_reads',
									caption: 'Good Reads',
									color: '#4DAF50'
								},
								{
									field: 'scanner_name',
									value: 'no_reads',
									caption: 'No Reads',
									color: '#FF5252'
								}
							],
							showLegend: true,
						}
					}
				],
			}
		]
	};

	const getDemoDashboardData = async() => {
		return {
			message: "Success Getting Dashboard",
			data: {
				system_counts: [
					{
						count_name: "Open Orders",
						count_desc: "Order That Have Not Been Marked As Pick Complete By WOSP",
						count: 5459
					},
					{
						count_name: "Open Pick Barcodes",
						count_desc: "Totes / Cartons That Are Still Requested By The Route Table For Pick Mod Usage",
						count: 24
					},
					{
						count_name: "Open Carousel Lines",
						count_desc: "Un picked Lines In The Carousel",
						count: 45
					},
					{
						count_name: "LPNs Awating Manifest",
						count_desc: "LPNs From The Post Putwall / Post Singles Sorter Process That Have NOT Been Through Manifest Scanner/Dim/Scale Yet",
						count: 8511
					}
				],
				single_items_sorter: [
					{ lane_name: "Lane01", count: 0 },
					{ lane_name: "Lane02", count: 5 },
					{ lane_name: "Lane03", count: 0 },
					{ lane_name: "Lane04", count: 12 },
					{ lane_name: "Lane05", count: 0 },
					{ lane_name: "Lane06", count: 3 },
					{ lane_name: "Lane07", count: 25 },
					{ lane_name: "Lane08", count: 13 },
					{ lane_name: "Lane09", count: 0 },
					{ lane_name: "Lane10", count: 11 },
					{ lane_name: "Lane11", count: 0 },
					{ lane_name: "Lane12", count: 50},
					{ lane_name: "Lane13", count: 0 },
					{ lane_name: "Lane14", count: 0 },
					{ lane_name: "Lane15", count: 4 }
				],
				manifest_lanes: [
					{ lane_name: "Manifest 1", count: 0 },
					{ lane_name: "Manifest 2", count: 748 }
				],
				put_wall_allocations: [
					{ lane_name: "Puwtwall01", count: 0 },
					{ lane_name: "Puwtwall02", count: 0 },
					{ lane_name: "Puwtwall03", count: 10 },
					{ lane_name: "Puwtwall04", count: 0 },
					{ lane_name: "Puwtwall05", count: 0 }
				],
				pick_mod_containers: [
					{ mod_name: "PMOD01", count: 6 },
					{ mod_name: "PMOD02", count: 4 },
					{ mod_name: "PMOD03", count: 10 },
					{ mod_name: "PMOD04", count: 6 },
					{ mod_name: "PMOD05", count: 7 },
					{ mod_name: "PMOD06", count: 2 },
					{ mod_name: "PMOD07", count: 15 },
					{ mod_name: "PMOD08", count: 5 }
				],
				carousel_lines: [
					{ pod_name: "POD01", open_lines: 3, released_lines: 4, completed_lines: 140 },
					{ pod_name: "POD02", open_lines: 4, released_lines: 6, completed_lines: 66 },
					{ pod_name: "POD03", open_lines: 4, released_lines: 6, completed_lines: 57 },
					{ pod_name: "POD04", open_lines: 2, released_lines: 6, completed_lines: 47 },
					{ pod_name: "POD05", open_lines: 4, released_lines: 6, completed_lines: 44 }
				],
				scanners: [
					{ scanner_name: "SCAN01", scanner_location: "PMOD 1/2", scanner_alive: true, good_reads: 4, no_reads: 13 },
					{ scanner_name: "SCAN02", scanner_location: "PMOD 3/4", scanner_alive: true, good_reads: 2, no_reads: 55 },
					{ scanner_name: "SCAN03", scanner_location: "PMOD 5/6", scanner_alive: true, good_reads: 10, no_reads: 22 },
					{ scanner_name: "SCAN04", scanner_location: "PMOD 7/8", scanner_alive: true, good_reads: 8, no_reads: 15 },
					{ scanner_name: "SCAN05", scanner_location: "Carousel Sorter", scanner_alive: true, good_reads: 337, no_reads: 20 },
					{ scanner_name: "SCAN06", scanner_location: "Singles / Multi Divert", scanner_alive: true, good_reads: 117, no_reads: 19 },
					{ scanner_name: "SCAN07", scanner_location: "Consolidation Sorter", scanner_alive: true, good_reads: 245, no_reads: 19 },
					{ scanner_name: "SCAN08", scanner_location: "Singles Sorter", scanner_alive: true, good_reads: 26, no_reads: 1 },
					{ scanner_name: "SCAN09", scanner_location: "Manifest #1", scanner_alive: true, good_reads: 0, no_reads: 17 },
					{ scanner_name: "SCAN10", scanner_location: "Manifest #2", scanner_alive: true, good_reads: 957, no_reads: 34 },
					{ scanner_name: "SCAN11", scanner_location: "Shipping Sorter", scanner_alive: true, good_reads: 135, no_reads: 1057 }
				],
				heartbeat_data: [
					{
						idkvkheartbeats: 20,
						heartbeat_name: "PLC_01",
						heartbeat_datetime: "2025-05-27T22:42:41.981",
						heartbeat_flag: true,
						heartbeat_source: "PLC",
						heartbeat_value: "CAROUSEL PLC",
						heartbeat_extra_value: null,
						alive: 1
					},
					{
						idkvkheartbeats: 29,
						heartbeat_name: "PLC_02",
						heartbeat_datetime: "2025-05-27T22:42:41.794",
						heartbeat_flag: true,
						heartbeat_source: "PLC",
						heartbeat_value: "SHIPPING PLC",
						heartbeat_extra_value: null,
						alive: 1
					},
					{
						idkvkheartbeats: 7,
						heartbeat_name: "SCAN01",
						heartbeat_datetime: "2025-05-27T22:42:41.950",
						heartbeat_flag: true,
						heartbeat_source: "Scanner",
						heartbeat_value: "PMOD 1/2",
						heartbeat_extra_value: 90,
						alive: 1
					},
					{
						idkvkheartbeats: 14,
						heartbeat_name: "SCAN02",
						heartbeat_datetime: "2025-05-27T22:42:41.950",
						heartbeat_flag: true,
						heartbeat_source: "Scanner",
						heartbeat_value: "PMOD 3/4",
						heartbeat_extra_value: 90,
						alive: 1
					},
					{
						idkvkheartbeats: 28,
						heartbeat_name: "SCAN03",
						heartbeat_datetime: "2025-05-27T22:42:41.950",
						heartbeat_flag: true,
						heartbeat_source: "Scanner",
						heartbeat_value: "PMOD 5/6",
						heartbeat_extra_value: 90,
						alive: 1
					},
					{
						idkvkheartbeats: 30,
						heartbeat_name: "SCAN04",
						heartbeat_datetime: "2025-05-27T22:42:41.950",
						heartbeat_flag: true,
						heartbeat_source: "Scanner",
						heartbeat_value: "PMOD 7/8",
						heartbeat_extra_value: 90,
						alive: 1
					}
				]
			}
			
		}

	}

  return {
    fetchDashboardConfig,
    getDemoDashboardConfig,
		getDemoDashboardData,
		getDemoDashboardConfigNew
  }
}

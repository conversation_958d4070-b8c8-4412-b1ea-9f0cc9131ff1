import { ref, computed, watch } from 'vue';
import { useRestAPI } from './useRestAPI'
import _ from 'lodash';

export function useStationAdmin() {
  // State
  const filterGroups = ref([]);
  const activeGroupId = ref(null);
  const isProcessing = ref(false);
  const lastAPIError = ref([]);
  const availableConditions = ref([]);
  const availableZones = ref([]);
  const filterGroupCounts = ref({});

  // Initialize the REST API functionality
  const api = useRestAPI();

  // Computed values
  const activeGroup = computed(() => {
    if (!activeGroupId.value) return null;
    return filterGroups.value.find(group => group.id_kvkorderstartfiltergroup === activeGroupId.value);
  });
  
  const conditionsByType = computed(() => {
    return _.groupBy(availableConditions.value, 'condition_name');
  });
  
  const activeConditions = computed(() => 
    availableConditions.value.filter(condition => condition.created_datetime !== null)
  );

  const uniqueConditionTypes = computed(() => {
    return Object.keys(conditionsByType.value);
  });

  // Watch for changes to active group
  watch(activeGroupId, (newID, oldID) => {
    if (newID !== oldID) {
      console.log('activeGroupId changed:', activeGroup.value?.filter_group_name);
      fetchGroupBindings();
    }
  });
  
  // API response handler - standardizes error handling
  const handleApiResponse = async (apiCall) => {
    isProcessing.value = true;
    try {
      const result = await apiCall();
      return { success: true };
    } catch (error) {
      console.error('API Error:', error);
      lastAPIError.value = error.response?.data?.message || 'Unknown error occurred';
      return { success: false, error: lastAPIError.value };
    } finally {
      isProcessing.value = false;
    }
  };

  // ----------------
  // Data Fetching Methods
  // ----------------
  
  // Fetch filter groups
  const fetchFilterGroups = async (params = {}) => {
    return handleApiResponse(async () => {
      const res = await api.get('/OrderStart/FilterGroups', params);
      filterGroups.value = res.data;
      
      // Default to the first group if no active group
      if (!activeGroupId.value && res.data.length > 0) {
        activeGroupId.value = res.data[0].id_kvkorderstartfiltergroup;
      }
      
      return res;
    });
  };

  // Fetch group bindings
  const fetchGroupBindings = async (params = {}) => {
    if (!activeGroup.value) return { success: false, error: 'No active group selected' };
    
    const grp = activeGroup.value.filter_group_name;
    return handleApiResponse(async () => {
      const response = await api.get(`/OrderStart/FilterGroup/${grp}`, params);
      availableConditions.value = response.data.FilterConditions || [];
      availableZones.value = response.data.FilterZones || [];
      filterGroupCounts.value = response.data.FilterGroupCounts || {};
      return response;
    });
  };

  // ----------------
  // Condition Management Methods
  // ----------------
  
  // Add a filter condition to a group
  const addCondition = async (conditionType, conditionValue) => {
    if (!activeGroup.value || !conditionType) {
      return { success: false, error: 'Missing required parameters' };
    }
    
    return handleApiResponse(async () => {
      const body = [{
        cmd: "ADDCONDITION",
        condition_name: conditionType,
        condition_value: conditionValue ? conditionValue : ''
      }];
      
      const response = await api.patch(`/OrderStart/FilterGroup/${activeGroup.value.filter_group_name}`, body );
      
      await fetchGroupBindings();
      return response;
    });
  };

  // Remove a condition from a group
  const removeCondition = async (conditionID) => {
    if (!activeGroup.value || !conditionID) {
      return { success: false, error: 'Missing required parameters' };
    }
    
    return handleApiResponse(async () => {
      const body = [{
        cmd: "DELETECONDITION",
        id_kvkorderstartactiveconditions: conditionID
      }];
      
      const response = await api.patch(`/OrderStart/FilterGroup/${activeGroup.value.filter_group_name}`,  body );
      
      await fetchGroupBindings();
      return response;
    });
  };

  // ----------------
  // Zone Management Methods
  // ----------------
  
  // Toggle a zone's active state
  const toggleZone = async (zone) => {
    if (!activeGroup.value) {
      return { success: false, error: 'No active group selected' };
    }
    
    // Check if the zone is already active in the group
    const isZoneActive = zone.id_kvkorderstartactivepickzones !== null;
    
    return handleApiResponse(async () => {
      let body;
      
      if (isZoneActive) {
        body = [{
          cmd: "DELETEPICKZONE",
          id_kvkorderstartactivepickzones: zone.id_kvkorderstartactivepickzones,
        }];
      } else {
        body = [{
          cmd: "ADDPICKZONE",
          order_start_pick_zone: zone.order_start_pick_zone,
        }];
      }
      
      const response = await api.patch(`/OrderStart/FilterGroup/${activeGroup.value.filter_group_name}`, body );
      
      await fetchGroupBindings();
      return response;
    });
  };

  // ----------------
  // Station Management Methods
  // ----------------
  
  // Toggle a station's active state
  const toggleStation = async (stationName, active, refreshCallback) => {
    return handleApiResponse(async () => {
      let response;
      
      if (active) {
        response = await api.post(`/OrderStart/TurnStationOn/${stationName}`);
      } else {
        response = await api.post(`/OrderStart/TurnStationOff/${stationName}`);
      }
      
      // If a refresh callback was provided, call it
      if (typeof refreshCallback === 'function') {
        await refreshCallback();
      }
      
      return response;
    });
  };

  // Update a station's filter group
  const updateStationFilterGroup = async (stationName, filterGroupName, refreshCallback) => {
    return handleApiResponse(async () => {
      const response = await api.patch(
        `/OrderStart/OrderStartStationFilterGroup/${stationName}?filter_group_name=${filterGroupName}`
      );
      
      // If a refresh callback was provided, call it
      if (typeof refreshCallback === 'function') {
        await refreshCallback();
      }
      
      return response;
    });
  };

  // ----------------
  // Group Management Methods
  // ----------------

  // Update a group's name
  const updateGroupName = async (newName) => {
    if (!activeGroup.value || !newName.trim()) {
      return { success: false, error: 'Missing required parameters' };
    }
    
    return handleApiResponse(async () => {
      const body = { filter_group_name: newName.trim() };
      
      const response = await api.patch(
        `/OrderStart/FilterGroupName/${activeGroup.value.filter_group_name}`, 
        body
      );
      
      // Update local state
      if (activeGroup.value) {
        activeGroup.value.filter_group_name = newName;
      }
      
      return response;
    });
  };
  
  // Update a group's description
  const updateGroupDesc = async (newDesc) => {
    if (!activeGroup.value || !newDesc.trim()) {
      return { success: false, error: 'Missing required parameters' };
    }
    
    return handleApiResponse(async () => {
      const body = { filter_group_desc: newDesc.trim() };
      
      const response = await api.patch(
        `/OrderStart/FilterGroupDesc/${activeGroup.value.filter_group_name}`, 
        body
      );
      
      // Update local state
      if (activeGroup.value) {
        activeGroup.value.filter_group_desc = newDesc;
      }
      
      return response;
    });
  };

  // Delete a group
  const deleteGroup = async (groupName) => {
    if (!groupName) {
      return { success: false, error: 'Missing group name' };
    }
    
    return handleApiResponse(async () => {
      const response = await api.delete(`/OrderStart/FilterGroup/${groupName}`);
      activeGroupId.value = null
      await fetchFilterGroups();
      return response;
    });
  };

  // Create a new group
  const createGroup = async (groupName, groupDesc) => {
    if (!groupName) {
      return { success: false, error: 'Missing group name' };
    }
    
    return handleApiResponse(async () => {
      const response = await api.put(
        `/OrderStart/FilterGroup/${groupName}`, 
        { filter_group_desc: groupDesc }
      );
      
      await fetchFilterGroups();
      return response;
    });
  };

  return {
    // State
    filterGroups,
    activeGroupId,
    activeGroup,
    lastAPIError,
    isProcessing,
    availableConditions,
    availableZones,
    conditionsByType,
    uniqueConditionTypes,
    activeConditions,
    filterGroupCounts,

    // Data Fetching Methods
    fetchFilterGroups,
    fetchGroupBindings,
    
    // Condition Management
    addCondition,
    removeCondition,
    
    // Zone Management
    toggleZone,
    
    // Station Management
    toggleStation,
    updateStationFilterGroup,
    
    // Group Management
    createGroup,
    updateGroupName,
    updateGroupDesc,
    deleteGroup
  };
}
import { ref, reactive, onUnmounted } from 'vue'
import { useLogger } from './useLogger'
import databaseName from '../myFunctions/databaseName';

const webSocketEndpoint = databaseName.getMonitorWebsocketEndpoint();
const serviceMap = {
  COMMAND: databaseName.getSocketCommandService(),
  ESTABLISH: databaseName.getSocketEstablishService()
};

export function useWebSocket(options = {}) {
  // Create a unique instance ID for tracking this instance
  const instanceId = Math.random().toString(36).substring(2, 8)
  // Default options
  const defaultOptions = {
    url: webSocketEndpoint.value,
    reconnectInterval: 3000,
    maxReconnectAttempts: 1,
    autoReconnect: true,
    debug: true
  }

  const opts = { ...defaultOptions, ...options }
  
  // Reactive state
  const socket = ref(null)
  const isConnected = ref(false)
  const reconnectAttempts = ref(0)
  const lastError = ref(null)
  const messages = reactive([])
  const MAX_MESSAGES = 100; // Adjust based on needs
  const MESSAGE_CLEANUP_THRESHOLD = MAX_MESSAGES * 1.2; // Cleanup when exceeding threshold

  // Connection status
  const connectionStatus = ref('DISCONNECTED') // DISCONNECTED, CONNECTING, CONNECTED, RECONNECTING, ERROR
  
  // This flag will help us prevent reconnections after intentional disconnects
  const shouldReconnect = ref(true)
  
  // Additional flags to prevent connection loops
  const isReconnecting = ref(false)
  const isDisconnecting = ref(false)
  
  // Debug logging
  const logger = useLogger(`WebSocket-${instanceId}`, opts.debug)

  // Track timeouts to be able to clear them
  let connectionTimeout = null
  let reconnectTimeout = null

  // Initialize WebSocket connection
  const connect = () => {
    // Prevent connection attempts when actively disconnecting
    if (isDisconnecting.value) {
      logger.log('Currently disconnecting, connect request ignored')
      return
    }
    
    // Check existing socket state
    if (socket.value) {
      if (socket.value.readyState === WebSocket.OPEN) {
        logger.log('Connection already established')
        isReconnecting.value = false // Reset reconnection flag if already connected
        return
      }
      
      if (socket.value.readyState === WebSocket.CONNECTING) {
        logger.log('Connection attempt already in progress')
        return
      }
      
      // For any other state, clean up the socket before continuing
      cleanupSocket()
    }

    // Now that we've cleared any existing connections, proceed with a new one
    connectionStatus.value = 'CONNECTING'
    logger.log('Connecting to', opts.url)
    
    // Set reconnection flags
    shouldReconnect.value = true
    
    try {
      socket.value = new WebSocket(opts.url)
      
      socket.value.onopen = onOpen
      socket.value.onmessage = onMessage
      socket.value.onerror = onError
      socket.value.onclose = onClose
  
      // If connection attempt hangs, force a retry
      connectionTimeout = setTimeout(() => {
        if (socket.value && socket.value.readyState !== WebSocket.OPEN) {
          logger.error('Connection timeout')
          cleanupSocket()
          
          // Reset reconnection flag if we're still in reconnecting state
          if (isReconnecting.value) {
            isReconnecting.value = false
          }
        }
      }, 10000) // 10 second timeout
    } catch (err) {
      handleError(err)
      cleanupSocket()
      
      // Reset reconnection flag if connection attempt fails immediately
      if (isReconnecting.value) {
        isReconnecting.value = false
      }
    }
  }

  // Helper function to clean up socket resources
  const cleanupSocket = () => {
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    
    stopHeartbeat()

    // Clear accumulated messages
    clearMessages()
    
    if (socket.value) {
      // Remove all listeners to prevent memory leaks
      try {
        socket.value.onopen = null
        socket.value.onmessage = null
        socket.value.onerror = null
        socket.value.onclose = null
        socket.value.close()
      } catch (err) {
        logger.error('Error closing socket', err)
      }
      socket.value = null
    }
  }

  // Event handlers
  const onOpen = () => {
    // Clear connection timeout
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    
    logger.log('Connected')
    isConnected.value = true
    connectionStatus.value = 'CONNECTED'
    reconnectAttempts.value = 0
    isReconnecting.value = false

    // Add heartbeat to keep connection alive
    // startHeartbeat()
  }

  // Heartbeat to detect connection issues early
  let heartbeatInterval = null
  const startHeartbeat = () => {
    stopHeartbeat() // Clear any existing heartbeat
    
    heartbeatInterval = setInterval(() => {
      // Only send heartbeat if connection is established
      if (socket.value && socket.value.readyState === WebSocket.OPEN) {
        try {
          // Simple heartbeat message
          const heartbeatMsg = { type: 'HEARTBEAT', timestamp: Date.now() }
          socket.value.send(JSON.stringify(heartbeatMsg))
          logger.log('Heartbeat sent')
        } catch (err) {
          logger.error('Error sending heartbeat', err)
          // If we can't send a heartbeat, connection might be bad
          if (isConnected.value && !isReconnecting.value && !isDisconnecting.value) {
            logger.log('Heartbeat failed, triggering reconnect')
            reconnect()
          }
        }
      } else if (isConnected.value) {
        // Socket isn't open even though we think it is
        logger.warn('Heartbeat detected closed connection')
        if (!isReconnecting.value && !isDisconnecting.value) {
          reconnect()
        }
      }
    }, 30000) // 30 second heartbeat
  }
  
  const stopHeartbeat = () => {
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval)
      heartbeatInterval = null
    }
  }

  const onMessage = (event) => {
    try {
        const data = JSON.parse(event.data)
        // Add timestamp to the message
        data._receivedAt = new Date().toISOString()
        logger.log('Received message', data)
        
        // Add message to array
        messages.push(data)
        
        // Cleanup if we exceed the threshold
        if (messages.length > MESSAGE_CLEANUP_THRESHOLD) {
            // Keep only the most recent MAX_MESSAGES
            messages.splice(0, messages.length - MAX_MESSAGES)
            logger.log(`Cleaned up messages. Current count: ${messages.length}`)
        }
    } catch (err) {
        logger.error('Error parsing message', err, event.data)
    }
}

  const onError = (event) => {
    logger.error('WebSocket error', event)
    lastError.value = new Date()
    connectionStatus.value = 'ERROR'
    
    // We don't need to do anything here because onClose will be called next
  }

  const onClose = (event) => {
    console.log('onClose:',event)
    // Only process close events for the current socket
    if (!socket.value) {
      return
    }
    
    logger.log('WebSocket closed', event.code, event.reason)
    
    // Update state
    isConnected.value = false
    connectionStatus.value = 'DISCONNECTED'
    
    // Clean up resources
    cleanupSocket()
    
    // Skip reconnection if we're intentionally disconnecting
    if (isDisconnecting.value) {
      isDisconnecting.value = false
      return
    }
    
    // Attempt to reconnect if enabled AND shouldReconnect is true
    if (opts.autoReconnect && shouldReconnect.value && !isReconnecting.value) {
      if (reconnectAttempts.value < opts.maxReconnectAttempts) {
        isReconnecting.value = true
        connectionStatus.value = 'RECONNECTING'
        reconnectAttempts.value++
        
        const delay = calculateBackoff(reconnectAttempts.value, opts.reconnectInterval)
        logger.log(`Reconnecting (${reconnectAttempts.value}/${opts.maxReconnectAttempts}) in ${delay}ms`)
        
        reconnectTimeout = setTimeout(() => {
          reconnectTimeout = null
          // Only reconnect if we still should
          if (shouldReconnect.value && !isDisconnecting.value) {
            connect()
            
            // Set a timeout to reset isReconnecting if connection doesn't succeed
            setTimeout(() => {
              if (isReconnecting.value && (!socket.value || socket.value.readyState !== WebSocket.OPEN)) {
                logger.log('Reconnection attempt timed out, resetting state')
                isReconnecting.value = false
              }
            }, 10000) // 10 second timeout for connection attempt
          } else {
            isReconnecting.value = false
          }
        }, delay)
      } else {
        logger.error('Maximum reconnection attempts reached')
        isReconnecting.value = false // Make sure we reset this flag
      }
    }
  }

  // Exponential backoff for reconnection attempts
  const calculateBackoff = (attempt, baseInterval) => {
    // Add some jitter to prevent all clients reconnecting simultaneously
    const jitter = Math.random() * 1000
    return Math.min(baseInterval * Math.pow(1.5, attempt - 1) + jitter, 30000) // Cap at 30 seconds
  }

  // Handle errors
  const handleError = (error) => {
    logger.error('Error', error)
    lastError.value = error
    connectionStatus.value = 'ERROR'
  }

  // Generic message sender
  const sendMessage = (message) => { 
    try {
      console.log('SENDING MESSAGE', message, socket.value)
      if (!isConnected.value || !socket.value || socket.value.readyState !== WebSocket.OPEN) {
        logger.warn('Cannot send message, not connected')
        return false
      }
  
   
       console.log('SENDING MESSAGE IN TRY CATCH', message)
      // pick service based on mess
      // age type
      message.msgToService = serviceMap[message.msgType] || message.msgToService;
      const messageStr = JSON.stringify(message)
      socket.value.send(messageStr)
      logger.log('Sent message', message)
      return true
    } catch (err) {
      console.log('Error sending message', err)
        handleError(err)
      return false
    }
  }

  // Disconnect WebSocket
  const disconnect = () => {
    // Prevent concurrent operations
    if (isDisconnecting.value) {
      return
    }
    
    isDisconnecting.value = true
    shouldReconnect.value = false // Disable auto-reconnect
    
    // Cancel pending reconnection
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
      reconnectTimeout = null
    }
    
    // Skip reconnection flag
    isReconnecting.value = false
    
    logger.log('Disconnecting')
    
    // Clean up resources
    cleanupSocket()
    
    // Update state
    isConnected.value = false
    connectionStatus.value = 'DISCONNECTED'
    
    // Reset the flag after a short delay to ensure all close events are processed
    setTimeout(() => {
      isDisconnecting.value = false
    }, 300)
  }

  // Clear message history
  const clearMessages = () => {
    messages.splice(0, messages.length)
    logger.log('Messages cleared')
  }

  // Reconnect manually
  const reconnect = () => {
    // Check if we're already in a disconnection process
    if (isDisconnecting.value) {
      logger.log('Disconnection in progress, reconnect request ignored')
      return
    }
    
    // Force reset the reconnection state to allow a new reconnection attempt
    if (isReconnecting.value) {
      logger.log('Forcing reset of previous reconnection attempt')
      
      // Cancel any pending reconnection
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout)
        reconnectTimeout = null
      }
    }
    
    logger.log('Manual reconnection initiated')
    
    // Set flags for reconnection
    shouldReconnect.value = true
    isReconnecting.value = true
    
    // Ensure clean slate by disconnecting first
    cleanupSocket()
    
    // Wait a moment for cleanup to complete
    setTimeout(() => {
      reconnectAttempts.value = 0
      
      // Check if we're still in reconnection mode
      if (!isDisconnecting.value && shouldReconnect.value) {
        connect()
      }
      
      // Reset reconnecting state if connect() doesn't get called
      // or if connect() returns early without attempting a connection
      if (isReconnecting.value && !socket.value) {
        isReconnecting.value = false
      }
    }, 500)
  }

  // Cleanup on component unmount
  onUnmounted(() => {
    logger.log('Component unmounting, disconnecting WebSocket')
    
    // Cancel all pending timeouts
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
      reconnectTimeout = null
    }
    
    if (connectionTimeout) {
      clearTimeout(connectionTimeout)
      connectionTimeout = null
    }
    
    stopHeartbeat()
    
    // Ensure we don't reconnect after unmount
    shouldReconnect.value = false
    isReconnecting.value = false
    isDisconnecting.value = true
    
    // Clean up the socket
    cleanupSocket()
  })

  // Return the public API
  return {
    // State
    isConnected,
    connectionStatus,
    messages,
    lastError,
    reconnectAttempts,
    
    // Methods
    connect,
    disconnect,
    reconnect,
    sendMessage,
    clearMessages
  }
}
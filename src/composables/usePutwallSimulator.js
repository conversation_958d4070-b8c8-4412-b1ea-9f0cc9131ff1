import { ref } from 'vue';
import notify from 'devextreme/ui/notify';

export function usePutwallSimulator() {
  const simulationActive = ref(false);
  const simulationInterval = ref(null);

  const updateRandomBin = (putwallData, options) => {
    const {
      onDataUpdated,
      rowKeys,
      enabledFlag,
      areaIdField,
      locationNameField
    } = options;
    
    console.log("Updating a random bin in active put walls");
    
    // Make a deep copy of the current data
    const updatedData = JSON.parse(JSON.stringify(putwallData.value));
    
    // Filter only active putwall areas
    const activePutwalls = updatedData.filter(pw => pw[enabledFlag]);
    
    // If no active putwalls, log and return
    if (activePutwalls.length === 0) {
      notify("No active put walls found. Enable a put wall to see changes.", "warning", 3000);
      return false;
    }
    
    // Current date for timestamps
    const now = new Date();
    const nowISO = now.toISOString();
    
    // Old date (5 hours ago) for "old" bins
    const oldDate = new Date(now);
    oldDate.setHours(oldDate.getHours() - 8);
    const oldISO = oldDate.toISOString();
    
    // Select a random active putwall area
    const putwallIndex = Math.floor(Math.random() * activePutwalls.length);
    const selectedPutwall = activePutwalls[putwallIndex];
    
    // Find this putwall in the original data
    const originalPutwallIndex = updatedData.findIndex(pw => pw[areaIdField] === selectedPutwall[areaIdField]);
    const putwall = updatedData[originalPutwallIndex];
    
    // Select a random row
    const randomRowIndex = Math.floor(Math.random() * rowKeys.length);
    const randomRow = rowKeys[randomRowIndex];
    
    // If the row doesn't exist or is empty, try another row
    if (!putwall[randomRow] || putwall[randomRow].length === 0) {
      console.log(`Row ${randomRow} is empty in put wall ${putwall[areaIdField]}, trying another bin`);
      return updateRandomBin(putwallData, options);
    }
    
    // Select a random bin in that row
    const binIndex = Math.floor(Math.random() * putwall[randomRow].length);
    const location = putwall[randomRow][binIndex];
    
    // Generate random order number
    const orderNum = 'ORD' + Math.floor(Math.random() * 100000);
    
    // Randomly select a state (0-5)
    const randomState = Math.floor(Math.random() * 6);
    
    // Set common properties
    location.container_lpns = randomState === 0 ? null : [];
    location.wms_order_number = randomState === 0 ? null : orderNum;
    location.last_activity_datetime = randomState === 0 ? null : (randomState === 3 ? oldISO : nowISO);
    location.order_pick_complete = randomState === 2;
    location.multi_bin_order = randomState === 4 || (randomState === 5 && Math.random() > 0.5);
    location.order_priority = randomState === 5 ? 1 : 0;
    
    // Set count properties based on state
    if (randomState === 0) {
      // Not assigned
      location.completed_lpn_count = 0;
      location.total_lpn_count = 0;
    } else if (randomState === 2) {
      // Complete
      location.total_lpn_count = Math.floor(Math.random() * 5) + 1;
      location.completed_lpn_count = location.total_lpn_count;
    } else {
      // All other states
      location.completed_lpn_count = Math.floor(Math.random() * 3);
      location.total_lpn_count = location.completed_lpn_count + Math.floor(Math.random() * 3) + 1;
    }
    
    // Log which bin was updated
    console.log(`Updated bin ${location[locationNameField]} in put wall ${putwall[areaIdField]} to state ${randomState}`);
    
    // Update the visual data
    putwallData.value = updatedData;
    
    // Call the callback if provided
    if (onDataUpdated) {
      onDataUpdated(updatedData);
    }
    
    return true;
  };

  const toggleSimulation = (putwallData, options) => {
    // Extract the interval time from options with a safe default
    const intervalTime = options.simulationIntervalTime || 3000;
    
    // Validate minimum interval time
    if (intervalTime < 500) {
      console.warn("Simulation interval too fast, using minimum 500ms");
    }
    
    // Use safe interval value
    const safeInterval = Math.max(500, intervalTime);
    
    if (simulationActive.value) {
      // Stop the simulation
      clearInterval(simulationInterval.value);
      simulationInterval.value = null;
      simulationActive.value = false;
      notify('Simulation stopped', 'info', 2000);
      return false;
    }
    
    // Check if any put walls are active
    const activePutwalls = putwallData.value.filter(pw => pw[options.enabledFlag]);
    if (activePutwalls.length === 0) {
      notify("No active put walls found. Enable at least one put wall to start the simulation.", "warning", 3000);
      return false;
    }
    
    // Start the simulation
    simulationActive.value = true;
    notify(`Simulation started! Updating one bin every ${safeInterval/1000} seconds in active put walls`, 'success', 2000);
    
    // Send the command to the server if callback provided
    if (options.onCommandSent) {
      options.onCommandSent('SIMULATION', 'N/A', false);
    }
    
    // Run immediately once
    updateRandomBin(putwallData, options);
    
    // Then set interval to run every X seconds
    simulationInterval.value = setInterval(() => {
      updateRandomBin(putwallData, options);
    }, safeInterval);
    
    return true;
  };

  /**
   * Stops the simulation if it's running
   */
  const stopSimulation = () => {
    if (simulationActive.value) {
      clearInterval(simulationInterval.value);
      simulationInterval.value = null;
      simulationActive.value = false;
    }
  };

  return {
    simulationActive,
    updateRandomBin,
    toggleSimulation,
    stopSimulation
  };
}
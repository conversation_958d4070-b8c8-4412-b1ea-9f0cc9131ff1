import { computed, ref, watch, nextTick } from 'vue'
import { useWebSocket } from './useWebSocket'
import databaseName from '../myFunctions/databaseName'
import auth from '../auth'

export function useWaveManagement(options = {}) {
	// =========================================
  // Shared State
  // =========================================
	const waveMainData = ref({});
	const waveMonitorData = ref({});
	const createWaveResponse = ref({});
	const commitWaveResponse = ref({});
	const releaseWaveResponse = ref({});

	// Message base for WebSocket
	const msgBase = databaseName.getWebsocketMessageBase();

	// Initialize the base WebSocket functionality
	const websocket = useWebSocket(options)
	const { 
		connect,
		isConnected, 
		connectionStatus, 
		messages, 
		sendMessage, 
		lastError,
		reconnect,
		disconnect,
		clearMessages
	} = websocket

	// Helper function to safely parse potentially double-encoded JSON
  const safeJsonParse = (input) => {
		if (typeof input !== 'string') return input;

		// Only attempt parse if string starts with { or [
		const isJsonStructure = input.trim().startsWith('{') || input.trim().startsWith('[');

		if (!isJsonStructure) { return input; }

    try {
      // First parse attempt
      const parsed = JSON.parse(input);
      
      // If the result is a string and looks like JSON, parse it again
      if (typeof parsed === 'string' && 
        (parsed.startsWith('[') || parsed.startsWith('{'))) {
        try {
          return JSON.parse(parsed);
        } catch (innerError) {
          console.warn('Second JSON parse failed, using first result', innerError);
          return parsed;
        }
      }
      
      // If it's not a string or doesn't look like JSON, return the first parse
      return parsed;
    } catch (error) {
      console.error('JSON parsing failed:', error);
      throw error; // Re-throw to let caller handle it
    }
  };

	// =========================================
  // Watchers
  // =========================================

	// Watch for connection status changes to trigger cleanup
	// watch(connectionStatus, (newStatus, oldStatus) => {
	// 	if (newStatus === 'CONNECTED' && oldStatus !== 'CONNECTED') {
	// 		sendEstablishMessage("OVERALL-WAVE-DASHBOARD");
	// 		sendGetWaveDashboardCommand();
	// 	}
	// });


	// WEB-SOCKET: PROCESS MESSAGES
	watch(messages, (newMessages) => {
		if (newMessages.length === 0) return;
	
		const msg = newMessages[newMessages.length - 1];
	
		if (msg.CommandObj) {
			const cmdName = msg.CommandObj.cmdName;
			const cmdMessage = msg.CommandObj.cmdMessage;

			// Process specific commands
			switch(cmdName) {
				case "UPDATE-OVERALL-WAVE-DASHBOARD":
					waveMainData.value = safeJsonParse(cmdMessage);
					break;
				case "UPDATE-WAVE-STATUSES":
					waveMonitorData.value = safeJsonParse(cmdMessage);
					break;
				case "CREATE-PUTWALL-WAVE_SUCCESS":
					createWaveResponse.value = safeJsonParse(cmdMessage);
					break;
				case "COMMIT-PUTWALL-WAVE_SUCCESS":
					commitWaveResponse.value = safeJsonParse(cmdMessage);
					break;
				case "CREATE-NONPUTWALL-WAVE_SUCCESS":
					createWaveResponse.value = safeJsonParse(cmdMessage);
					break;
				case "COMMIT-NONPUTWALL-WAVE_SUCCESS":
					commitWaveResponse.value = safeJsonParse(cmdMessage);
					break;
				case "CREATE-PUTWALL-WAVE_ERROR":
					lastError.value = { message: cmdMessage, timestamp: Date.now(), command: cmdName };
					break;
				case "CREATE-NONPUTWALL-WAVE_ERROR":
					lastError.value = { message: cmdMessage, timestamp: Date.now(), command: cmdName };
					break;
				case "RELEASE-WAVE-TO-PUTWALL_SUCCESS":
					releaseWaveResponse.value = safeJsonParse(cmdMessage);
					break;
				case "RELEASE-WAVE-TO-PUTWALL_ERROR":
					lastError.value = { message: cmdMessage, timestamp: Date.now(), command: cmdName };
					break;
				case "RELEASE-WAVE_SUCCESS":
					releaseWaveResponse.value = safeJsonParse(cmdMessage);
					break;
				case "RELEASE-WAVE_ERROR":
					lastError.value = { message: cmdMessage, timestamp: Date.now(), command: cmdName };
					break;
				default:
					console.log('Unhandled command:', cmdName, cmdMessage);
			}
		}	
	});


	// =========================================
	// WebSocket message commands
	// =========================================
	// Send ESTABLISH message
	const sendEstablishMessage = (msgGroup) => {
		console.log('sendEstablishMessage - ', msgGroup)
		const user =  auth._user
		const msgObj = {
			msgType: "ESTABLISH",
			msgFromService: '',
			msgFromUser: "",
			EstablishConnObj: {
				messageGroup: msgGroup,
				userIdName: user.userIdName,
				userSecurity: user.userSecurity,
				message: ""
			}
		}
		// console.log('SENDING ESTABLISH MESSAGE', msgObj)
		return sendMessage({...msgBase.value,...msgObj})
	}

	const sendGetWaveDashboardCommand = () => {
		const msgObj = {
			msgType: "COMMAND",
			msgFromService: 'OVERALL-WAVE-DASHBOARD',
			msgFromUser: "",
			CommandObj: {
				cmdName: "GET-OVERALL-WAVE-DASHBOARD",
				cmdMessage: ""
			}
		}
		return sendMessage({...msgBase.value,...msgObj})
	}	

	const sendCreateWaveCommand = (waveConfig, isPutwall) => {
		try {

			const msgObj = {
				msgType: "COMMAND",
				msgFromService: isPutwall ? 'CREATE-PUTWALL-WAVE' : 'CREATE-NONPUTWALL-WAVE' ,
				msgToUser: "",
				msgFromUser: "",
				CommandObj: {
					cmdName: isPutwall ? 'CREATE-PUTWALL-WAVE' : 'CREATE-NONPUTWALL-WAVE' ,
					cmdMessage: JSON.stringify(waveConfig)
				}
			}
			return sendMessage({...msgBase.value,...msgObj})
		} catch (error) {
			console.error('Error parsing waveConfig:', error);
		}
	}

	const sendCommitWaveCommand = (waveID, isPutwall) => {
		try {
			const msgObj = {
				msgType: "COMMAND",
				msgFromService: isPutwall ? 'COMMIT-PUTWALL-WAVE': 'COMMIT-NONPUTWALL-WAVE',
				msgToUser: "",
				msgFromUser: "",
				CommandObj: {
					cmdName: isPutwall ? 'COMMIT-PUTWALL-WAVE': 'COMMIT-NONPUTWALL-WAVE',
					cmdMessage: waveID
				}
			}
			return sendMessage({...msgBase.value,...msgObj})
		} catch (error) {
			console.error('Error sending commit wave command:', error);
		}
	}

	const sendReleaseWaveToPutwallCommand = (waveNumber) => {
		try {
			console.log('sendReleaseWaveToPutwallCommand - ', waveNumber)
			const msgObj = {
				msgType: "COMMAND",
				msgFromService: 'RELEASE-WAVE-TO-PUTWALL',
				msgToUser: "",
				msgFromUser: "",
				CommandObj: {
					cmdName: 'RELEASE-WAVE-TO-PUTWALL',
					cmdMessage: waveNumber
				}
			}
			return sendMessage({...msgBase.value,...msgObj})
		} catch (error) {
			console.error('Error parsing waveConfig:', error);
		}
	}

	const sendReleaseWaveCommand = (waveNumber) => {
		try {
			console.log('sendReleaseWaveCommand - ', waveNumber)
			const msgObj = {
				msgType: "COMMAND",
				msgFromService: 'RELEASE-WAVE',
				msgToUser: "",
				msgFromUser: "",
				CommandObj: {
					cmdName: 'RELEASE-WAVE',
					cmdMessage: waveNumber
				}
			}
			return sendMessage({...msgBase.value,...msgObj})
		} catch (error) {
			console.error('Error parsing waveConfig:', error);
		}
	}

	const sendGetWaveMonitorCommand = () => {
		const msgObj = {
			msgType: "COMMAND",
			msgFromService: 'UPDATE-WAVE-STATUSES',
			msgFromUser: "",
			CommandObj: {
				cmdName: "UPDATE-WAVE-STATUSES",
				cmdMessage: ""
			}
		}
		return sendMessage({...msgBase.value,...msgObj})
	}

	// connect and send establish message immediately
	const initialize = () => {
		if (isConnected.value) {
			console.log('Initializing WebSocket connection');
			// sendEstablishMessage("OVERALL-WAVE-DASHBOARD")
		} else {
			// We can connect manually and handle the establish message in a watcher
			reconnect()
		}
	}



	return {
		// Re-export base WebSocket functionality
		...websocket,

		//Send commands
		sendEstablishMessage,
		sendCreateWaveCommand,
		sendCommitWaveCommand,
		sendReleaseWaveCommand,
		sendReleaseWaveToPutwallCommand,
		sendGetWaveDashboardCommand,
		sendGetWaveMonitorCommand,
		// Shared state
		commitWaveResponse,
		waveMainData,
		waveMonitorData,
		createWaveResponse,
		releaseWaveResponse,
		// loading and error states

		// Initialization helper
		initialize
	}
}
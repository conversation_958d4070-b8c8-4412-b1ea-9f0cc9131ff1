import 'devextreme/dist/css/dx.common.css';
import './themes/generated/theme.base.css';
import './themes/generated/theme.additional.css';
import { createApp }  from "vue";
import router from "./router";
import themes from "devextreme/ui/themes";

import App from "./App";
import appInfo from "./app-info";
import config from "devextreme/core/config";
import { licenseKey } from "./myFunctions/devextreme-license.ts";

config({
    licenseKey
});

themes.initialized(() => {
    const app = createApp(App);
    app.use(router);
    app.config.globalProperties.$appInfo = appInfo;
    //app.config.globalProperties.$test = () => { console.log('test'); };
    //app.config.globalProperties.$scanFocus = (barcode) => {console.log(barcode);};
    app.mount('#app');
});

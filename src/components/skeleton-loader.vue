<template>
  <div class="abstract-skeleton-dark">
    <!-- Top bar -->
    <!-- <div class="skeleton-top-bar">
      <div class="skeleton-shape"></div>
      <div class="skeleton-shape medium"></div>
    </div> -->
    
    <!-- Content area -->
    <div class="skeleton-content">
      <!-- Content blocks with varying sizes -->
      <div class="skeleton-blocks">
        <div class="skeleton-block large"></div>
        
        <div class="skeleton-row">
          <div class="skeleton-block small"></div>
          <div class="skeleton-block small"></div>
          <div class="skeleton-block small"></div>
        </div>
        
        <div class="skeleton-row">
          <div class="skeleton-block medium"></div>
          <div class="skeleton-block medium"></div>
        </div>
         
        <div class="skeleton-roe">
          <div class="skeleton-line"></div>
          <div class="skeleton-line"></div>
          <div class="skeleton-line"></div>
          <div class="skeleton-line short"></div>
        </div>
      </div>
    </div>
    
    <!-- Overlay content slot -->
    <div class="overlay-content" v-if="!isLoading">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  isLoading: {
    type: Boolean,
    default: true
  }
});
</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

.abstract-skeleton-dark {
  position: relative;
  width: 100%;
  height: 100%;
  justify-content: space-between;
  overflow: hidden;
  display: flex;
  flex-direction: row;
}

/* Common skeleton element style */
.skeleton-shape,
.skeleton-block,
.skeleton-line {
  //background: linear-gradient(90deg, #424250 25%, #4a4a58 50%, #424250 75%);
  background: linear-gradient(90deg, #3a3a45 25%, #3c3c47 50%, #3a3a45 75%);
  background-size: 200% 100%;
  animation: shimmer 3.5s infinite;
  border-radius: 6px;
}

/* Top bar */
.skeleton-top-bar {
  height: 60px;
  width: 100%;
  background-color: #2e2e38;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.skeleton-shape {
  width: 120px;
  height: 24px;
}

.skeleton-shape.medium {
  width: 180px;
}

/* Content area */
.skeleton-content {
  padding: 30px;
flex: auto;
  overflow-y: hidden;
  display: flex;
}

.skeleton-blocks {
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
  flex: auto;
  justify-content: space-between;
  display: flex;
  flex-direction: column;
}

.skeleton-block {
  border-radius: 8px;
  margin-bottom: 25px;
}

.skeleton-block.large {
  height: 180px;
  width: 100%;
}

.skeleton-row {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
}

.skeleton-block.small {
  flex: 1;
  height: 100px;
}

.skeleton-block.medium {
  flex: 1;
  height: 120px;
}

.skeleton-block.full {
  width: 100%;
  height: 150px;
}

.skeleton-lines {
  padding: 20px;
  background-color: #2e2e38;
  border-radius: 8px;
  margin-bottom: 25px;
}

.skeleton-line {
  height: 16px;
  width: 100%;
  margin-bottom: 15px;
}

.skeleton-line.short {
  width: 60%;
}

/* Overlay content */
.overlay-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #363640;
  z-index: 20;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Media query for smaller screens */
@media (max-width: 768px) {
  .skeleton-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .skeleton-block.small,
  .skeleton-block.medium {
    width: 100%;
  }
}
</style>
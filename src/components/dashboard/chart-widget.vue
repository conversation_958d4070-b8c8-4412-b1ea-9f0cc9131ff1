<template>
	<div class="widget-wrap">
    <div class="widget-header">
      <div class="widget-title">{{ widgetData.title }}</div>
    </div>
    <div class="widget-body">
      <div v-if="loading" class="widget-loading">Loading...</div>
      <div v-else-if="error" class="widget-error">{{ error }}</div>
      
			<div v-else class="widget-container">

        <!-- Bar Chart -->
				<template v-if="widgetData.chartType === 'bar'">
					<DxChart :data-source="data" :style="{ height: '100%' }">
						<DxCommonSeriesSettings :type="widgetData.chartType" :hover-mode="'allArgumentPoints'"/>
						<DxSeries v-for="(item, index) in widgetData.config.series" :key="index" :argument-field="item.field" :value-field="item.value" color="#97C95C"/>
						<DxLegend :visible="widgetData.config.showLegend" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom" /> 
						<DxTooltip :enabled="true" cornerRadius="10" color="#181818"/>
					</DxChart>
				</template>
				
				<!-- Pie Chart -->
				<template v-if="widgetData.chartType === 'pie'">
					<DxPieChart :data-source="data" :redraw-on-resize="true" resolve-label-overlapping="shift" :style="{ height: '100%' }">
						<DxSeries v-for="(item, index) in widgetData.config.series" :key="index" :argument-field="item.field" :value-field="item.value" type="doughnut"/>
						<DxLegend :visible="widgetData.config.showLegend" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom" /> 
						<DxTooltip :enabled="true" cornerRadius="10" color="#181818"/>
					</DxPieChart>
				</template>

				<!-- Full Stacked Bar Chart -->
				<template v-if="widgetData.chartType === 'fullstackedbar'">	
			
					<DxChart :data-source="data" :rotated="true" :style="{ height: '100%' }">
						<DxCommonSeriesSettings :type="widgetData.chartType" :bar-padding="0" />
						<!-- <DxCommonSeriesSettings :type="widgetData.chartType" :hover-mode="'allArgumentPoints'"/> -->
						<DxSeries v-for="(item, index) in widgetData.config.series" :key="index" :argument-field="item.field" :value-field="item.value" :color="item.color" :name="item.caption"/>
						<DxLegend :visible="widgetData.config.showLegend" orientation="horizontal" item-text-position="right" horizontal-alignment="center" vertical-alignment="bottom" /> 
						<DxTooltip :enabled="true" cornerRadius="10" color="#181818"/>
					</DxChart>
				</template>

      </div>

    </div>
  </div>
</template>

<script setup>
	import { ref, computed, onMounted, watch, defineProps } from 'vue';

	import DxChart, { DxCommonSeriesSettings, DxSeries } from 'devextreme-vue/chart';

	import { DxPieChart,DxLabel,DxLegend,DxExport,DxTooltip,DxFont, DxTitle, DxConnector } from 'devextreme-vue/pie-chart';

	const props = defineProps({
		widgetData: {
			type: Object,
			required: true
		},
		data: {
			type: Array,
			default: () => []
		}
	});

	const loading = ref(false);
	const error = ref(null);

	const columns = computed(() => {
		return props.widgetData.config.columns || [];
	});
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.widget-wrap {
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	.widget-container {
		height: 100%;
	}
	.widget-body {
		height: 100%;
	}
	.widget-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}

</style>
<template>
	<div class="widget-wrap">
    <div class="widget-header">
      <div class="widget-title">{{ widgetData.title }}</div>
    </div>
    <div class="widget-body">
      <div v-if="loading" class="widget-loading">Loading...</div>
      <div v-else-if="error" class="widget-error">{{ error }}</div>
      
			<div v-else class="widget-container">

				<!-- data grid -->
        <table>
          <thead>
            <tr>
              <th v-for="column in columns" :key="column.field"> {{ column.caption }} </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, rowIndex) in data" :key="rowIndex">
              <td v-for="column in columns" :key="`${rowIndex}-${column.field}`">
                {{ row[column.field] }}
              </td>
            </tr>
          </tbody>
        </table>

      </div>

    </div>
  </div>
</template>

<script setup>
	import { ref, computed, onMounted, watch, defineProps } from 'vue';

	const props = defineProps({
		widgetData: {
			type: Object,
			required: true
		}, 
		data: {
			type: Array,
			default: () => []
		}
	});

	const loading = ref(false);
	const error = ref(null);

	const columns = computed(() => {
		return props.widgetData.config.columns || [];
	});
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.widget-wrap {
		height: 100%;
		display: flex;
		flex-direction: column;
	}
	.widget-container {
		height: 100%;
	
	}
	.widget-body {
		height: 100%;
			overflow-y: auto;
	}
	.widget-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}
	
	.widget-container table {
		width: 100%;
		border-collapse: collapse;
	}

	.widget-container th {
		text-align: left;
		padding: 3px;
		font-weight: 500;
		border-bottom: 1px solid $base-border-color;;
	}

	.widget-container td {
		padding: 3px;
	}

</style>
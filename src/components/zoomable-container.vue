<template>
  <div class="zoom-wrapper" ref="wrapper">
    <!-- Zoom controls -->
    <div v-if="!hideControls" class="zoom-controls">
      <button @click="decreaseZoom" class="zoom-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"/>
          <line x1="21" y1="21" x2="16.65" y2="16.65"/>
          <line x1="8" y1="11" x2="14" y2="11"/>
        </svg>
      </button>
      <span class="zoom-level">{{ (zoomLevel * 100).toFixed(0) }}%</span>
      <button @click="increaseZoom" class="zoom-button">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="11" cy="11" r="8"/>
          <line x1="21" y1="21" x2="16.65" y2="16.65"/>
          <line x1="11" y1="8" x2="11" y2="14"/>
          <line x1="8" y1="11" x2="14" y2="11"/>
        </svg>
      </button>
    </div>

    <!-- Responsive container -->
    <div class="fixed-container" ref="container">
      <!-- Content container that scales from center -->
      <div 
        class="content-container"
        :style="contentStyle"
      >
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ZoomableContainer',
  props: {
    hideControls: {
      type: Boolean,
      default: false
    },
    modelValue: {
      type: Number,
      default: 1
    },
    // Optional min height for the container
    minHeight: {
      type: String,
      default: '400px'
    }
  },
  emits: ['update:modelValue', 'zoom-in', 'zoom-out', 'zoom-change'],
  data() {
    return {
      zoomLevel: this.modelValue,
      containerWidth: 0,
      containerHeight: 0,
      resizeObserver: null
    }
  },
  watch: {
    modelValue(newValue) {
      this.zoomLevel = newValue
    }
  },
  computed: {
    contentStyle() {
      return {
        transform: `scale(${1 / this.zoomLevel})`,
        width: `${100 * this.zoomLevel}%`,
        height: `${100 * this.zoomLevel}%`,
        left: `${(1 - this.zoomLevel) * 50}%`,
        top: `${(1 - this.zoomLevel) * 50}%`
      }
    }
  },
  methods: {
    increaseZoom() {
      const newZoom = Math.min(this.zoomLevel + 0.1, 3)
      this.updateZoom(newZoom)
      this.$emit('zoom-in')
    },
    decreaseZoom() {
      const newZoom = Math.max(this.zoomLevel - 0.1, 0.5)
      this.updateZoom(newZoom)
      this.$emit('zoom-out')
    },
    updateZoom(newValue) {
      this.zoomLevel = Number(Math.round(newValue * 100) / 100)
      this.$emit('update:modelValue', newValue)
      this.$emit('zoom-change', newValue)
    },
    updateContainerDimensions() {
      if (this.$refs.container) {
        this.containerWidth = this.$refs.container.offsetWidth
        this.containerHeight = this.$refs.container.offsetHeight
        this.$emit('dimensions-change', {
          width: this.containerWidth,
          height: this.containerHeight
        })
      }
    }
  },
  mounted() {
    this.updateContainerDimensions()
    
    // Set up resize observer
    this.resizeObserver = new ResizeObserver(entries => {
      this.updateContainerDimensions()
    })
    
    if (this.$refs.wrapper) {
      this.resizeObserver.observe(this.$refs.wrapper)
    }
  },
  beforeUnmount() {
    // Clean up resize observer
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  }
}
</script>

<style scoped>
.zoom-wrapper {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  height: 100%;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
}

.zoom-button {
  padding: 0.5rem;
  background-color: #f3f4f6;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-button:hover {
  background-color: #e5e7eb;
}

.zoom-level {
  font-size: 0.875rem;
  color: #4b5563;
  min-width: 4rem;
  text-align: center;
}

.fixed-container {
  width: 100%;
  height: 100%;
  min-height: v-bind('minHeight');
  position: relative;
  overflow: hidden;
  flex-grow: 1;
}

.content-container {
  position: absolute;
  transform-origin: center;
  transition: all 0.2s ease;
	display:flex
}
</style>
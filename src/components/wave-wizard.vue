<template>
	<div class="wrap">

		<OrderStats v-if="currentStep !=2" :orderStatsData="waveStatsData" :direction="'column'" :rowHeight="'100%'" :flexWrap="'nowrap'" />

		<div class="wizard-container">
			<div class="steps" >
				Step {{ currentStep + 1 }} of {{ steps.length }} - {{ steps[currentStep] }} <span v-if="currentStep > 0" class="accent">({{ isPutwallWave ? 'Putwall' : 'Non-Putwall' }})</span>
			</div>

			<div v-if="currentStep === 0" class="step-content" style="align-items: center;">
				<div style="display: flex; flex-direction: column; gap: 15px;">
						<div class="button-group">
							<div class="toggle-button" :class="{ active: !isPutwallWave }" @click="isPutwallWave = false">
								Non Putwall
							</div>
							<div class="toggle-button" :class="{ active: isPutwallWave }" @click="isPutwallWave = true" >
								Putwall
							</div>
						</div>

				</div>
			</div>


			<DxValidationGroup ref="validationGroup">
				<div v-if="currentStep === 1" class="step-content">
					<div class="card">
						<div class="accent">Wave Settings</div>
						<div>
							<DxCheckBox v-model="configSelection.auto_fill_remainder" text="Auto Fill Remainder" />
							<div class="subtext">Automatically fill wave with items.</div>
						</div>
						<div>
							<DxCheckBox v-model="configSelection.ignore_inventory_requirement" text="Ignore Inventory Requirement" />
							<div class="subtext">Process shipments even when inventory is below minimum levels</div>
						</div>
					</div>

					<div class="card">
						<div class="accent">Filters</div>
						<div>
							<div>Filter by Shipment Types</div>
							<DxTagBox v-model="configSelection.shipment_types" :data-source="shipmentTypes" :search-enabled="true" :show-selection-controls="true"
												:disabled="isPutwallWave"  validation-message-mode="always" 
												:is-valid="configSelection.shipment_types.length > 0">
								<DxValidator>
									<DxCustomRule :validation-callback="({ value }) => Array.isArray(value) && value.length > 0" message="This field is required" />
								</DxValidator>
							</DxTagBox>
						</div>
						<div>
							<div>Destination Countries</div>
							<DxTagBox v-model="configSelection.country_list" :data-source="countries" :search-enabled="true" :show-selection-controls="true"
												:is-valid="configSelection.country_list.length > 0" >
								<DxValidator>
									<DxCustomRule :validation-callback="({ value }) => Array.isArray(value) && value.length > 0" message="This field is required" />
								</DxValidator>
							</DxTagBox>
						</div>
						<div>
							<div>Putwall Selection</div>
							<DxTagBox v-model="configSelection.putwall_list" :data-source="putwalls" :search-enabled="true" :show-selection-controls="true"
												:disabled="!isPutwallWave"
												:is-valid="!isPutwallWave || configSelection.putwall_list.length > 0"
												:validation-error="{ message: 'This field is required' }">
								<DxValidator >
									<DxCustomRule :validation-callback="({ value }) => Array.isArray(value) && value.length > 0" message="This field is required" />
								</DxValidator>
							</DxTagBox>
						</div>
					</div>

					<div class="card">
						<div class="accent">Item Management</div>
						<div>
							<div>{{ "Include Specific Items" }} <span class="accent">{{"(Auto-Fill " + (configSelection.auto_fill_remainder ? 'On' : 'Off') + ")"}}</span></div>
							<div class="subtext">Select specific items to include in the wave. When Auto-Fill is enabled, the system may<br>  include additional shipment orders that do not contain any of the items selected below.</div>
							<DxTagBox v-model="configSelection.contains_item_list" :data-source="filteredItemsForInclude" :search-enabled="true" :show-selection-controls="true"
												display-expr="item_number" value-expr="item_number" item-template="item">
								<template #item="{ data }">
									<div>{{ data.item_number }} <span class="subtext">({{ data.count }}</span> <span class="subtext">Units Allocated)</span></div>
									<div>{{ data.description }}</div>
								</template>
							</DxTagBox>
						</div>
						<div>
							<div>Ignore Specific Items</div>
							<div class="subtext">Shipments containing these items will not be included in the wave.</div>
							<DxTagBox v-model="configSelection.ignore_item_list" :data-source="filteredItemsForExclude" :search-enabled="true" :show-selection-controls="true"
												display-expr="item_number" value-expr="item_number" item-template="item">
								<template #item="{ data }">
									<div>{{ data.item_number }} <span class="subtext">({{ data.count }}</span> <span class="subtext">Units Allocated)</span></div>
									<div>{{ data.description }}</div>
								</template>
							</DxTagBox>
						</div>

					</div>

					<div class="card">
						<div class="accent">Quantity Control</div>
						<div>
							<div>Maximum Shipment Count</div>
							<div class="subtext">Set the maximum number of shipments to include in the wave. Set to 0 for unlimited shipments</div>
							<DxNumberBox v-model="configSelection.shipment_count" :min="0" :max="10000" :step="1" :show-spin-buttons="true" :disabled="isPutwallWave" />
						</div>
					</div>

				</div>
				<DxValidationSummary />
			</DxValidationGroup>


			<div v-if="currentStep === 2">
				<DxLoadPanel :height="500" :width="700" :visible="loadingVisible" :show-indicator="true" :show-pane="true" :shading="true" shading-color="rgba(0,0,0,0.4)" />
				
				<WavePreview :data="createWaveResponse" :config="configSelection" @item-removed="onItemRemoved"/>

			</div>

			<div class="actions">
				<DxButton text="<< Back" :disabled="currentStep === 0" type="default" styling-mode="contained" @click="currentStep--" width="125" height="34" />
				<DxButton v-if="currentStep < steps.length - 1" text="Next >>" type="default" styling-mode="contained" @click="currentStep++" width="125" height="34" />
				<DxButton v-else text="Save Wave" type="default" styling-mode="contained" @click="onCommitWave" width="125" height="34"/>
			</div>
		</div>
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
	import { ref, computed, onMounted, watch, defineProps } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import {  DxTextArea, DxButton, DxSelectBox, DxTagBox, DxCheckBox, DxNumberBox,DxDropDownBox } from 'devextreme-vue';
	import { DxDataGrid, DxColumn, DxMasterDetail, DxSelection, DxFilterPanel, DxHeaderFilter, DxSearchPanel, DxToolbar, DxItem } from 'devextreme-vue/data-grid';
	import { DxLoadPanel } from 'devextreme-vue/load-panel';
	import { DxValidationGroup, DxValidationSummary } from 'devextreme-vue/validation-group';
	import { DxCustomRule, DxValidator, DxRequiredRule } from 'devextreme-vue/validator';
	import { DxTextBox } from 'devextreme-vue/text-box';

	// Child components
	import OrderStats from '@/components/order-stats.vue';
	import WavePreview from '@/components/wave-preview.vue';

	// Composables
	import { useWaveManagement } from '@/composables/useWaveManagement';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { sendCreateWaveCommand, sendEstablishMessage , createWaveResponse, sendCommitWaveCommand, commitWaveResponse, connect, lastError } = useWaveManagement();

	/*=====================================================================
    PROPS
 	=====================================================================*/
	const props = defineProps({
		waveStatsData: {
			type: Object,
			required: true
		}
	});

	/*=====================================================================
    EMITS
  =====================================================================*/
	const emit = defineEmits(['close-popup'])

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	onMounted(() => {
		connect();
		sendEstablishMessage('CREATE-WAVE');
	});

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)

	const validationGroup = ref(null);
	const loadingVisible = ref(false);
	const currentStep = ref(0);
	const steps = ['Select Wave Type','Wave Configuration', 'Wave Review']
	const isPutwallWave = ref(true);
	const countries = ref(['USA', 'CAN', 'GBR']);
	const shipmentTypes = ref([ 'single', 'large_shipment', 'bypass_putwall' ]);
	const putwalls = ref(['Putwall 01', 'Putwall 02', 'Putwall 03', 'Putwall 04', 'Putwall 05', 'Putwall 06', 'Putwall 07', 'Putwall 08', 'Putwall 09', 'Putwall 10', 'Putwall 11', 'Putwall 12']);
	const configSelection = ref({
		contains_item_list: [], //grab shipments that contain an item from the list
		ignore_item_list: [], //ignore shipments that contain an item from the list
		putwall_list: [], //pass the putwalls the user selected, there are 12, the naming needs to be exact as well and it follows the sequence from my example
		auto_fill_remainder: false, //if true it will FIFO fill the remainder if the full wall cannot be filled by the filters
		ignore_inventory_requirement: false, //if true it will allow shipments with items missing completely from inventory to be waved, more so used if they know they are out of an item
		shipment_types: isPutwallWave.value === true ? [ 'putwall'] : [], //NON PUTWALL ONLY, pass the shipment types the user has selected for the wave, the 3 listed are the 3 non putwall types
		shipment_count: 0, //how many shipments you want to be in this wave, any value larger than 0 currently no limit but may create a limit later on in the backend
		country_list: [], 	//these are the only countries, will make configurable later but for now can you hardcode
	});

	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
	// Items available for inclusion (excluding already excluded items)
	const filteredItemsForInclude = computed(() => {
		if (!props.waveStatsData.items) return [];
		return props.waveStatsData.items.filter(item => 
			!configSelection.value.ignore_item_list.includes(item.item_number)
		);
	});

	// Items available for exclusion (excluding already included items)
	const filteredItemsForExclude = computed(() => {
		if (!props.waveStatsData.items) return [];
		return props.waveStatsData.items.filter(item => 
			!configSelection.value.contains_item_list.includes(item.item_number)
		);
	});

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const validateForm = async () => {
		console.log('Validation group ref:', validationGroup.value);
		console.log('Validation group instance:', validationGroup.value?.instance);

		const result = await validationGroup.value.instance.validate();
		console.log('Validation result:', result);
		console.log('Is valid:', result.isValid);
		console.log('Broken rules:', result.brokenRules);
		return result.isValid;
	};

	const onCreateWave = async () => {
		loadingVisible.value = true;
		const isValid = await validateForm();

		if (isValid) {
			console.log('sendCreateWaveCommand',configSelection.value)
			sendCreateWaveCommand(configSelection.value, isPutwallWave.value);
		}else{
			loadingVisible.value = false;
			currentStep.value--;

			// await validateForm();
		}
	};

	const onItemRemoved = (item) => {
	
		// Prevent adding to ignore_item_list if it's in contains_item_list
		const isInContains = configSelection.value.contains_item_list.includes(item.item_number);

		if (!isInContains) {
		configSelection.value.ignore_item_list.push(item.item_number);
		sendCreateWaveCommand(configSelection.value, isPutwallWave.value);
		}else {
			notify({ ...notifyOptions.value, message: 'Cannot ignore item while its in the include list.', type: 'error', displayTime: 5000 });
		}

	};

	const onCommitWave = async () => {
		try {

			if (!createWaveResponse.value) {
				alert('No wave created, cannot commit');
				return;
			}

			loadingVisible.value = true;
			let waveId = createWaveResponse.value?.tmp_wave_id;

			sendCommitWaveCommand(waveId, isPutwallWave.value);

		} catch (error) {
			console.error('Error committing wave:', error);
			loadingVisible.value = false;
		}
	};



	/*=====================================================================
		WATCHERS
	=====================================================================*/
	watch(isPutwallWave, (newVal) => {
		if(newVal) {
			configSelection.value.shipment_types = ['Putwall'];
			configSelection.value.shipment_count = 0;
		} else {
			configSelection.value.shipment_types = [];
			configSelection.value.shipment_count = 100;
			configSelection.value.putwall_list = [];
		}
	});

	watch (createWaveResponse, (newVal) => {
		if(newVal) {
			console.log('Create wave response:', newVal);
			if (newVal.item_counts.length === 0) {
				notify({ ...notifyOptions.value, message: 'No items found for the selected criteria', type: 'error', displayTime: 5000 });
				currentStep.value--;
			}
			loadingVisible.value = false;
		}
	});

	watch(commitWaveResponse, (newVal) => {
		if(newVal) {
			console.log('Commit wave response:', newVal);
			loadingVisible.value = false;
			notify({ ...notifyOptions.value, message: 'Wave Saved Successfully!', type: 'success', displayTime: 5000 });
			emit('close-popup')
		}
	});

	watch(currentStep, (newVal) => {
		if(newVal === 2) {
			onCreateWave();
		}
	});

	watch(lastError, (newVal) => {
		if(newVal) {
			console.log('Last error:', newVal);
			loadingVisible.value = false;
			const errorMessage = typeof newVal === 'string' ? newVal : newVal.message;
			notify({ ...notifyOptions.value, message: errorMessage, type: 'error', displayTime: 5000 });
			currentStep.value--;
		}
	});
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.wrap {
		display: flex;
		flex-direction: row;
		flex:auto;
		gap: 15px;
		color: rgba($base-text-color, .65)!important;
		position: relative;
		padding-bottom: 50px;
		height: 100%;
	}

	.wizard-container {
		display: flex;
		flex: auto;
		flex-direction: column;
		gap: 15px;
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		border-radius: 8px;
		padding: 20px;
		overflow-y: auto;
		
	}


	.card {
		display: flex;
		flex: auto;
		flex-direction: column;
		gap: 15px;
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		border-radius: 8px;
		padding: 20px;
	}
	
	.accent {
		color: $base-accent;
	}

	.actions {
		margin-top: 20px;
		display: flex;
		justify-content: end;
		// flex: auto;
    align-items: flex-end;
		gap: 20px;
		position: absolute;
    bottom: 0;
    /* background: #2d2c36; */
    // padding: 5px 10px;
    // border-radius: 8px;
    // background-color: rgb(45 44 54);
    // border: 1px solid rgb(81, 81, 89);
    width: 100%;
    align-self: center;
		left: 0;
		right: 0;
	}

	.steps {
		font-size: 16px;
		// font-weight: bold;
	}
	.subtext {
		font-size: 12px;
		color: rgba($base-text-color, .35)!important;
		padding: 5px 0;

	}

	.step-content {
		display: flex;
		flex-direction: column;
		gap: 15px;
		flex: auto;
		justify-content: center;

	}

	.button-group {
		display: flex;
		gap: 10px;

	}

	.toggle-button {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		border-radius: 8px;
		padding: 20px;
		cursor: pointer;
		user-select: none;
		transition: all 0.2s ease;
		width: 180px;
		text-align: center; 
	}



	.toggle-button:hover {
		border-color: rgba($base-accent, .3);
	}

	.toggle-button.active {
		background-color: $base-accent;
		border-color: $base-accent;
		color: white;
	}

	// DX Data Grid Overrides
	::v-deep .dx-datagrid {
		color: rgba($base-text-color, 0.75)!important;
	}

	.grid-border {
		border-radius: 8px;
		border: 1px solid $base-border-color;
		overflow: hidden;
	}

	::v-deep .dx-header-row {
		background-color: rgba($base-bg-dark, .65);
		border-radius: 8px;
	}

	::v-deep .dx-empty-message {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}

	::v-deep .dx-datagrid-rowsview .dx-row {
		border: none;
	}
	::v-deep .dx-data-row {
		cursor: pointer;
	}
	
	::v-deep .dx-datagrid-rowsview .dx-master-detail-row > .dx-master-detail-cell {
		padding: 0!important;
	}

	::v-deep .dx-datagrid-header-panel {
		padding: 10px!important;
	}

	::v-deep .dx-datagrid-search-panel {
		margin: 0!important;
	}

	::v-deep .dx-checkbox-container {
		color: rgba($base-text-color, .65)!important;
	}

	::v-deep .dx-texteditor {
		min-width: 370px!important;
		width: 100%;
		max-width: 370px!important;
	}
	
	::v-deep .dx-popup-content-scrollable {
		overflow-x: hidden!important;
	}
</style>
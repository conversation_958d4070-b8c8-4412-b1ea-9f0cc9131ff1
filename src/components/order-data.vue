<template>
  <DxLoadPanel :visible="loading" :container="'.content-block'" :position="{ of: '.content-block' }"/>
  <div class="order-data-grid">
    <div class="filter-controls">
      <!-- Required filters -->
      <div class="filter-group">
        <div class="filter-item">
          <div>Date</div>
          <DxTextBox :value="date" styling-mode="underlined"  :disabled="true" />
          <!-- <DxDateBox :value="date" type="date" :disabled="true"  styling-mode="underlined" display-format="yyyy-MM-dd" /> -->
        </div>
        <div class="filter-item">
          <div>Satus</div>
          <DxSelectBox :items="statusOptions" v-model:value="filters.status" display-expr="text" value-expr="value" styling-mode="underlined" />
        </div>

        <div class="filter-item">
          <div>Container Type</div>
          <DxSelectBox :items="containerTypeList" v-model:value="filters.container_type" styling-mode="underlined" :show-clear-button="true" />
        </div>
        <div class="filter-item">
          <div>Shipping Carrier</div>
          <DxSelectBox :items="shippingCarriersList" v-model:value="filters.shipping_carrier" styling-mode="underlined" :show-clear-button="true" />
        </div>
        <div class="filter-item">
          <div>Order Start Station</div>
          <DxSelectBox :items="orderStartStationList" v-model:value="filters.order_start_station_name" styling-mode="underlined" :show-clear-button="true" />
        </div>
        <div class="filter-item">
          <div>Pack Station</div>
          <DxSelectBox :items="packStationList" v-model:value="filters.pack_station_name" styling-mode="underlined" :show-clear-button="true" />
        </div>
        <div class="filter-item">
          <DxButton text="Search" type="default" styling-mode="contained"  @click="fetchData" />
        </div>
      </div>
    </div>

    <DxDataGrid :height="'100%'" :data-source="gridData || []" :row-alternation-enabled="true" :column-hiding-enabled="false"
      :show-borders="true" :word-wrap-enabled="true" :column-auto-width="true" :no-data-text="noDataText" @exporting="onExporting">

      <DxHeaderFilter :visible="true"/>
      <DxFilterPanel :visible="true"/>
      <!-- <DxScrolling mode="virtual" /> -->
      <DxExport :enabled="true" />
      <!-- <DxColumn data-field="wave_id" caption="Wave ID" /> -->
      <DxColumn data-field="wms_order_number" caption="Order Number" /> 
      <DxColumn data-field="lpn_barcode" caption="Barcode" />
      <DxColumn data-field="customer_identifier" caption="Customer" />
      <DxColumn data-field="shipping_carrier" caption="Carrier" />
      <DxColumn data-field="container_type" caption="Container" />
      <DxColumn data-field="order_start_station_name" caption="Start Station" />
      <DxColumn data-field="pack_station_name" caption="Pack Station" />
      <DxColumn data-field="created_datetime" caption="Imported Date" />
      <DxColumn data-field="order_started_datetime" caption="Order Started" data-type="datetime" />
      <DxColumn data-field="pack_complete_datetime" caption="Pack Completed" data-type="datetime" />
    </DxDataGrid>
  </div>
</template>

<script setup>

  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import {defineProps, ref, reactive, onMounted, watch } from 'vue';

  // UI components & utilities
  import { DxLoadPanel } from 'devextreme-vue/load-panel';
  import { DxButton } from 'devextreme-vue/button';
  import { DxDateBox } from 'devextreme-vue/date-box';
  import { DxSelectBox } from 'devextreme-vue/select-box';
  import { exportDataGrid } from 'devextreme/excel_exporter';
  import { DxDataGrid, DxColumn, DxScrolling, DxHeaderFilter,  DxFilterPanel, DxExport} from 'devextreme-vue/data-grid';
	import { Workbook } from 'exceljs';
	import saveAs from 'file-saver';
  import { DxTextBox } from 'devextreme-vue';

	// Composables
	import { useRestAPI } from '@/composables/useRestAPI'; 

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const api = useRestAPI()

  /*=====================================================================
    PROPS & EMITS
  =====================================================================*/
  const props = defineProps({
    date: { type: Date, required: false, default: () => new Date() },
    orderStartStationList: { type: Array, required: true, default: () => [] },
    packStationList: { type: Array, required: true, default: () => [] },
    shippingCarriersList: { type: Array, required: true, default: () => [] },
    containerTypeList: { type: Array, required: true, default: () => [] },
    refreshTrigger: { type: Number, default: 0 } 
  });

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
  const gridData = ref([]);
  const noDataText = ref('No data to display');
  const loading = ref(false);
  const statusOptions = [
    { text: 'Open', value: 'NotStarted' },
    { text: 'In Progress', value: 'InProgress' },
    { text: 'Complete', value: 'Complete' }
  ];
  const filters = reactive({
    date: props.date,
    status: 'Complete',
    container_type: null,
    shipping_carrier: null,
    order_start_station_name: null,
    pack_station_name: null
  });

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
  // Format date for API
  const formatDate = (date) => {
    if (!date) return '';
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
  };

  // Fetch data from API
  const fetchData = async () => {
    loading.value = true;
    
    try {
      // Build query parameters
      const params = {
        Date: props.date,
        Status: filters.status
      };
      
      // Add optional parameters if they have values
      if (filters.container_type) params.container_type = filters.container_type;
      if (filters.shipping_carrier) params.shipping_carrier = filters.shipping_carrier;
      if (filters.order_start_station_name) params.order_start_station_name = filters.order_start_station_name;
      if (filters.pack_station_name) params.pack_station_name = filters.pack_station_name;
      
      // Make API call
      const response = await api.get(`/Stats/DashboardDetails/`, params);
      gridData.value = response.data;
      loading.value = false;

    } catch (error) {
      console.error('Error fetching data:', error);
      gridData.value = [];
      noDataText.value = error.response?.data?.message || 'Error loading data';
    } finally {
      loading.value = false;
    }
  };

  const onExporting = ( e) => {
    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Order Data');

    exportDataGrid({
        component: e.component,
        worksheet: worksheet,
        autoFilterEnabled: true,
    }).then(() => {
        workbook.xlsx.writeBuffer().then((buffer) => {
            saveAs(new Blob([buffer], { type: 'application/octet-stream' }), 'DataGrid.xlsx');
        });
    });

    e.cancel = true;
	};

  /*=====================================================================
    WATCHERS 
  =====================================================================*/
  watch(() => props.refreshTrigger, () => {
    fetchData();
  });

  /*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
  onMounted(() => {
    fetchData();
  });
</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

  .order-data-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
    color: rgba($base-text-color, .75)!important;
    padding: 10px;
  }

  .filter-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-radius: 4px;
  }

  .filter-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .filter-item {
    display: flex;
    flex-direction: column;
    min-width: 200px;
    justify-content: flex-end; 
  }

  // DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;
	}
	
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}
	

	// DX Button Overrides
	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 32px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 32px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon ,.dx-button-content {
		min-width: unset!important;
		height: unset!important;
	}

  // DX Data Grid Overrides
  ::v-deep(.dx-datagrid-header-panel) {
		padding: 0!important;
		margin-bottom: 5px;;
	}
	::v-deep(.dx-datagrid ){
		color: rgba($base-text-color, 0.75)!important;
	}
	::v-deep(.dx-datagrid .dx-data-row td) {
		word-break: break-word;
		white-space: normal;
	}

	::v-deep(.dx-header-row){
		background-color: rgba($base-bg-dark, .65);
	}

	::v-deep(.dx-empty-message) {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}


	::v-deep(.dx-button-has-icon ){
		width: unset!important;
		height: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 10px !important;
		width: unset;
		height: unset;
	}
	
	
	::v-deep(.dx-numberbox-spin-container){
		width:10px
	}
	
	::v-deep(.dx-texteditor .dx-texteditor-buttons-container:last-child>.dx-numberbox-spin-container:last-child){
		margin-inline-end: unset;
	}
</style>

<template>
  <div class="container">
    <div class="header">
      <!-- <label style="font-size: large;">{{ groupName }} </label> -->
      <div style="display: flex; justify-items: center; align-items: center;">
        <DxTextBox styling-mode="filled" v-model="groupName" @value-changed="updateName"></DxTextBox>
        <span style="font-size: small; color: #3aafa9; display: block;">({{groupCounts.display_value}} Orders)</span>
      </div>
      <DxButton text="Delete" class="delete-button" @click="$emit('delete-group', groupName)" styling-mode="text" type="default"></DxButton>
    </div>
    
    <div>
      <label>Group Description</label>
      <div>
        <DxTextBox styling-mode="filled" v-model="groupDescription" @value-changed="updateDescription"></DxTextBox>
      </div>
    </div>
   
    <!-- Filter Conditions -->
    <div>
      <div class="row">
        <label style="font-size: large;">Filter Conditions</label>
        <div style="flex: auto;"></div>
        <DxButton text="New" @click="isAddingCondition = true" styling-mode="contained" type="default"></DxButton>
      </div>	
      <!-- Add Condition Form -->
      <AddFilterConditionForm
        v-if="isAddingCondition"
        :condition-types="conditionTypes"
        :conditions-by-type="conditionsByType"
        @save="handleAddCondition"
        @cancel="isAddingCondition = false"
      />
      
      <!-- Conditions List -->
      <FilterConditionsList
        :conditions="activeConditions"
        @remove="handleRemoveCondition"
      />
    </div>
    
    <!-- Pick Zones -->
    <div>
      <label style="font-size: large;">Pick Mods</label>
      <PickZonesGrid
        :zones="availableZones"
        @toggle-zone="handleToggleZone"
      />
    </div>
  </div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import { ref, watch, onMounted, defineProps, defineEmits } from 'vue';
  
  // UI components & utilities
  import { DxButton } from 'devextreme-vue/button';
  import { DxTextBox } from 'devextreme-vue/text-box';
  
  // Child components
  import AddFilterConditionForm from './AddFilterConditionForm.vue';
  import FilterConditionsList from './FilterConditionsList.vue';
  import PickZonesGrid from './PickZonesGrid.vue';

  /*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
  // No composables used in this component

  /*=====================================================================
    PROPS & EMITS
  =====================================================================*/
  const props = defineProps({
    group: Object,
    availableConditions: Array,
    activeConditions: Array,
    availableZones: Array,
    conditionTypes: Array,
    conditionsByType: Object,
    groupCounts: Object
  });

  const emit = defineEmits([
    'update-name',
    'update-desc', 
    'add-condition', 
    'remove-condition', 
    'toggle-zone', 
    'delete-group'
  ]);

  /*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
  onMounted(() => {
    if (!props.group) return;
    groupName.value = props.group?.filter_group_name;
    groupDescription.value = props.group.filter_group_desc;
  });

  /*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
  const groupName = ref('');
  const groupDescription = ref('');
  const isAddingCondition = ref(false);

  /*=====================================================================
    WATCHERS 
  =====================================================================*/
  watch(() => props.group, (newGroup) => {
    groupName.value = newGroup?.filter_group_name;
    groupDescription.value = newGroup?.filter_group_desc;
  });

  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
  const updateName = () => {
    if (groupName.value?.trim() && groupName.value !== props.group?.filter_group_name) {
      emit('update-name', groupName.value);
    }
  };
  
  const updateDescription = () => {
    if (groupDescription.value?.trim() && groupDescription.value !== props.group?.filter_group_desc) {
      emit('update-desc', groupDescription.value);
    }
  };

  const handleAddCondition = (conditionData) => {
    emit('add-condition', conditionData);
    isAddingCondition.value = false;
  };

  const handleRemoveCondition = (conditionId) => {
    emit('remove-condition', conditionId);
  };

  const handleToggleZone = (zone) => {
    emit('toggle-zone', zone);
  };
</script>

<style scoped lang="scss">
  @import "@/themes/generated/variables.additional.scss";

  .container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 auto;
    overflow-y: auto;
    letter-spacing: 0.3px;
  }

  .row {
    display: flex;
    gap: 20px;
    flex-shrink: 0; 
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header h2 {
    font-size: 1.25rem;
    font-weight: 400;
    margin: unset;
  }
  
  .delete-button {
    color: #ef4444; 
    transition: color 0.2s;
    background-color: unset;
    box-shadow: unset;
  }

  .delete-button:hover {
    color: #f16464;
    background-color: unset;
  }

  /* DX Controls Override  */
  .dx-texteditor {
    border: 0px;
    min-width: 120px;
    border: 1px solid $base-border-color;
    border-radius: 5px;
    background-color: rgba($base-bg-dark, 0.5);
  }
  
  .dx-texteditor.dx-editor-filled.dx-state-disabled {
    background-color: rgba($base-bg-dark, 0.5);
    opacity: 1;
  }
  
  .dx-texteditor::before {
    content: none;
  }
  
  .dx-texteditor::after {
    content: none;
  }
  
  .custom-button.dx-button-has-icon {
    min-width: unset !important;
  }

  ::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
    font-size: 8px !important;
    width: unset;
    height: unset;
  }
</style>
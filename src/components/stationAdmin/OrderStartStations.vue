<template>
  <div class="container">
		<!-- <div class="header">
			<h2>Order Start <br>Stations</h2>
		</div> -->


		<div class="item" v-for="station in orderStartStations" v-bind:key="station.id_kvkorderstartstation">
			<div class="row">
				<div>{{station.order_start_station_name}}</div>
				<div style="flex: auto;"></div>
			
				<div><DxSwitch v-model='station.active'  @value-changed="(e) => toggleStation(station.order_start_station_name, e.value)"/></div>
			</div>
			<div class="row" style="align-items: center;">
				<div style="">Filter Group:</div>
				<div style="flex: auto;"></div>
				<DxSelectBox v-model='station.filter_group_name' :dataSource='filterGroups' value-expr='filter_group_name' display-expr='filter_group_name' styling-mode='contained'
				@value-changed="updateStationFilterGroup(station.order_start_station_name, station.filter_group_name)"/>
			</div>
		</div>

		<!-- <div v-if="orderStartStations.length < 3" class="item"></div>
		<div v-if="orderStartStations.length < 4"  class="item"></div> -->
	
	</div>
</template>

<script setup>
	import { defineProps, defineEmits } from 'vue';
	import { DxButton } from 'devextreme-vue/button';
	import { DxSwitch } from 'devextreme-vue/switch'
	import { DxSelectBox } from 'devextreme-vue/select-box';

	/* 
		PROPS FROM PARENT  
	*/
	const props = defineProps({
		filterGroups: { type: Array, required: true, default: () => [] },
		orderStartStations: { type: Array, required: true, default: () => [] }
	});

	/* 
		EMITS 
	*/
	const emit = defineEmits(['toggle-station', 'update-filter-group']);

	/* 
		COMPUTED PROPERTIES  
	*/

	/* 
		FUNCTIONS
	*/
	const toggleStation = (stationName, active) => {

		emit('toggle-station', { stationName, active });
	};

	// Update station filter group
	const updateStationFilterGroup = (stationName, filterGroupName) => {
		console.log(updateStationFilterGroup, stationName, filterGroupName)
		emit('update-filter-group', { stationName, filterGroupName });
	};
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	/* color classes */
	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}

	.container {
		flex: none!important;
		flex-direction: row!important;
		height: unset;
		overflow:visible;
		
	}

	.row {
		display: flex;
		gap: 10px;
		flex-shrink: 0;
	
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.header h2 {
		font-size: 1.25rem;
		font-weight: 400;
		margin: unset;
	}

	.item{
		display: flex;
		flex-direction: column;
		flex: auto;
		gap: 5px;
		padding: 20px;
		background-color: rgba($base-bg-dark, 0.5);
		border-radius: 8px;
		box-shadow: 0 10px 6px -6px  $base-bg-dark;
		max-width: 320px;
		border: 1px solid $base-border-color;
		
	}

	/* DX Controls Override  */
	.dx-texteditor {
		border: 0px;
		min-width: 120px;
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: $base-bg-dark;

	}
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: $base-bg-dark;
		opacity: 1;
	}
	.dx-texteditor::before {
		content: none;
	}
	.dx-texteditor::after {
		content: none;
	}
	
	.dx-button {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}

	.custom-button.dx-button-has-icon  {
		min-width: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 8px !important;
		width: unset;
		height: unset;
	}
</style>
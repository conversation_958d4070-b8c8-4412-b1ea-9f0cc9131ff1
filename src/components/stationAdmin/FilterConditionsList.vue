<template>
  <div class="container">
    <div v-for="condition in conditions" :key="condition.id_kvkorderstartactiveconditions">
      <div class="tag">
        <span >{{ condition.condition_name }}:</span>
        <span  v-if="condition.condition_is_flag" class="accent">
          FLAG
        </span>
        <span v-else class="accent">{{ condition.condition_value }}</span>
				<div style="flex:auto"></div>
				<!-- <button class="x-delete" @click="$emit('remove', condition.id_kvkorderstartactiveconditions)">✕</button>
				<DxButton text="x" class="x-delete"  @click="$emit('create-new')" styling-mode="outlined" type="default" ></DxButton> -->
				<DxButton  @click="$emit('remove', condition.id_kvkorderstartactiveconditions)" class="custom-button" :width="24" :height="24" type='default' styling-mode='outlined' icon='clear'  />
      </div>
      
    </div>
    
    <div v-if="conditions.length === 0">
      No conditions. Click "New" to add one.
    </div>
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
  =====================================================================*/
	// Vue core
	import { ref, computed, defineProps, defineEmits } from 'vue';
	
	// UI components & utilities
	import { DxButton } from 'devextreme-vue/button';
	
	/*=====================================================================
    PROPS & EMITS
  =====================================================================*/
	const props = defineProps({
		conditions: { type: Array, required: true, default: () => []  },
	});

	const emit = defineEmits(['remove']);

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.container {
		display: flex;
		flex-wrap: wrap;
		flex-direction: row!important;
		justify-content: start;
		align-items: auto;
		align-content: start;
		gap: 20px;
		// height: 50%;
		// overflow-y: auto;
		letter-spacing: 0.3px;
		margin: 20px 0;
	}
	.row {
		display: flex;
		gap: 20px;
		flex-shrink: 0; 

	}

	.accent {
		color: $base-accent;
	}
	.bg-accent {
		background-color: $base-accent;
	}


	.tag{
		display: flex;
		gap: 10px;
		padding: 10px 20px;
		border-radius: 8px;
		align-content: center;
		background-color: rgba($base-bg-dark, 0.5);
		border: 1px solid $base-border-color;
		width: 220px;
		align-items: center;
	}

	.x-delete {
		color: $base-accent; 
		// background-color: #f9d3d3; 
		border-radius: 9999px; 
		width: 1.5rem; 
		height: 1.5rem; 
		display: flex; 
		align-items: center; 
		justify-content: center; 
		transition: color 0.2s ease-in-out; 
		// border: unset;
		cursor: pointer;
		padding-block: unset; 
     padding-inline-start: unset;
   padding-inline-end: unset;
	}
	/* DX Controls Override  */
	.dx-texteditor {
		border: 0px;
		min-width: 120px;
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, 0.5);

	}
	.dx-texteditor.dx-editor-filled.dx-state-disabled {
		background-color: rgba($base-bg-dark, 0.5);
		opacity: 1;
	}
	.dx-texteditor::before {
		content: none;
	}
	.dx-texteditor::after {
		content: none;
	}
	.dx-button-has-text {
    min-width: unset;
	}

	.custom-button.dx-button, .dx-button.dx-button-default , .dx-item-content.dx-button.dx-button-default {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);

	}
	.custom-button {
		border-radius: 5px;
		box-shadow: none;
		height: 31px;
		margin-bottom: 1px;
		color: $base-text-color!important; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
	}
	.custom-button.dx-button-has-icon  {
		min-width: unset!important;
	}

	::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
		font-size: 8px !important;
		width: unset;
		height: unset;
	}
</style>
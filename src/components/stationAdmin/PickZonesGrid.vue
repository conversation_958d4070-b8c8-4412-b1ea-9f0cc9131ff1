<template>
  <div class="grid-container">
    <fieldset v-for="(row, rowIndex) in chunkedZones" :key="rowIndex">
      <legend>Mod {{ rowIndex + 1 }}</legend>
      <div class="grid">
        <div 
          v-for="zone in row" 
          :key="zone.order_start_pick_zone" 
          :class="zone?.id_kvkorderstartactivepickzones != null ? 'active' : 'inactive'"
        >
          <DxButton 
            styling-mode="outlined" 
            type="default" 
            :text='"Aisle " + zone.order_start_pick_zone'  
            @click="$emit('toggle-zone', zone)"
          >
          </DxButton>
        </div>
      </div>
    </fieldset>
  </div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import { defineProps, defineEmits, computed } from 'vue';
  
  // UI components & utilities
  import { DxButton } from 'devextreme-vue/button';

  /*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
  // No composables used in this component

  /*=====================================================================
    PROPS & EMITS
  =====================================================================*/
  const props = defineProps({
    zones: { type: Array, required: true, default: () => [] },
  });

  const emit = defineEmits(['toggle-zone']);

  /*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
  const chunkedZones = computed(() => {
    const chunkSize = 4;
    return props.zones.reduce((rows, zone, index) => {
      if (index % chunkSize === 0) rows.push([]);
      rows[rows.length - 1].push(zone);
      return rows;
    }, []);
  });

  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
  const isZoneActive = (zone) => {
    return zone?.id_kvkorderstartactivepickzones != null;
  };
</script>

<style scoped lang="scss">
  @import "@/themes/generated/variables.additional.scss";

  .container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 auto;
    overflow-y: auto;
    letter-spacing: 0.3px;
  }

  .grid-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 20px 0;
  }

  .grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    flex-shrink: 0; 
  }
  
  .active {
    &::v-deep(.dx-button) {
      background-color: $base-accent;
      color: $base-bg-dark;
    }
  }
  
  .inactive {
    /* Empty but keeping for semantic clarity */
  }

  fieldset {
    border: 1px solid $base-border-color;
    border-radius: 10px;
  }

  legend {
    font-weight: bold;
  }

  /* DX Controls Override  */
  .dx-button {
    border-radius: 5px;
    box-shadow: none;
    height: 40px;
    margin-bottom: 1px;
    width: 100%;
    color: rgba($base-text-color, .55);
  }
</style>
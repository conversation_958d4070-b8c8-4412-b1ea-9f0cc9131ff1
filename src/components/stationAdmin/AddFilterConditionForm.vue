<template>
  <div class="container">
    <div class="row">
      <div>
        <label>Filter Type</label>
        <DxSelectBox v-model='selectedType' :dataSource='conditionTypes' styling-mode='contained'/>
      </div>
      
      <div v-if="showValueInput">
        <label>Value <span v-if="isWildcardSearch" class="filter-hint">(wildcards: *)</span></label>
        <DxTextBox styling-mode="filled" v-model="conditionValue" valueChangeEvent='keyup' :mask="inputMask" validation-message-position="top" />
      </div>
      
      <div class="row">
        <DxButton text="Cancel" @click="$emit('cancel')" styling-mode="outlined" type="default"></DxButton>
        <DxButton text="Save" @click="saveCondition" :disabled="!isValid" styling-mode="contained" type="default"></DxButton>
      </div>
    </div>
  </div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import { ref, computed, defineProps, defineEmits } from 'vue';
  
  // UI components & utilities
  import { DxButton } from 'devextreme-vue/button';
  import { DxTextBox } from 'devextreme-vue/text-box';
  import { DxSelectBox } from 'devextreme-vue/select-box';
  
  /*=====================================================================
    PROPS & EMITS
  =====================================================================*/
  const props = defineProps({
    conditionTypes: { type: Array, required: true, default: () => [] },
    conditionsByType: { type: Object, required: true, default: () => ({}) }
  });
  
  const emit = defineEmits(['save', 'cancel']);
  
  /*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
  const selectedType = ref('');
  const conditionValue = ref('');
  
  /*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
  const selectedCondition = computed(() => {
    if (!selectedType.value || !props.conditionsByType || !props.conditionsByType[selectedType.value]) { 
      return null; 
    }
    return props.conditionsByType[selectedType.value][0];
  });
  
  const isFlag = computed(() => selectedCondition.value?.condition_is_flag === true);
  
  const isWildcardSearch = computed(() => selectedCondition.value?.condition_wildcard_search === true);
  
  const showValueInput = computed(() => selectedCondition.value && !isFlag.value);
  
  const isValid = computed(() => selectedType.value && (isFlag.value || conditionValue.value.trim()));
  
  const inputMask = computed(() => {
    switch (selectedType.value) {
      //had to remove mask users need to be able to filter on conditions like 601* to be able to get all orders that need to go to the chicago land area for example
      //mask was preventing this and backend will handle rejection if improper format
      //case 'ZIP Code': return '00000';  // US Zip Code format
      case 'Order Date': return '0000-00-00'; // YYYY-MM-DD
      default: return ''; // No mask by default
    }
  });
  
  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
  const saveCondition = () => {
    if (!isValid.value) return;
    emit('save', { type: selectedType.value, value: conditionValue.value });
    selectedType.value = '';
    conditionValue.value = '';
  };
</script>

<style scoped lang="scss">
  @import "@/themes/generated/variables.additional.scss";
  
  .container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 auto;
    overflow-y: auto;
    letter-spacing: 0.3px;
    border-radius: 8px;
    border: 1px solid;
    padding: 10px;
    border-color: $base-border-color;
    background-color: rgba($base-bg-dark, .3);
    height: unset;
    margin: 20px 0;
  }

  .row {
    display: flex;
    gap: 20px;
    flex-shrink: 0; 
    align-items: end;
  }

  /* DX Controls Override  */
  .dx-texteditor {
    border: 0px;
    min-width: 120px;
    border: 1px solid $base-border-color;
    border-radius: 5px;
    background-color: $base-bg-dark;
  }
  
  .dx-texteditor.dx-editor-filled.dx-state-disabled {
    background-color: $base-bg-dark;
    opacity: 1;
  }
  
  .dx-texteditor::before {
    content: none;
  }
  
  .dx-texteditor::after {
    content: none;
  }
  
  .dx-button {
    border-radius: 5px;
    box-shadow: none;
    height: 31px;
    margin-bottom: 1px;
    color: $base-text-color; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
  }

  .custom-button.dx-button-has-icon {
    min-width: unset !important;
  }

  ::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
    font-size: 8px !important;
    width: unset;
    height: unset;
  }
</style>
<template>
  <div class="container" style="height: calc(100vh - 410px);">
    <div class="header">
      <h2>Filter Groups</h2>
      <DxButton text="New" @click="popupNewGroupVisible = true" styling-mode="contained" type="default"></DxButton>

      <DxPopup 
        ref="popupNewGroup" 
        title="Create New Group" 
        container='.dx-viewport' 
        :show-title="true" 
        :hide-on-outside-click="false" 
        :show-close-button="true"
        :visible="popupNewGroupVisible"
        :onHidden='onPopupHidden'
        width="450"
        height="320">
        <DxPosition my="center" at="center" of=".content-block" />
        <template #content>
          <NewGroupForm @create="handleCreateGroup" style="flex: auto;" />
        </template>
      </DxPopup>
    </div>

    <div class="search-sort-controls">
      <div class="search-box">
        <DxTextBox styling-mode="filled" v-model="searchQuery" valueChangeEvent="input" placeholder="Search by name..."></DxTextBox>
      </div>
      <div class="sort-controls">
        <DxButton 
          styling-mode="text" 
          class="sort-button" 
          :text="sortField === 'filter_group_name' ? (sortDirection === 'asc' ? 'Name ↑' : 'Name ↓') : 'Name'" 
          @click="toggleSort('filter_group_name')" 
          :type="sortField === 'filter_group_name' ? 'default' : 'normal'">
        </DxButton>
        <DxButton 
          styling-mode="text" 
          class="sort-button" 
          :text="sortField === 'altered_datetime' ? (sortDirection === 'asc' ? 'Date ↑' : 'Date ↓') : 'Date'" 
          @click="toggleSort('altered_datetime')" 
          :type="sortField === 'altered_datetime' ? 'default' : 'normal'">
        </DxButton>
      </div>
    </div>

    <div class="group-list">
      <div 
        v-for="group in sortedAndFilteredGroups" 
        :key="group.id_kvkorderstartfiltergroup" 
        class="group-item card"
        :class="{ 'active': activeGroupId === group.id_kvkorderstartfiltergroup }"
        @click="$emit('select-group', group.id_kvkorderstartfiltergroup)">

        <div class="group-header">
          <h5>{{ group.filter_group_name }}</h5>
          <div>{{ formatDate(group.altered_datetime) }}</div>
        </div>
        <div class="group-desc">
          {{ group.filter_group_desc }}
        </div>
      </div>

      <div v-if="sortedAndFilteredGroups.length === 0" class="no-groups">
        {{ filterGroups.length === 0 ? 'No filter groups. Click "New" to create one.' : 'No matching filter groups found.' }}
      </div>
    </div>
  </div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import { defineProps, defineEmits, ref, computed } from 'vue';
  
  // UI components & utilities
  import { DxButton } from 'devextreme-vue/button';
  import { DxTextBox } from 'devextreme-vue/text-box';
  import { DxPopup, DxPosition, DxToolbarItem } from 'devextreme-vue/popup';
  
  // Child components
  import NewGroupForm from '@/components/stationAdmin/NewGroupForm.vue';

  /*=====================================================================
    PROPS & EMITS
  =====================================================================*/
  const props = defineProps({
    filterGroups: { type: Array, required: true, default: () => [] },
    activeGroupId: { type: [Number, String, null], default: null }
  });

  const emit = defineEmits(['select-group', 'create-group', 'delete-group']);

  /*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
  const searchQuery = ref('');
  const sortField = ref('filter_group_name'); // Default sort by name
  const sortDirection = ref('asc'); // Default ascending
  const popupNewGroupVisible = ref(false);

  /*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/
  const sortedAndFilteredGroups = computed(() => {
    // First filter by search query
    let filtered = props.filterGroups;
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase().trim();
      filtered = filtered.filter(group => 
        group.filter_group_name.toLowerCase().includes(query)
      );
    }

    // Then sort
    return [...filtered].sort((a, b) => {
      let valueA, valueB;
      
      if (sortField.value === 'filter_group_name') {
        valueA = a.filter_group_name.toLowerCase();
        valueB = b.filter_group_name.toLowerCase();
      } else if (sortField.value === 'altered_datetime') {
        valueA = new Date(a.altered_datetime || 0).getTime();
        valueB = new Date(b.altered_datetime || 0).getTime();
      }
      
      if (sortDirection.value === 'asc') {
        return valueA > valueB ? 1 : -1;
      } else {
        return valueA < valueB ? 1 : -1;
      }
    });
  });

  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
  const toggleSort = (field) => {
    if (sortField.value === field) {
      // Toggle direction if same field
      sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
    } else {
      // Set new field and reset to ascending
      sortField.value = field;
      sortDirection.value = 'asc';
    }
  };

  const handleCreateGroup = async(data) => {
    popupNewGroupVisible.value = false;
    emit('create-group', data);
  };
  
  const onPopupHidden = async() => {
    popupNewGroupVisible.value = false;
  };

  const formatDate = (isoString) => {
    if (!isoString) return "";

    const date = new Date(isoString);
    return date.toLocaleString("en-US", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    }).replace(",", "");
  };
</script>

<style scoped lang="scss">
  @import "@/themes/generated/variables.additional.scss";

  /* color classes */
  .accent {
    color: $base-accent;
  }
  
  .bg-accent {
    background-color: $base-accent;
  }

  .container {
    min-width: 330px;
    max-width: 330px;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header h2 {
    font-size: 1.25rem;
    font-weight: 400;
    margin: unset;
  }

  .search-sort-controls {
    display: flex;
  }

  .search-box {
    width: 100%;
  }

  .search-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid $base-border-color;
    border-radius: 4px;
    background-color: rgba($base-bg-dark, 0.3);
    color: $base-text-color;
  }

  .search-input:focus {
    outline: none;
    border-color: $base-accent;
  }

  .sort-controls {
    display: flex;
    align-items: center;
  }

  .sort-label {
    font-size: 0.875rem;
    color: rgba($base-text-color, 0.7);
  }

  .group-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    overflow-y: auto;
    overflow-x: overlay;
    flex: 1;
    padding-right: 5px;
  }
  
  .group-item {
    padding: 12px 20px;
    border: 1px solid $base-border-color;
    cursor: pointer;
    border-radius: 8px;
    align-content: center;
    background-color: rgba($base-bg-dark, 0.5);
  }

  .group-item:hover {
    border-color: $base-accent;
    transition: border-color .5s;
  }

  .group-item.active {
    border-color: $base-accent;
  }

  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .group-header h5 {
    font-weight: 500;
    margin: unset;
  }

  .delete-button {
    color: #ef4444;
    transition: color 0.2s;
    background-color: unset;
    box-shadow: unset;
  }

  .delete-button:hover {
    color: #f16464;
    background-color: unset;
  }

  .group-desc {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: rgba($base-text-color, .5);
  }

  .no-groups {
    text-align: center;
    padding: 1rem;
    color: rgba($base-text-color, .7);
  }

  /* DX Controls Override  */
  .dx-texteditor {
    border: 0px;
    min-width: 120px;
    border: 1px solid $base-border-color;
    border-radius: 5px;
    background-color: rgba($base-bg-dark, 0.5);
  }
  
  .dx-texteditor.dx-editor-filled.dx-state-disabled {
    background-color: rgba($base-bg-dark, 0.5);
    opacity: 1;
  }
  
  .dx-texteditor::before {
    content: none;
  }
  
  .dx-texteditor::after {
    content: none;
  }

  .custom-button.dx-button-has-icon {
    min-width: unset !important;
  }

  ::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
    font-size: 8px !important;
    width: unset;
    height: unset;
  }
</style>
<template>
  <div class="container">
    <div>
      <div>Group Name</div>
      <DxTextBox styling-mode="filled" v-model="groupName" value-change-event="keyup">
        <DxValidator><DxRequiredRule /></DxValidator>
      </DxTextBox>
    </div>

    <div>
      <div>Group Description</div>
      <!-- <DxTextBox styling-mode="filled" v-model="groupDesc" value-change-event="keyup"></DxTextBox> -->
      <DxTextArea
        v-model='groupDesc'
        value-change-event="keyup"
        :height='75'
        :max-length='1000'
        validationMessageMode='always'
      />
    </div>

    <div>
      <DxButton text="Save" type="default" styling-mode="contained" @click="createGroup" />
    </div>
  </div>
</template>

<script setup>
  /*=====================================================================
    IMPORTS
  =====================================================================*/
  // Vue core
  import { ref, computed, defineProps, defineEmits } from 'vue';
  
  // UI components & utilities
  import { DxButton } from 'devextreme-vue/button';
  import { DxTextBox } from 'devextreme-vue/text-box';
  import { DxTextArea } from 'devextreme-vue/text-area';
  import { DxRequiredRule, DxValidator } from 'devextreme-vue/validator';

  /*=====================================================================
    PROPS & EMITS
  =====================================================================*/
  const emit = defineEmits(['cancel', 'create']);

  /*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
  const groupName = ref('');
  const groupDesc = ref('');

  /*=====================================================================
    FUNCTIONS
  =====================================================================*/
  const createGroup = () => {
    if (groupName.value.trim()) {
      emit('create', {groupName: groupName.value.trim(), groupDesc: groupDesc.value.trim()});
    }
  };
</script>

<style scoped lang="scss">
  @import "@/themes/generated/variables.additional.scss";
  .container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    flex: 1 1 auto;
    overflow-y: auto;
    letter-spacing: 0.3px;
    border-radius: 8px;
    padding: 10px;
    height: unset;
    max-width: unset !important;
    width: unset;
  }

  .row {
    display: flex;
    gap: 20px;
    flex-shrink: 0; 
    align-items: end;
  }

  /* DX Controls Override  */
  .dx-texteditor {
    border: 0px;
    min-width: 120px;
    
    border-radius: 5px;
    background-color: $base-bg-dark;
  }
  
  .dx-texteditor.dx-editor-filled.dx-state-disabled {
    background-color: $base-bg-dark;
    opacity: 1;
  }
  
  .dx-texteditor::before {
    content: none;
  }
  
  .dx-texteditor::after {
    content: none;
  }
  
  .dx-button {
    border-radius: 5px;
    box-shadow: none;
    height: 31px;
    margin-bottom: 1px;
    color: $base-text-color; //rgba($base-text-color, alpha($base-text-color)  * 0.85);
  }

  .custom-button.dx-button-has-icon {
    min-width: unset !important;
  }

  ::v-deep(.custom-button.dx-button-has-icon .dx-icon) {
    font-size: 8px !important;
    width: unset;
    height: unset;
  }
</style>
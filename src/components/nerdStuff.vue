<template>
  <div class="device-info-container">
    
    <div class="info-section">
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Browser:</div>
          <div class="info-value">{{ browserInfo.name }} {{ browserInfo.version }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">User Agent:</div>
          <div class="info-value">{{ navigatorInfo.userAgent }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">Cookies Enabled:</div>
          <div class="info-value">{{ navigatorInfo.cookieEnabled ? 'Yes' : 'No' }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">Language:</div>
          <div class="info-value">{{ navigatorInfo.language }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">Online Status:</div>
          <div class="info-value">{{ navigatorInfo.onLine ? 'Online' : 'Offline' }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">Do Not Track:</div>
          <div class="info-value">{{ navigatorInfo.doNotTrack ? 'Enabled' : 'Disabled' }}</div>
        </div>
      </div>
    </div>

    <div class="info-section">
      <div class="info-grid">
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">Platform:</div>
          <div class="info-value">{{ navigatorInfo.platform }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Operating System:</div>
          <div class="info-value">{{ osInfo }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Device Type:</div>
          <div class="info-value">{{ deviceType }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">Touch Screen:</div>
          <div class="info-value">{{ navigatorInfo.maxTouchPoints > 0 ? 'Yes' : 'No' }}</div>
        </div>
        <div class="info-item" v-if="navigatorInfo">
          <div class="info-label">CPU Cores:</div>
          <div class="info-value">{{ navigatorInfo.hardwareConcurrency || 'Unknown' }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Memory:</div>
          <div class="info-value">{{ deviceMemory }}</div>
        </div>
      </div>
    </div>

    <div class="info-section">
      <div class="info-grid">
        <div class="info-item">
          <div class="info-label">Screen Resolution:</div>
          <div class="info-value">{{ screenWidth }} x {{ screenHeight }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Window Size:</div>
          <div class="info-value">{{ windowWidth }} x {{ windowHeight }}</div>
        </div>
        <div class="info-item" v-if="screenInfo">
          <div class="info-label">Color Depth:</div>
          <div class="info-value">{{ screenInfo.colorDepth }} bits</div>
        </div>
        <div class="info-item" >
          <div class="info-label">Pixel Ratio:</div>
          <div class="info-value">{{ pixelRatio }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">Orientation:</div>
          <div class="info-value">{{ orientation }}</div>
        </div>
        <div class="info-item" v-if="viewportInfo">
          <div class="info-label">Viewport:</div>
          <div class="info-value">{{ viewportInfo.width }} x {{ viewportInfo.height }}</div>
        </div>
        
      </div>
    </div>

    <div class="info-section">
      <div class="info-grid">
        <div class="info-item" v-for="(value, key) in networkInfo" :key="key">
          <div class="info-label">{{ formatLabel(key) }}:</div>
          <div class="info-value">{{ value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';

// Safe refs for browser APIs
const screenInfo = ref(null);
const viewportInfo = ref(null);
const pixelRatio = ref(null);

// Reactive state
const windowWidth = ref(0);
const windowHeight = ref(0);
const screenWidth = ref(0);
const screenHeight = ref(0);
const orientation = ref('');
const deviceMemory = ref('Unknown');
const networkInfo = ref({});
const featureSupport = ref({});
const browserInfo = ref({
  name: '',
  version: ''
});

// Computed properties
const navigatorInfo = ref(null);

const deviceType = computed(() => {
  if (!navigatorInfo.value) return 'Unknown';
  
  const userAgent = navigatorInfo.value.userAgent;
  if (/Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
    if (/iPad|tablet|Tablet/i.test(userAgent)) {
      return 'Tablet';
    }
    return 'Mobile';
  }
  return 'Desktop';
});

const osInfo = computed(() => {
  if (!navigatorInfo.value) return 'Unknown';
  
  const userAgent = navigatorInfo.value.userAgent;
  let osName = 'Unknown';
  let osVersion = '';

  if (/Windows NT/.test(userAgent)) {
    osName = 'Windows';
    if (/Windows NT 10.0/.test(userAgent)) osVersion = '10';
    else if (/Windows NT 6.3/.test(userAgent)) osVersion = '8.1';
    else if (/Windows NT 6.2/.test(userAgent)) osVersion = '8';
    else if (/Windows NT 6.1/.test(userAgent)) osVersion = '7';
    else if (/Windows NT 6.0/.test(userAgent)) osVersion = 'Vista';
    else if (/Windows NT 5.1/.test(userAgent)) osVersion = 'XP';
  } else if (/Mac OS X/.test(userAgent)) {
    osName = 'macOS';
    const macVersion = userAgent.match(/Mac OS X (\d+[._]\d+[._]?\d*)/);
    if (macVersion) {
      osVersion = macVersion[1].replace(/_/g, '.');
    }
  } else if (/Android/.test(userAgent)) {
    osName = 'Android';
    const androidVersion = userAgent.match(/Android (\d+(\.\d+)+)/);
    if (androidVersion) {
      osVersion = androidVersion[1];
    }
  } else if (/iOS|iPhone|iPad|iPod/.test(userAgent)) {
    osName = 'iOS';
    const iosVersion = userAgent.match(/OS (\d+[._]\d+[._]?\d*)/);
    if (iosVersion) {
      osVersion = iosVersion[1].replace(/_/g, '.');
    }
  } else if (/Linux/.test(userAgent)) {
    osName = 'Linux';
  }

  return osVersion ? `${osName} ${osVersion}` : osName;
});

// Methods
const updateWindowDimensions = () => {
  if (typeof window === 'undefined') return;
  
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
  
  if (typeof screen !== 'undefined') {
    screenWidth.value = screen.width;
    screenHeight.value = screen.height;
    
    // Update screen info
    screenInfo.value = {
      width: screen.width,
      height: screen.height,
      colorDepth: screen.colorDepth
    };
  }
  
  // Update pixel ratio
  pixelRatio.value = window.devicePixelRatio ? window.devicePixelRatio.toFixed(2) : '1.00';
  
  // Update viewport info
  if (document && document.documentElement) {
    viewportInfo.value = {
      width: document.documentElement.clientWidth,
      height: document.documentElement.clientHeight
    };
  }
  
  // Check orientation
  if (window.screen && window.screen.orientation) {
    orientation.value = window.screen.orientation.type;
  } else if (window.orientation !== undefined) {
    // Legacy orientation API
    const angle = window.orientation;
    if (angle === 0 || angle === 180) {
      orientation.value = 'portrait';
    } else {
      orientation.value = 'landscape';
    }
  } else {
    orientation.value = windowWidth.value > windowHeight.value ? 'landscape' : 'portrait';
  }
};

const detectBrowser = () => {
  if (!navigatorInfo.value) return;
  
  const userAgent = navigatorInfo.value.userAgent;
  
  // Check for Chrome
  if (/Chrome/.test(userAgent) && !/Chromium|Edge|Edg|OPR|Opera|Brave|Vivaldi/.test(userAgent)) {
    browserInfo.value.name = 'Chrome';
    const chromeVersion = userAgent.match(/Chrome\/(\d+\.\d+\.\d+\.\d+)/);
    browserInfo.value.version = chromeVersion ? chromeVersion[1] : '';
  }
  // Check for Firefox
  else if (/Firefox/.test(userAgent)) {
    browserInfo.value.name = 'Firefox';
    const firefoxVersion = userAgent.match(/Firefox\/(\d+\.\d+)/);
    browserInfo.value.version = firefoxVersion ? firefoxVersion[1] : '';
  }
  // Check for Edge
  else if (/Edg/.test(userAgent)) {
    browserInfo.value.name = 'Edge';
    const edgeVersion = userAgent.match(/Edg\/(\d+\.\d+\.\d+\.\d+)/);
    browserInfo.value.version = edgeVersion ? edgeVersion[1] : '';
  }
  // Check for Safari
  else if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
    browserInfo.value.name = 'Safari';
    const safariVersion = userAgent.match(/Version\/(\d+\.\d+\.\d+)/);
    browserInfo.value.version = safariVersion ? safariVersion[1] : '';
  }
  // Check for Opera
  else if (/OPR|Opera/.test(userAgent)) {
    browserInfo.value.name = 'Opera';
    const operaVersion = userAgent.match(/(?:OPR|Opera)\/(\d+\.\d+\.\d+)/);
    browserInfo.value.version = operaVersion ? operaVersion[1] : '';
  }
  // Other browsers
  else {
    browserInfo.value.name = 'Unknown';
    browserInfo.value.version = '';
  }
};

const checkNetworkInfo = () => {
  if (!navigatorInfo.value) return;
  
  if (typeof navigator !== 'undefined' && navigator.connection) {
    const conn = navigator.connection;
    networkInfo.value = {
      effectiveType: conn.effectiveType || 'Unknown',
      downlink: conn.downlink ? `${conn.downlink} Mbps` : 'Unknown',
      rtt: conn.rtt ? `${conn.rtt} ms` : 'Unknown',
      saveData: conn.saveData ? 'Enabled' : 'Disabled'
    };
  } else {
    networkInfo.value = {
      status: navigatorInfo.value.onLine ? 'Online' : 'Offline'
    };
  }
};

const checkFeatureSupport = () => {
  featureSupport.value = {
    'WebGL': !!window.WebGLRenderingContext,
    'Canvas': !!window.CanvasRenderingContext2D,
    'WebRTC': !!window.RTCPeerConnection,
    'Geolocation': !!navigator.geolocation,
    'Web Workers': !!window.Worker,
    'Service Workers': 'serviceWorker' in navigator,
    'Local Storage': !!window.localStorage,
    'Session Storage': !!window.sessionStorage,
    'IndexedDB': !!window.indexedDB,
    'WebSockets': !!window.WebSocket,
    'Notifications': 'Notification' in window,
    'Push API': 'PushManager' in window,
    'Battery API': 'getBattery' in navigator,
    'Web Share': 'share' in navigator,
  };
};

const formatLabel = (str) => {
  return str
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, function(str) { return str.toUpperCase(); });
};

// Lifecycle hooks
onMounted(() => {
  // Check device memory
  if (navigator.deviceMemory) {
    deviceMemory.value = `${navigator.deviceMemory} GB`;
  }
  
  // Initialize navigator info safely
  if (typeof window !== 'undefined' && window.navigator) {
    navigatorInfo.value = {
      userAgent: navigator.userAgent,
      cookieEnabled: navigator.cookieEnabled,
      language: navigator.language,
      onLine: navigator.onLine,
      doNotTrack: navigator.doNotTrack,
      platform: navigator.platform,
      maxTouchPoints: navigator.maxTouchPoints,
      hardwareConcurrency: navigator.hardwareConcurrency
    };
  }

  // Update dimensions
  updateWindowDimensions();
  window.addEventListener('resize', updateWindowDimensions);
  
  // Detect browser
  detectBrowser();
  
  // Check network info
  checkNetworkInfo();
  
  // Check feature support
  checkFeatureSupport();
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', updateWindowDimensions);
});
</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";
.device-info-container {

  margin: 0 auto;
  padding: 10px;
  color: rgba($base-text-color, 0.65);
}
.info-section {
  margin-bottom: 10px;
  overflow: hidden;
}


.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 0px;

}

.info-item {
  display: flex;
  padding: 0px;
  border-radius: 4px;

}

.info-label {
  font-weight: bold;
  margin-right: 10px;
}

.info-value {
  flex: 1;
  word-break: break-word;
  color: $base-accent;
}
</style>
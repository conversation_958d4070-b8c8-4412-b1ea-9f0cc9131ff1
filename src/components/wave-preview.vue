<template>
	<div class="container">
		<!-- <div class="accent">Temporary Wave ID: {{ data.tmp_wave_id }}</div> -->
		<div class="row">
			<div class="card">
				<div class="card-title">Shipments</div>
				<div style="overflow-y: auto;">{{ data.shipment_count }}</div>
			</div>
			<div class="card">
				<div class="card-title">Orders</div>
				<div style="overflow-y: auto;">{{ data.order_count }}</div>
			</div>
			<div class="card">
				<div class="card-title">Lines</div>
				<div style="overflow-y: auto;">{{ data.line_count }}</div>
			</div>
			<div class="card">
				<div class="card-title">Qty</div>
				<div style="overflow-y: auto;">
					<table>
						<!-- <thead><tr><th>WMS</th><th>Actual</th><th>Accuracy</th></tr></thead> -->
						<tbody>
							<tr>
								<td>WMS: {{ data.wms_qty_count }}</td>
								<td>Actual:{{ data.actual_qty_count }}</td>	
								<!-- <td>{{ ((Math.min(data.actual_qty_count, data.wms_qty_count) / Math.max(data.actual_qty_count, data.wms_qty_count)) * 100).toFixed(2)}}%</td> -->
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<!-- <div class="card">
				<div class="card-title">Actual Qty</div>
				<div style="overflow-y: auto;">{{ data.actual_qty_count }}</div>
			</div> -->
			<div v-if="data.bin_count > 0" class="card">
				<div class="card-title">Bins</div>
				<div style="overflow-y: auto;">{{ data.bin_count }}</div>
			</div>
			<div class="card">
				<div class="card-title">Pick Tasks</div>
				<div style="overflow-y: auto;">{{ data?.pick_task_list?.length }}</div>
			</div>
			<div class="card">
				<div class="card-title">Replen Tasks</div>
				<div style="overflow-y: auto;">{{ data?.replen_task_list?.length }}</div>
			</div>
		</div>

		<DxTabPanel :swipe-enabled="false" :style="`height: calc(100vh - 450px)`">
			<DxItem title="Inventory">
				<div style="overflow-y: auto; height: 100%;">
					<DxDataGrid :data-source="data.item_counts" 
						:row-alternation-enabled="true" 
						:show-borders="false" 
						:word-wrap-enabled="true" 
						:column-hiding-enabled="false" 
						:column-auto-width="true" 
						@rowRemoving="onItemRemoving">

						<DxColumn data-field="item_number" caption="Item Number" />
						<DxColumn data-field="wms_count" caption="WMS Qty" />
						<DxColumn data-field="actual_count" caption="Actual Qty" />

						<DxColumn width="200" />

						<DxHeaderFilter :visible="true"/>
						<DxFilterPanel :visible="false"/>
						<DxSearchPanel :visible="true" :highlight-case-sensitive="true" :width="`${isMobile ? '100%' : '300'}`"/>

						<DxEditing :mode="'popup'" :allow-updating="false" :allow-adding="false" :allow-deleting="true"></DxEditing>
					</DxDataGrid>
				</div>
			</DxItem>

			<DxItem title="Pick Tasks">

				<div style="overflow-y: auto;">
					<DxDataGrid :data-source="data.pick_task_list" 
						:row-alternation-enabled="true" 
						:show-borders="false" 
						:word-wrap-enabled="true" 
						:column-hiding-enabled="false" 
						:column-auto-width="true" >

						<DxColumn data-field="item_number" caption="Item Number" />
						<DxColumn data-field="slotted_location" caption="Location" />
						<DxColumn data-field="shipment_id" caption="ID" />
						<DxColumn data-field="quantity" caption="Qty" />

						<DxHeaderFilter :visible="true"/>
					</DxDataGrid>
				</div>

			</DxItem>

			<DxItem title="Replen Tasks">
				<div style="overflow-y: auto;">
					<DxDataGrid :data-source="data.replen_task_list" 
						:row-alternation-enabled="true" 
						:show-borders="false" 
						:word-wrap-enabled="true" 
						:column-hiding-enabled="false" 
						:column-auto-width="true" >

						<DxColumn data-field="item_number" caption="Item Number" />
						<DxColumn data-field="reserve_slotted_location" caption="Reserve Location" />
						<DxColumn data-field="primary_slotted_location" caption="Primary Location" />
						<DxColumn data-field="quantity" caption="Qty" />

						<DxHeaderFilter :visible="true"/>
					</DxDataGrid>
				</div>
			</DxItem>

			<DxItem v-if="data.putwall_data" title="Putwall Data">

				<DxTabPanel :swipe-enabled="false" >
					<DxItem v-for="putwall in data.putwall_data" :key="putwall.putwall_area" :title="putwall.putwall_area" >
						<div class="container" style="overflow-y: auto; ">
							<div class="row" style="border-bottom: 1px solid #50515A;">
								<div class="card no-border">
									<div>Shipments</div>
									<div class="accent" style="overflow-y: auto;">{{ putwall.shipment_count}}</div>
								</div>
								<div class="card no-border">
									<div>Orders</div>
									<div class="accent" style="overflow-y: auto;">{{ putwall.order_count}}</div>
								</div>
								<div class="card no-border">
									<div>Lines</div>
									<div class="accent" style="overflow-y: auto;">{{ putwall.line_count}}</div>
								</div>
								<div class="card no-border">
									<div>Qty</div>
									<div class="accent" style="overflow-y: auto;">{{ putwall.qty_count}}</div>
								</div>
								<div class="card no-border">
									<div>Bins</div>
									<div class="accent" style="overflow-y: auto;">{{ putwall.bin_count}}</div>
								</div>
							</div>
							<div class="row">
								<DxDataGrid :data-source="putwall.putwall_shipments" 
									:row-alternation-enabled="true" 
									:show-borders="false" 
									:word-wrap-enabled="true" 
									:column-hiding-enabled="false" 
									:column-auto-width="true"
									@row-click="onRowClick" >
								
									<!-- <DxColumn v-for="column in (columns || [])" :key="column.field_name" :data-field="column.field_name" :caption="column.caption"/> -->
									<DxColumn data-field="shipment_id" caption="Shipment ID" />
									<DxColumn data-field="priority" caption="priority" alignment="left" />

									<DxColumn data-field="carrier" caption="Carrier" />
									<DxColumn data-field="carrier_class_of_service" caption="Carrier Class" />
									<DxColumn data-field="country" caption="Country" />
									<DxColumn data-field="wms_source" caption="WMS Source" />
									<DxColumn data-field="line_count" caption="Lines" />
									<DxColumn data-field="qty_count" caption="Qty" />
									<DxColumn data-field="shipment_type" caption="Type" />
									<DxColumn data-field="order_count" caption="Orders" />
									<DxColumn data-field="bins_used" caption="Bins" />


									<DxSelection mode="single"/>
									<DxHeaderFilter :visible="true"/>
									<DxFilterPanel :visible="false"/>
									<DxSearchPanel :visible="false" :highlight-case-sensitive="true" :width="`${isMobile ? '100%' : '300'}`"/>
									<DxMasterDetail :enabled="true" template="detailTemplate" />
								

									<!-- Wave Detail Template -->
									<template #detailTemplate="{ data: orders }">
										<div class="detail-view">
											<table class="table">
												<thead>
													<tr>
														<th>WMS Order Number</th><th>Bins</th>
													</tr>
												</thead>
												<tbody>
													<tr v-for="order in orders.data.shipment_orders" :key="order.wms_order_number">
														<td>{{ order.wms_order_number }}</td>
														<td>{{ order.bin_count }}</td>
													</tr>
												</tbody>
											</table>
										</div>
									</template>
								</DxDataGrid>

								<!-- <DxDataGrid :data-source="putwall.putwall_shipments" 
									:row-alternation-enabled="true" 
									:show-borders="false" 
									:word-wrap-enabled="true" 
									:column-hiding-enabled="false" 
									:column-auto-width="true"
									class="grid-border">

									<DxColumn data-field="shipment_id" caption="ID" />
									<DxColumn data-field="priority" caption="priority" />

									<DxColumn data-field="carrier" caption="carrier" />
									<DxColumn data-field="carrier_class_of_service" caption="carrier_class_of_service" />
									<DxColumn data-field="country" caption="country" />
									<DxColumn data-field="wms_source" caption="wms_source" />
									<DxColumn data-field="line_count" caption="line_count" />
									<DxColumn data-field="qty_count" caption="qty_count" />
									<DxColumn data-field="shipment_type" caption="shipment_type" />
									<DxColumn data-field="order_count" caption="order_count" />
									<DxColumn data-field="bins_used" caption="bins_used" />

									

									<DxHeaderFilter :visible="true"/>
									<DxFilterPanel :visible="false"/>
									<DxSearchPanel :visible="true" :highlight-case-sensitive="true" :width="`${isMobile ? '100%' : '300'}`"/>

									<DxEditing :mode="'popup'" :allow-updating="true" :allow-adding="true" :allow-deleting="true">
										<DxPopup :title="'Edit Details'" :show-title="false" :width="700" :height="500" />
										<DxForm>
											<DxItem data-field="shipment_orders" />
											
										</DxForm>
									</DxEditing>
								</DxDataGrid> -->
							</div>
						</div>
					</DxItem>
				</DxTabPanel>

				
			</DxItem>
		</DxTabPanel>
		

	
  </div>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
	import { ref, computed, onMounted, watch, defineProps } from 'vue';
	import { DxTabPanel, DxItem } from 'devextreme-vue/tab-panel';
	import { DxDataGrid, DxColumn, DxMasterDetail, DxSelection, DxFilterPanel, DxHeaderFilter, DxSearchPanel, DxEditing, DxPopup, DxForm } from 'devextreme-vue/data-grid';
	import { DxButton } from 'devextreme-vue/button';
	import notify from 'devextreme/ui/notify';

	/*=====================================================================
    PROPS
 	=====================================================================*/
	const props = defineProps({
		data: {
			type: Array,
			required: true
		},
		config:{
			type: Object,
			required: true
		},
	});

	/*=====================================================================
    EMITS
  =====================================================================*/
	const emit = defineEmits(['item-removed'])

	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)
	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const onRowClick = (e) => {
		if (e.rowType === 'data') {
			e.component.collapseAll(-1);
			if (e.isExpanded) { e.component.collapseRow(e.key); } else { e.component.expandRow(e.key); }
		}
	}
	const onItemRemoved = (e) => {
		// removedItems.value.push(e.data)
		emit('item-removed', e.data)
	}

	const onItemRemoving = (e) => {
		const itemNumber = e.data.item_number;
		const isInContains = props.config.contains_item_list?.includes(itemNumber);

		if (isInContains) {
			notify({ ...notifyOptions.value, message: 'Item is in the include list. Please remove it before deleting.', type: 'error', displayTime: 5000 });
			e.cancel = true; // <-- Cancels the delete
			return;
		}

		emit('item-removed', e.data);
	};
</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

	.container {
		display: flex;
		flex-direction: column;
		gap: 10px;

	}


.row {
		display: flex;
		gap: 10px;
		flex-wrap: wrap;
		justify-content:stretch;
		justify-items: stretch;
	}

	.stat {
		display: flex;
		flex: auto;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10px;
		border-radius: 8px;
		border: 1px solid $base-border-color;
		background-color: rgba($base-bg-dark, 0.5);
		min-width: 120px;
	}

	/* Card styles */
	.card {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 10px;
		flex: auto;
		min-height: 0;
		max-height: 100%;
		display: flex;
		flex-direction: column;

		&.no-border {
			border: none;
			background-color: transparent;
		}
	}

	.card-wrap{
		// padding: 10px;
		min-height: 0;
		width: auto;
    height: auto;
    display: flex;
    flex-basis: 0px;
    flex-grow: 1;
    flex-direction: column;
	}

	.card-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}

		
	.accent {
		color: $base-accent;
	}


	table {
		width: 100%;
		border-collapse: collapse;
		border: none;
		color: rgba($base-text-color, .65)!important;

		th, td {
			border: none;
			padding: 3px;
			text-align: left;
		}
	}

	// DX Tab Panel Overrides
	::v-deep(.dx-tab) {
		// border: 1px solid $base-border-color;
		border-bottom: 0;
		padding: 8px 25px;
		border-radius: 8px 8px 0px 0px;
		border:1px solid #515159;
		border-bottom: 0;
		// margin-top: 5px;
		margin-right: 5px;
	}
	::v-deep(.dx-tabs) {
		max-width: 200px;
		border-radius: 8px 8px 0px 0px;
		padding-top: 10px;
		padding-left: 10px;
		padding-right: 10px;
		background-color: unset;

	}
	::v-deep(.dx-tab-text) {
		font-weight: 400;
	}

	::v-deep(.dx-tab.dx-tab-selected) {
		background-color: rgba($base-bg-dark, .65);
		// border: 1px solid $base-border-color;

		border:1px solid #515159;
		border-bottom: 0;
		border-radius: 8px 8px 0px 0px;
		// margin-top: 0px;
	}
	::v-deep(.dx-tab.dx-state-focused) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-hover) {
		background-color: rgba($base-bg-dark, .65)!important;
	}
	::v-deep(.dx-tab.dx-state-active) {
		background-color: rgba($base-bg-dark, .65)!important;
	}

	.dx-tabpanel {

		display: block;
		overflow: auto;
		// border: 1px solid #515159;
		border-radius: 8px;
		overflow: visible;
	}
	::v-deep(.dx-tabpanel-tabs) {

		overflow-x: auto;
		height: auto;
		display: block;
	}
	::v-deep(.dx-tabs-wrapper ){
		position: relative;
		display: flex;
		// border: 1px solid $base-border-color;
		// border-right: 2px solid $base-border-color;
		border-bottom: 0;
		border-radius: 8px 8px 0px 0px;
		// overflow: scroll;
		
	
	}
	::v-deep(.dx-tabpanel-container>.dx-tabpanel-container ) {
		height: 100%!important;
		
	}
	::v-deep(.dx-multiview-wrapper) {
		padding: 0px;
		background-color: #2f2e38;
		border: 1px solid #515159;
		border-radius: 8px 8px 0px 0px;
	
	}

	::v-deep .dx-tabpanel-container {


	}

	// DX Data Grid Overrides
	::v-deep .dx-datagrid {
		color: rgba($base-text-color, 0.75)!important;
		overflow: hidden!important;
	}


	.detail-view {
		padding: 15px;
	}


	.grid-border {
		border-radius: 8px;
		border: 1px solid $base-border-color;
		overflow: hidden;
		overflow: hidden!important;
	}

	::v-deep .dx-header-row {
		background-color: rgba($base-bg-dark, .65);
		border-radius: 8px;
	}

	::v-deep .dx-empty-message {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}

	::v-deep .dx-datagrid-rowsview .dx-row {
		border: none;
	}
	::v-deep .dx-data-row {
		cursor: pointer;
	}
	
	::v-deep .dx-datagrid-rowsview .dx-master-detail-row > .dx-master-detail-cell {
		padding: 0!important;
		
	}

	::v-deep .dx-datagrid-header-panel {
		padding: 10px!important;
	}

	::v-deep .dx-datagrid-search-panel {
		margin: 0!important;
	}

	::v-deep .dx-checkbox-container {
		color: rgba($base-text-color, .65)!important;
	}

	::v-deep .dx-texteditor {
		min-width: 370px!important;
		width: 100%;
		max-width: 370px!important;
	}
	
	::v-deep .dx-popup-content-scrollable {
		overflow-x: hidden!important;
	}
</style>
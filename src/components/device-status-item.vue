<!-- SiteStatus.vue -->
<template>
  <div class="site-status">
    <div class="main-content">
      <div class="header">
        <h2 class="accent">{{ heartbeatName }}</h2>
        <div class="right-section">
          <div class="status-indicator" :style="statusColor"></div>
        </div>
      </div>
      
      <div class="info-container">
				<div class="info-row">
				<!-- <span class="label">Value</span> -->
					<span class="value">{{ heartbeatValue }}</span>
				</div>
        <div class="info-row">
          <span class="label">Heartbeat:</span>
          <span class="value">{{ getRelativeTime(heartbeatDatetime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps } from 'vue'

// Props definition
const props = defineProps({
  alive: {
    type: Boolean,
    required: true
  },
  heartbeatDatetime: {
    type: String,
    required: true
  },
  heartbeatFlag: {
    type: Boolean,
    required: true
  },
  heartbeatName: {
    type: String,
    required: true
  },
  heartbeatSource: {
    type: String,
    required: true
  },
  heartbeatValue: {
    type: [String, Number],
    required: true
  }
})


// Computed properties
const statusColor = computed(() => ({
  backgroundColor: props.alive ? '#4CAF50' : '#FF5252'
}))

const formattedDateTime = computed(() => {
  try {
    const date = new Date(props.heartbeatDatetime)
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date)
  } catch (error) {
    return props.heartbeatDatetime
  }
})

const getRelativeTime = (dateString) => {
  const now = new Date()
  const past = new Date(dateString)
  const diffInMs = now - past
  
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60))
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60))
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes} ${diffInMinutes === 1 ? 'minute' : 'minutes'} ago`
  } else if (diffInHours < 24) {
    return `${diffInHours} ${diffInHours === 1 ? 'hour' : 'hours'} ago`
  } else {
    return `${diffInDays} ${diffInDays === 1 ? 'day' : 'days'} ago`
  }
}


</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.site-status {
	
		color: rgba($base-text-color, 0.65);
		padding: 0 15px 0px 15px;
		// border-bottom: 1px solid red;
		// margin: 10px 0px;
	}

	.main-content {
	
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 5px;
	}

	.header h2 {
		font-size: 14px;
		font-weight: 400;
		margin: 0;
	}

	.status-indicator {
		width: 16px;
		height: 16px;
		border-radius: 50%;
		transition: background-color 0.3s ease;
	}

	.info-container {
		display: flex;
		flex-direction: column;
		gap: 5px;
	}

	.accent {
		color: $base-accent;
	}
	.info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;

	}
</style>
<template>
	<div>
		<!-- Stats -->
		<div class="row" :style="{ flexDirection: direction , height: rowHeight , flexWrap: flexWrap }">
			<!-- Priority Counts -->
			<div class="card" :style="{ height: cardHeight }">
				<div class="card-title">Priority Counts</div>
				<div style="overflow-y: auto;">
					<table>
						<!-- <thead><tr><th>Priority</th><th>Count</th></tr></thead> -->
						<tbody>
							<tr v-for="(item, index) in orderStatsData.priority" :key="index">
								<td>{{ item.priority }}</td>
								<td>{{ item.count }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Carrier Counts -->
			<div class="card" :style="{ height: cardHeight }">
				<div class="card-title">Carrier Counts</div>
				<div style="overflow-y: auto;">
					<table>
						<!-- <thead><tr><th>Carrier</th><th>Class</th><th>Count</th></tr></thead> -->
						<tbody>
							<tr v-for="(item, index) in orderStatsData.carrier" :key="index">
								<td>{{ item.ship_carrier }}</td>
								<td>{{ item.class_of_service }}</td>
								<td>{{ item.count }}</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<!-- Shipment Type Counts -->
			<div class="card" :style="{ height: cardHeight }">
				<div class="card-title">Shipment Type Counts</div>
				<div style="overflow-y: auto;">
					<table>
						<!-- <thead><tr><th>Type</th><th>Count</th></tr></thead> -->
						<tbody>
							<tr v-for="(item, index) in orderStatsData.shipment_type" :key="index">
								<td>{{ item.shipment_type }}</td>
								<td @click="fetchDetailData(item.shipment_type)">
									<span :class="{ 'accent underline': orderStatsData[item.shipment_type] }" >{{ item.count }}</span>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
		</div>
  </div>

	<DxPopup
			class="dx-popup"
			:visible="popupShipmentTypeDetailVisible"
			:title="popupTitle"
			:show-title="true"
			:drag-enabled="false"
			:hide-on-outside-click="true"
			:show-close-button="true"
			:onHidden='onPopupHidden'
			:container="'.dx-viewport'"
			:height="isMobile? '100%': '50%'"
			:width="isMobile? '100%': '50%'">
			<DxPosition my="center" at="center" of=".dx-viewport" />
			<template #content>
				<div>
					<DxDataGrid :data-source="orderStatsData[shipmentTypeSelected]" 
						:row-alternation-enabled="true" 
						:show-borders="false" 
						:word-wrap-enabled="true" 
						:column-hiding-enabled="false" 
						:column-auto-width="true" 
						height="100%"
						class="grid-border">
							
						<DxColumn data-field="item_number" caption="Item Number" />
						<DxColumn data-field="shipment_count" caption="Shipment Count" />
						<DxColumn data-field="qty_count" caption="Qty" />

						<DxSearchPanel :visible="true" :highlight-case-sensitive="true" :width="`${isMobile ? '100%' : '300'}`"/>
						<DxHeaderFilter :visible="true"/>
					</DxDataGrid>
				</div>
			</template>
		</DxPopup>
</template>

<script setup>
	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
	import { ref, computed, onMounted, watch, defineProps } from 'vue';
	import { DxPopup, DxPosition } from 'devextreme-vue/popup';
	import { DxDataGrid, DxColumn, DxHeaderFilter, DxSearchPanel } from 'devextreme-vue/data-grid';

	// Composables
	import { useScreenSize } from '@/composables/useScreenSize';

	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)
  =====================================================================*/
	const { isMobile } = useScreenSize()
	
	/*=====================================================================
    PROPS
 	=====================================================================*/
	const props = defineProps({
		orderStatsData: {
			type: Object,
			required: true
		},
		cardHeight: {
			type: String,
			default: '180px'
		},
		rowHeight: {
			type: String,
			default: 'auto'
		},
		direction: {
			type: String,
			default: 'row'
		},
		flexWrap: {
			type: String,
			default: 'wrap'
		}
	});

	const popupShipmentTypeDetailVisible = ref(false);
	const shipmentTypeSelected = ref('');

	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const fetchDetailData = (shipmentType) => {

		if (!props.orderStatsData[shipmentType]) {
			return;
		}

		shipmentTypeSelected.value = shipmentType;
		popupShipmentTypeDetailVisible.value = true;
	};

	const onPopupHidden = async() => {
    popupShipmentTypeDetailVisible.value = false;
  };

</script>

<style scoped lang="scss">
	@import "@/themes/generated/variables.additional.scss";

.row {
		display: flex;
		gap: 10px;
		flex-wrap: wrap;
		justify-content:stretch;
		justify-items: stretch;
	}

	.stat {
		display: flex;
		flex: auto;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 10px;
		border-radius: 8px;
		border: 1px solid $base-border-color;
		background-color: rgba($base-bg-dark, 0.5);
		min-width: 120px;
	}

	.accent {
		color: $base-accent;
	}
	.underline {
		text-decoration: underline;
		cursor: pointer;
	}

	/* Card styles */
	.card {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 20px;
		flex: auto;
		min-height: 0;
		max-height: 100%;
		display: flex;
		flex-direction: column;
	}

	.card-wrap{
		// padding: 10px;
		min-height: 0;
		width: auto;
    height: auto;
    display: flex;
    flex-basis: 0px;
    flex-grow: 1;
    flex-direction: column;
	}

	.card-title {
		font-size: 16px;
		font-weight: 400;
		margin-bottom: 10px;
	}


	table {
		width: 100%;
		border-collapse: collapse;
		border: none;

		th, td {
			border: none;
			padding: 3px;
			text-align: left;
		}
	}

	// DX Data Grid Overrides
	::v-deep .dx-datagrid {
		color: rgba($base-text-color, 0.75)!important;
		overflow: hidden!important;
	}


	.detail-view {
		padding: 15px;
	}


	.grid-border {
		border-radius: 8px;
		border: 1px solid $base-border-color;
		overflow: hidden;
		overflow: hidden!important;
	}

	::v-deep .dx-header-row {
		background-color: rgba($base-bg-dark, .65);
		border-radius: 8px;
	}

	::v-deep .dx-empty-message {
		margin-top: 40px;
		font-size: 16px;
		color: rgba($base-text-color, 0.75)!important;

	}

	::v-deep .dx-datagrid-rowsview .dx-row {
		border: none;
	}
	::v-deep .dx-data-row {
		cursor: pointer;
	}
	
	::v-deep .dx-datagrid-rowsview .dx-master-detail-row > .dx-master-detail-cell {
		padding: 0!important;
		
	}

	::v-deep .dx-datagrid-header-panel {
		padding: 10px!important;
	}

	::v-deep .dx-datagrid-search-panel {
		margin: 0!important;
	}

	// DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;
		
	}
	
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}
	::v-deep .dx-texteditor {
		min-width: 370px!important;
		width: 100%;
		max-width: 370px!important;
	}
</style>
<!-- SourceGroup.vue -->
<template>
  <div class="source-group">
    <div class="main-content" @click="toggleExpand">
      
			<div class="header">
        <h2>{{ source }}</h2>
        <div class="right-section">
          <div class="status-indicator" :class="statusClass"></div>
          <div class="arrow" :class="{ 'arrow-expanded': isExpanded }">▼</div>
        </div>
      </div>
      
      <div class="info-container">
        <div class="info-row">
          <span class="label">Device status</span>
          <span class="value">{{ activeDevices }} / {{ devices.length }} Active</span>
        </div>
      </div>

    </div>

    <div class="expanded-content" :class="{ 'expanded': isExpanded }">
      <div v-for="(device) in devices" :key="device.heartbeat_name"  class="device-item">
				<div class="separator"></div>
        <deviceStatusItem
          :alive="device.alive"
          :heartbeat-datetime="device.heartbeat_datetime"
          :heartbeat-flag="device.heartbeat_flag"
          :heartbeat-name="device.heartbeat_name"
          :heartbeat-source="device.heartbeat_source"
          :heartbeat-value="device.heartbeat_value"
        />
				
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps } from 'vue'
import deviceStatusItem from './device-status-item.vue'

const props = defineProps({
  source: {
    type: String,
    required: true
  },
  devices: {
    type: Array,
    required: true
  }
})

const isExpanded = ref(false)

const activeDevices = computed(() => {
  return props.devices.filter(device => device.alive).length
})

const statusClass = computed(() => {
		return (activeDevices.value === props.devices.length )
		? 'status-all-active'  // All devices active
		: activeDevices.value === 0 
			? 'status-all-inactive'  // No devices active
			: 'status-partial-active'  // Some devices active
});



const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}
</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

.separator {
	width: 100%;
	height: 5px;
	background-color: $base-bg;
	margin: 15px 0;
}

.source-group {

  width: 100%;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	color: rgba($base-text-color, 0.65);
	background-color: rgba($base-bg-dark, 0.3);
  border: 1px solid $base-border-color;
}

.main-content {
  padding: 15px;
  cursor: pointer;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.right-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header h2 {
  font-size: 16px;
  font-weight: 400;
  margin: 0;
}

.status-indicator {
	width: 15px;
  height: 15px;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
	
}

.status-all-active {
  background: conic-gradient(
    from 0deg,
    #4CAF50 0deg 360deg
  );
  border-color: #4CAF50;
}

.status-partial-active {
  background: conic-gradient(
    from 0deg,
    transparent 0deg 180deg,
    #ffd700 180deg 360deg
  );
	outline: 1px dotted #ffd700;
	
}

.status-all-inactive {
  background: #FF5252;
}

.arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
  color: #666666;
}

.arrow-expanded {
  transform: rotate(180deg);
}

.info-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: #666666;
}

.value {
  font-weight: 500;
}

.expanded-content {
	height: 0;
  transition: height 0.3s ease-in-out;
	overflow: hidden;
	// transform: scale(0);
	// transform-origin: top;

}

.expanded {
  //max-height: 2000px;
	padding-bottom: 15px; 
	// transform: scale(1);
	height: auto;
	
}

.source-group:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
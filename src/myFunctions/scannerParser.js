// import databaseName from './databaseName';

export default{

  defaultScannerObjects(){
      return [
          { displayName: this.createScannerName('SCAN01'), lookupName: 'SCAN01', scanTime: '', filter: '', message: '' },
          { displayName: this.createScannerName('SCAN02'), lookupName: 'SCAN02', scanTime: '', filter: '', message: '' },
          { displayName: this.createScannerName('SCAN03'), lookupName: 'SCAN03', scanTime: '', filter: '', message: '' },
      ];
  },
formatScanners(data){
    let tmpJSON = this.defaultScannerObjects();
  //   let AmtOfScanners = databaseName.getAmtOfScanners();
  //   for(let c = 1; c <= AmtOfScanners; c++){
  //       let lookupName = "";
  //       if (c == 13 || c == 24 || c == 25 || c == 26 || c == 27 || c == 28 || c == 29 || c == 30 || c == 31 || c == 32 || c == 40) {
  //         lookupName = null;
  //       }
  //       else if(c > 9){
  //         lookupName = "SCANNERPOINT"+c;
  //         tmpJSON.push({displayName: this.createScannerName(lookupName), lookupName: lookupName, scanTime: "", filter: "", message: ""});
  //       }
  //       else{
  //         lookupName = "SCANNERPOINT0"+c;
  //         tmpJSON.push({displayName: this.createScannerName(lookupName), lookupName: lookupName, scanTime: "", filter: "", message: ""});
  //       }
  //     }

    data.forEach(obj=>{
        for(let c = 0; c < tmpJSON.length; c++){
            if(obj.auditCategory === tmpJSON[c].lookupName){
                tmpJSON[c].filter = obj.auditFilter;
                tmpJSON[c].message = obj.auditMessage;
                tmpJSON[c].scanTime = obj.auditDateTime;
                break;
            }
        }
    });

    return tmpJSON;
},
/*
createScannerName(scanner){
    switch(scanner){
        case "SCAN01":
            return "SCANNER01 - PANDA"
        case "SCAN02":
            return "SCANNER02 - Validation"
        default:
            return scanner;
    }
},
*/
formatHeartbeats(data, mobile){
    //console.log("i got to the sub function", data);
    //let now = new Date();
    //console.log("mainData=>",data);
    let tmpJSON = [];
    let tmpWMSJSON = [];
    let tmpServiceJSON = [];
    let tmpPLCJSON = [];
    let tmpPRINTJSON = [];
    let tmpPandaJSON = [];
    let tmpScaleJSON = [];
    let tmpEDIJSON = [];

    let finalJSON = [];
    data.forEach(obj=>{
        //console.log(obj.heartbeatDateTime);
        //console.log(Math.abs(Date.now - new Date(obj.heartbeatDateTime)) / 1000);
        
        //let diff = Math.abs(now - new Date(obj.heartbeatDateTime)) / 1000;
        let status = 'off';
        //console.log(diff);
        if(obj.alive == 0){
            status = 'off';
        } else{
            status = 'on';
        }
        let active = true;
        if (obj.heartbeat_flag == true) {
          active = 'active';
        } else {
          active = 'inactive';
        }
        //if(obj.heartbeatDateTime === ""){
        //    status = 'off';
        //}
        
        if(obj.heartbeat_source === 'Scanner'){
            tmpJSON.push({displayName: `${obj.heartbeat_name}[${obj.heartbeat_value}]`, status: status, active: active, group: 'SCANNERS'});
          }     
        // else if(obj.heartbeatSource === 'Dispatcher'){
        //     tmpWMSJSON.push({displayName: obj.heartbeatName, status: status, active: active, group: 'DISPATCHER'});
        // }
        else if(obj.heartbeat_source === 'PLC'){
            tmpPLCJSON.push({displayName: `${obj.heartbeat_name}[${obj.heartbeat_value}]`, status: status, active: active, group: 'PLCS'});
        }
        // else if (obj.heartbeatSource === 'Printer') {
        //   tmpPRINTJSON.push({displayName: obj.heartbeatName, status: status, active: active, group: 'PRINTERS'});
        // }
        else if (obj.heartbeat_source === 'Panda') {
            tmpPandaJSON.push({displayName: `${obj.heartbeat_name}[${obj.heartbeat_value}]`, status: status, active: active, group: 'PANDA'});
        } 
        else if (obj.heartbeat_source === 'Service') {
            tmpServiceJSON.push({displayName: `${obj.heartbeat_name}[${obj.heartbeat_value}]`, status: status, active: active, group: 'SERVICES'});
        } 
        else if (obj.heartbeat_source === 'Scale') {
            tmpScaleJSON.push({displayName: `${obj.heartbeat_name}[${obj.heartbeat_value}]`, status: status, active: active, group: 'SCALES'});
        } 
        else if (obj.heartbeat_source === 'EDI') {
            tmpEDIJSON.push({displayName: `${obj.heartbeat_name}[${obj.heartbeat_value}]`, status: status, active: active, group: 'EDI'});
        } 
        else {
          console.log('Error: Unable to Place Heartbeat in Category');
        }
        
        //tmpServiceJSON.push({displayName: obj.heartbeatName, status: status, group: obj.heartbeatSource});
    });

    if (mobile == true) {
      let zone = 1;
      let tmpOBJ = {};
      tmpJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });

      if(zone != 1){
          finalJSON.push(tmpOBJ);
      }
      tmpOBJ = {};

      zone = 1;

      tmpWMSJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });

      if(zone != 1){
          finalJSON.push(tmpOBJ);
      }

      tmpOBJ = {};

      zone = 1;

      tmpServiceJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });
      if(zone != 1){
          finalJSON.push(tmpOBJ);
      }

      tmpOBJ = {};

      zone = 1;

      tmpPLCJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });
      if(zone != 1){
          finalJSON.push(tmpOBJ);
      }

      tmpOBJ = {};

      zone = 1;

      tmpPRINTJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });
      if(zone != 1){
          finalJSON.push(tmpOBJ);
      }
      tmpOBJ = {};

      zone = 1;

      tmpPandaJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });
      if(zone != 1){
        finalJSON.push(tmpOBJ);
      }
      tmpOBJ = {};

      zone = 1;

      tmpScaleJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });
      if(zone != 1){
        finalJSON.push(tmpOBJ);
      }
      //start of source
      tmpOBJ = {};

      zone = 1;

      tmpEDIJSON.forEach(obj=>{
          tmpOBJ.group = obj.group;
          if(zone == 1 || zone == 2 || zone == 3){
              tmpOBJ.displayName1 = obj.displayName;
              tmpOBJ.status1 = obj.status;
              tmpOBJ.active1 = obj.active;

              finalJSON.push(tmpOBJ);
              zone = 0;
              tmpOBJ = {};
          }
          // if(zone == 2){
          //     tmpOBJ.displayName2 = obj.displayName;
          //     tmpOBJ.status2 = obj.status;
          // }
          // if(zone == 3){
          //     tmpOBJ.displayName3 = obj.displayName;
          //     tmpOBJ.status3 = obj.status;
          //     //tmpOBJ.group = obj.group;
          //     finalJSON.push(tmpOBJ);
          //     zone = 0;
          //     tmpOBJ = {};
          // }
          zone++;
      });
      if(zone != 1){
        finalJSON.push(tmpOBJ);
      }
      // end of source

      return finalJSON;
      //not mobile screen below
    } else {

    let zone = 1;
    let tmpOBJ = {};
    tmpJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });

    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }
    tmpOBJ = {};

    zone = 1;

    tmpWMSJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });

    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }

    tmpOBJ = {};

    zone = 1;

    tmpServiceJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });
    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }

    tmpOBJ = {};

    zone = 1;

    tmpPLCJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });
    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }

    tmpOBJ = {};

    zone = 1;

    tmpPRINTJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });
    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }
    tmpOBJ = {};

    zone = 1;

    tmpPandaJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });
    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }
    tmpOBJ = {};

    zone = 1;

    tmpScaleJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });
    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }
    tmpOBJ = {};

    zone = 1;

    tmpEDIJSON.forEach(obj=>{
        tmpOBJ.group = obj.group;
        if(zone == 1){
            tmpOBJ.displayName1 = obj.displayName;
            tmpOBJ.status1 = obj.status;
            tmpOBJ.active1 = obj.active;
        }
        if(zone == 2){
            tmpOBJ.displayName2 = obj.displayName;
            tmpOBJ.status2 = obj.status;
            tmpOBJ.active2 = obj.active;
        }
        if(zone == 3){
            tmpOBJ.displayName3 = obj.displayName;
            tmpOBJ.status3 = obj.status;
            tmpOBJ.active3 = obj.active;
            //tmpOBJ.group = obj.group;
            finalJSON.push(tmpOBJ);
            zone = 0;
            tmpOBJ = {};
        }
        zone++;
    });
    if(zone != 1){
        finalJSON.push(tmpOBJ);
    }


    return finalJSON;
  }
}
}
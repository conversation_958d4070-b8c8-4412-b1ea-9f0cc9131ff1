export default [
  {
    text: "Home",
    path: "/home",
    icon: "home"
  },
  {
    text: "Dashboard",
    path: "/dashboard",
    icon: "smalliconslayout"
  },

  {
    text: 'Operations',
    icon: 'optionsgear',
    items: [
      {
        text: 'Order Start',
        path: '/order-start',
      },
      {
        text: 'Picking',
        path: '/picking',
      },
      {
        text: 'Packing',
        path: '/pack-station',
      },
      {
        text: 'Shipping',
        path: '/shipping',
      },
      {
        text: 'Hospital Lane',
        path: '/jackpot-lane',
      },
      {
        text: 'Quality Check',
        path: '/sku-validate',
      },
      {
        text: 'Returns',
        path: '/returns',
      },
      {
        text: 'E-commerce',
        path: '/ecommerce',
      },
    ]
  },

  {
    text: 'EZ Hub',
    icon: 'mediumiconslayout',
    items: [
      {
        text: 'EZ View',
        path: '/ez-view',
      },
      {
        text: 'EZ Search',
        path: '/ez-search',
      },
      {
        text: 'EZ Trace',
        path: '/ez-trace',
      },
    ]
  },

  {
    text: 'Integrations',
    icon: 'unpin',
    items: [
      {
        text: 'Matthews LP',
        path: '/lp',
      },
      {
        text: 'Matthews Put Wall',
        path: '/pw',
      },
      {
        text: 'OPEX SureSort',
        path: '/ss',
      },
      {
        text: 'OPEX Perfect Pick',
        path: '/pp',
      },
    ]
  },

  {
    text: 'Active Monitoring',
    icon: 'square',
    items: [
      {
        text: 'Dashboard',
        path: '/am-dashboard',
      },
      {
        text: 'Device Monitor',
        path: '/device-monitor',
      },
      {
        text: 'Real-Time Monitor',
        path: '/real-time-monitor',
      },
      {
        text: 'Alarm Diagnostic',
        path: '/alarm-diagnostic',
      },
    ]
  },
  {
    text: 'Reports & Statistics',
    icon: 'textdocument',
    items: [
      {
        text: 'Scanner Stats',
        items:[
          {
            text: 'Active Scanner Stats',
            icon: '',
          },
          {
            text: 'Active by Day',
          },
          {
            text: 'Active by Day + Hr',
          },
        ]
      },
      {
        text: 'Device Group Stats',
        items:[
          {
            text: 'Active Group Stats',
          },
          {
            text: 'Group Stats by Day',
            icon: '',
          },
          {
            text: 'Group Stats by Day + Hr',
          },
        ]
      },
      {
        text: 'Reports',
        items:[
          {
            text: 'Denial List',
          },
          {
            text: 'LPN Status',
            icon: '',
          },
          {
            text: 'Order Status',
          },
        ]
      },

 
    ]
  },
  {
    text: 'KPIs',
    icon: 'chart',
    items: [
      {
        text: 'KPI #1',
      },
      {
        text: 'KPI #2',
      },
    ]
  },
  {
    text: 'Administrative',
    icon: 'preferences',
    items: [
      {
        text: 'User Management',
      },
      {
        text: 'Order Start Admin',
      },
      {
        text: 'Lane Mapping',
      },
      {
        text: 'Integration Testing',
      },
      {
        text: 'LPN Denial Request', 
      },
      {
        text: 'LPN Fraud Detection', 
      },
      {
        text: 'LPN Exception Request', 
      },
    ]
  },
  {
    text: 'Logs',
    icon: 'detailslayout',
    items: [
      {
        text: 'Audit Log',
      },
      {
        text: 'Error Log',
      },
    ]
  }
]
